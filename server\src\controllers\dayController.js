const calendarService = require('../services/calendarService');

const getAllDays = (req, res) => {
    try {
        const days = calendarService.getAllDays();
        res.json(days);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getDayByDate = (req, res) => {
    try {
        const { date } = req.params;
        const day = calendarService.getDayByDate(date);
        if (day) {
            res.json(day);
        } else {
            res.status(404).json({ message: 'Day not found' });
        }
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const createOrUpdateDay = (req, res) => {
    try {
        const { date, type, hours, description, shiftId } = req.body;
        if (!date || !type) {
            return res.status(400).json({ message: 'Date and type are required' });
        }
        const updatedDay = calendarService.createOrUpdateDay(date, type, hours, description, shiftId);
        res.status(200).json(updatedDay);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const applyPatternToDates = (req, res) => {
    try {
        const { startDate, endDate, pattern } = req.body;
        if (!startDate || !endDate || !pattern || !Array.isArray(pattern)) {
            return res.status(400).json({ message: 'Start date, end date, and a valid pattern array are required.' });
        }
        calendarService.applyPatternToDates(startDate, endDate, pattern);
        res.status(200).json({ message: 'Pattern applied successfully.' });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

module.exports = {
    getAllDays,
    getDayByDate,
    createOrUpdateDay,
    applyPatternToDates
};
