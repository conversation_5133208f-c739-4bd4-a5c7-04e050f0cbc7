import React, { useState } from 'react';
import { Container, Row, Col, Tabs, Tab } from 'react-bootstrap';
import RealCalendarView from '../components/RealCalendarView';
import PlannedCalendarView from '../components/PlannedCalendarView';
import CalendarAnalysis from '../components/CalendarAnalysis';

function CalendarPage() {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [activeTab, setActiveTab] = useState('real');
  const [calendarKey, setCalendarKey] = useState(0);

  const handleDateSelect = (date) => {
    setSelectedDate(date);
  };

  const handleCalendarUpdate = () => {
    // Forzar actualización de todos los calendarios
    setCalendarKey(prev => prev + 1);
  };

  return (
    <Container fluid>
      <Row className="mb-3">
        <Col>
          <h2>Sistema de Horarios</h2>
        </Col>
      </Row>

      <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-3">
        <Tab eventKey="real" title="📅 Calendario Real">
          <RealCalendarView
            key={`real-${calendarKey}`}
            onDateSelect={handleDateSelect}
            selectedDate={selectedDate}
            onCalendarUpdate={handleCalendarUpdate}
          />
        </Tab>
        <Tab eventKey="planned" title="📋 Calendario Teórico">
          <PlannedCalendarView
            key={`planned-${calendarKey}`}
            onDateSelect={handleDateSelect}
            selectedDate={selectedDate}
            onCalendarUpdate={handleCalendarUpdate}
          />
        </Tab>
        <Tab eventKey="analysis" title="📊 Análisis">
          <CalendarAnalysis
            key={`analysis-${calendarKey}`}
            selectedDate={selectedDate}
          />
        </Tab>
      </Tabs>
    </Container>
  );
}

export default CalendarPage;
