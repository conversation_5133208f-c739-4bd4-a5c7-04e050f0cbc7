import React, { useState, useEffect } from 'react';
import { Row, Col, Button, Alert, Form, Modal } from 'react-bootstrap';
import Calendar from 'react-calendar';
import { 
  getAllPlannedDays, 
  getAllPatterns, 
  applyPattern, 
  clearPlannedCalendar,
  deletePlannedDay
} from '../services/api';

function PlannedCalendarView({ onDateSelect, selectedDate, onCalendarUpdate }) {
  const [plannedDays, setPlannedDays] = useState([]);
  const [patterns, setPatterns] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  // Estados para aplicación de patrones
  const [showPatternModal, setShowPatternModal] = useState(false);
  const [selectedPatternId, setSelectedPatternId] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  useEffect(() => {
    loadPlannedDays();
    loadPatterns();
  }, []);

  const loadPlannedDays = async () => {
    try {
      setLoading(true);
      const days = await getAllPlannedDays();
      setPlannedDays(days);
      setError('');
    } catch (err) {
      console.error('Error loading planned days:', err);
      setError('Error cargando días planificados: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const loadPatterns = async () => {
    try {
      const patternsData = await getAllPatterns();
      setPatterns(patternsData);
    } catch (err) {
      console.error('Error loading patterns:', err);
    }
  };

  const handleApplyPattern = async () => {
    if (!selectedPatternId || !startDate || !endDate) {
      setError('Todos los campos son obligatorios');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      await applyPattern(selectedPatternId, startDate, endDate);
      await loadPlannedDays();
      onCalendarUpdate();
      setShowPatternModal(false);
      resetPatternForm();
    } catch (err) {
      setError('Error aplicando patrón: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleClearCalendar = async () => {
    if (window.confirm('¿Estás seguro de que quieres limpiar todo el calendario planificado?')) {
      try {
        setLoading(true);
        await clearPlannedCalendar();
        await loadPlannedDays();
        onCalendarUpdate();
        setError('');
      } catch (err) {
        setError('Error limpiando calendario: ' + err.message);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleDeleteDay = async (dayId) => {
    if (window.confirm('¿Eliminar este día del calendario planificado?')) {
      try {
        setLoading(true);
        await deletePlannedDay(dayId);
        await loadPlannedDays();
        onCalendarUpdate();
      } catch (err) {
        setError('Error eliminando día: ' + err.message);
      } finally {
        setLoading(false);
      }
    }
  };

  const resetPatternForm = () => {
    setSelectedPatternId('');
    setStartDate('');
    setEndDate('');
  };

  const getTileContent = ({ date, view }) => {
    if (view !== 'month') return null;
    
    const dayData = plannedDays.find(day => 
      new Date(day.date).toDateString() === date.toDateString()
    );
    
    if (dayData) {
      return (
        <div className="calendar-tile-content">
          <div className="badge bg-secondary">{dayData.hours}h</div>
          {dayData.shiftName && (
            <div className="shift-name">{dayData.shiftName}</div>
          )}
        </div>
      );
    }
    return null;
  };

  const getTileClassName = ({ date, view }) => {
    if (view !== 'month') return '';
    
    const dayData = plannedDays.find(day => 
      new Date(day.date).toDateString() === date.toDateString()
    );
    
    if (dayData) {
      return 'has-planned-work';
    }
    return '';
  };

  const getTotalHours = () => {
    return plannedDays.reduce((total, day) => total + (day.hours || 0), 0);
  };

  const getCurrentMonthHours = () => {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    return plannedDays
      .filter(day => {
        const dayDate = new Date(day.date);
        return dayDate.getMonth() === currentMonth && dayDate.getFullYear() === currentYear;
      })
      .reduce((total, day) => total + (day.hours || 0), 0);
  };

  const getSelectedDayData = () => {
    if (!selectedDate) return null;
    return plannedDays.find(day => 
      new Date(day.date).toDateString() === selectedDate.toDateString()
    );
  };

  return (
    <div>
      <Row className="mb-3">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <h4>Calendario Teórico (Planificado)</h4>
            <div>
              <Button
                variant="success"
                onClick={() => setShowPatternModal(true)}
                className="me-2"
              >
                📋 Aplicar Patrón
              </Button>
              <Button
                variant="outline-danger"
                onClick={handleClearCalendar}
                disabled={loading}
              >
                🗑️ Limpiar Todo
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {error && (
        <Row className="mb-3">
          <Col>
            <Alert variant="danger" onClose={() => setError('')} dismissible>
              {error}
            </Alert>
          </Col>
        </Row>
      )}

      <Row className="mb-3">
        <Col md={8}>
          <div className="calendar-container">
            <Calendar
              onChange={onDateSelect}
              value={selectedDate}
              tileContent={getTileContent}
              tileClassName={getTileClassName}
              locale="es-ES"
            />
          </div>
        </Col>
        <Col md={4}>
          <div className="calendar-stats">
            <h5>Estadísticas Planificadas</h5>
            <div className="stat-item">
              <strong>Total de días planificados:</strong> {plannedDays.length}
            </div>
            <div className="stat-item">
              <strong>Total de horas:</strong> {getTotalHours().toFixed(1)}h
            </div>
            <div className="stat-item">
              <strong>Horas este mes:</strong> {getCurrentMonthHours().toFixed(1)}h
            </div>
            <div className="stat-item">
              <strong>Promedio por día:</strong> {plannedDays.length > 0 ? (getTotalHours() / plannedDays.length).toFixed(1) : 0}h
            </div>
          </div>

          {selectedDate && (
            <div className="selected-date-info mt-3">
              <h6>Fecha seleccionada:</h6>
              <p>{selectedDate.toLocaleDateString('es-ES', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}</p>
              
              {(() => {
                const dayData = getSelectedDayData();
                return dayData ? (
                  <div className="day-details">
                    <p><strong>Horas planificadas:</strong> {dayData.hours}h</p>
                    {dayData.shiftName && <p><strong>Turno:</strong> {dayData.shiftName}</p>}
                    {dayData.description && <p><strong>Descripción:</strong> {dayData.description}</p>}
                    <Button 
                      variant="outline-danger" 
                      size="sm"
                      onClick={() => handleDeleteDay(dayData.id)}
                      disabled={loading}
                    >
                      🗑️ Eliminar
                    </Button>
                  </div>
                ) : (
                  <p className="text-muted">No hay planificación para este día</p>
                );
              })()}
            </div>
          )}

          <div className="pattern-info mt-3">
            <h6>Patrones disponibles:</h6>
            {patterns.length > 0 ? (
              <ul className="list-unstyled">
                {patterns.map(pattern => (
                  <li key={pattern.id} className="mb-1">
                    <small>
                      <strong>{pattern.name}</strong> ({pattern.days.length} días)
                    </small>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-muted">No hay patrones creados</p>
            )}
          </div>
        </Col>
      </Row>

      {/* Modal para aplicar patrones */}
      <Modal show={showPatternModal} onHide={() => setShowPatternModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Aplicar Patrón al Calendario Teórico</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Fecha de inicio</Form.Label>
                  <Form.Control
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Fecha de fin</Form.Label>
                  <Form.Control
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </Form.Group>
              </Col>
            </Row>
            
            <Form.Group className="mb-3">
              <Form.Label>Seleccionar patrón</Form.Label>
              <Form.Select
                value={selectedPatternId}
                onChange={(e) => setSelectedPatternId(e.target.value)}
              >
                <option value="">Seleccionar patrón...</option>
                {patterns.map(pattern => (
                  <option key={pattern.id} value={pattern.id}>
                    {pattern.name} ({pattern.days.length} días)
                  </option>
                ))}
              </Form.Select>
            </Form.Group>

            {selectedPatternId && (
              <div className="pattern-preview">
                <h6>Vista previa del patrón:</h6>
                {(() => {
                  const pattern = patterns.find(p => p.id === selectedPatternId);
                  return pattern ? (
                    <div className="pattern-days">
                      {pattern.days.map((day, index) => (
                        <div key={index} className="pattern-day-preview">
                          <strong>Día {index + 1}:</strong> {day.shiftName} ({day.hours}h)
                        </div>
                      ))}
                    </div>
                  ) : null;
                })()}
              </div>
            )}
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowPatternModal(false)}>
            Cancelar
          </Button>
          <Button
            variant="primary"
            onClick={handleApplyPattern}
            disabled={loading || !selectedPatternId || !startDate || !endDate}
          >
            {loading ? 'Aplicando...' : 'Aplicar Patrón'}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
}

export default PlannedCalendarView;
