{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\components\\\\RealCalendarManager.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Modal, Form, Button, Alert, Row, Col, Card, Badge } from 'react-bootstrap';\nimport { createOrUpdateRealDay, getRealDayByDate, getAllShifts, applyShiftToDate, suggestShiftsForDate } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction RealCalendarManager({\n  show,\n  onHide,\n  selectedDate,\n  onDayUpdated\n}) {\n  _s();\n  const [dayType, setDayType] = useState('');\n  const [hours, setHours] = useState(0);\n  const [description, setDescription] = useState('');\n  const [startTime, setStartTime] = useState('');\n  const [endTime, setEndTime] = useState('');\n  const [selectedShift, setSelectedShift] = useState('');\n  const [useShift, setUseShift] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [suggestedShifts, setSuggestedShifts] = useState([]);\n  const [existingDay, setExistingDay] = useState(null);\n  useEffect(() => {\n    if (show) {\n      loadAvailableShifts();\n      if (selectedDate) {\n        loadExistingDay();\n        loadSuggestedShifts();\n      }\n    }\n  }, [show, selectedDate]);\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (err) {\n      console.error('Error loading shifts:', err);\n    }\n  };\n  const loadExistingDay = async () => {\n    if (!selectedDate) return;\n    try {\n      const dateStr = selectedDate.toISOString().split('T')[0];\n      const day = await getRealDayByDate(dateStr);\n      if (day) {\n        setExistingDay(day);\n        setDayType(day.type || '');\n        setHours(day.hours || 0);\n        setDescription(day.description || '');\n        if (day.shiftId) {\n          setSelectedShift(day.shiftId);\n          setUseShift(true);\n          setStartTime(day.startTime || '');\n          setEndTime(day.endTime || '');\n        } else {\n          setStartTime('');\n          setEndTime('');\n        }\n      } else {\n        resetForm();\n      }\n    } catch (err) {\n      console.error('Error loading existing day:', err);\n      resetForm();\n    }\n  };\n  const loadSuggestedShifts = async () => {\n    if (!selectedDate) return;\n    try {\n      const dateStr = selectedDate.toISOString().split('T')[0];\n      const suggestions = await suggestShiftsForDate(dateStr);\n      setSuggestedShifts(suggestions);\n    } catch (err) {\n      console.error('Error loading suggested shifts:', err);\n    }\n  };\n  const resetForm = () => {\n    setExistingDay(null);\n    setDayType('');\n    setHours(0);\n    setDescription('');\n    setSelectedShift('');\n    setUseShift(false);\n    setStartTime('');\n    setEndTime('');\n    setError('');\n  };\n  const handleShiftSelect = shift => {\n    setSelectedShift(shift.id);\n    setUseShift(true);\n    setDayType('worked');\n    setHours(shift.totalHours); // Use totalHours from shift\n    setDescription(`${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`);\n    setStartTime(shift.startTime);\n    setEndTime(shift.endTime);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const dateStr = selectedDate.toISOString().split('T')[0];\n      if (useShift && selectedShift) {\n        // Usar turno predefinido\n        await createOrUpdateRealDay(dateStr, dayType, hours, description, selectedShift, startTime, endTime);\n      } else {\n        // Entrada manual\n        await createOrUpdateRealDay(dateStr, dayType, hours, description, null, startTime, endTime);\n      }\n      if (onDayUpdated) {\n        onDayUpdated();\n      }\n      onHide();\n    } catch (err) {\n      setError('Error guardando el día: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDelete = async () => {\n    if (!existingDay || !window.confirm('¿Estás seguro de que quieres eliminar este día?')) {\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      const dateStr = selectedDate.toISOString().split('T')[0];\n      await deleteRealDay(dateStr);\n      if (onDayUpdated) {\n        onDayUpdated();\n      }\n      onHide();\n    } catch (err) {\n      setError('Error eliminando el día: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatDate = date => {\n    return date === null || date === void 0 ? void 0 : date.toLocaleDateString('es-ES', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    show: show,\n    onHide: onHide,\n    size: \"lg\",\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n        children: [existingDay ? 'Editar' : 'Registrar', \" D\\xEDa Real\", selectedDate && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-muted fs-6 mt-1\",\n          children: formatDate(selectedDate)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        onSubmit: handleSubmit,\n        children: [suggestedShifts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Turnos Sugeridos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: suggestedShifts.map(shift => /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              className: \"mb-2\",\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: `shift-suggestion-card ${selectedShift === shift.id ? 'selected' : ''}`,\n                onClick: () => handleShiftSelect(shift),\n                children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                  className: \"p-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: shift.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 195,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-muted small\",\n                        children: [shift.startTime, \" - \", shift.endTime, \" (\", shift.breakMinutes, \"min)\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 196,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"primary\",\n                      children: [shift.totalHours, \"h\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 200,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 21\n              }, this)\n            }, shift.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: /*#__PURE__*/_jsxDEV(Form.Check, {\n            type: \"checkbox\",\n            id: \"useShift\",\n            label: \"Usar turno predefinido\",\n            checked: useShift,\n            onChange: e => setUseShift(e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), useShift && /*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Turno\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n            value: selectedShift,\n            onChange: e => {\n              const shift = availableShifts.find(s => s.id === e.target.value);\n              if (shift) {\n                handleShiftSelect(shift);\n              } else {\n                setSelectedShift('');\n                setStartTime('');\n                setEndTime('');\n                setHours(0);\n                setDescription('');\n              }\n            },\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Selecciona un turno\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this), availableShifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: shift.id,\n              children: [shift.name, \" (\", shift.startTime, \" - \", shift.endTime, \") - \", shift.totalHours, \"h\"]\n            }, shift.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 13\n        }, this), (useShift || dayType === 'worked') && /*#__PURE__*/_jsxDEV(Row, {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            children: /*#__PURE__*/_jsxDEV(Form.Group, {\n              controlId: \"startTime\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Hora de Inicio\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"time\",\n                value: startTime,\n                onChange: e => setStartTime(e.target.value),\n                required: useShift || dayType === 'worked'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            children: /*#__PURE__*/_jsxDEV(Form.Group, {\n              controlId: \"endTime\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Hora de Fin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"time\",\n                value: endTime,\n                onChange: e => setEndTime(e.target.value),\n                required: useShift || dayType === 'worked'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this), !useShift && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Tipo de d\\xEDa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: dayType,\n              onChange: e => setDayType(e.target.value),\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Selecciona el tipo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"worked\",\n                children: \"Trabajado\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"rest\",\n                children: \"Descanso\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"vacation\",\n                children: \"Vacaciones\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"sick\",\n                children: \"Baja m\\xE9dica\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"holiday\",\n                children: \"Festivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this), dayType === 'worked' && /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Horas trabajadas (calculado si hay horas de inicio/fin)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"number\",\n              step: \"0.5\",\n              min: \"0\",\n              max: \"24\",\n              value: hours,\n              onChange: e => setHours(parseFloat(e.target.value) || 0),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Descripci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n            as: \"textarea\",\n            rows: 3,\n            value: description,\n            onChange: e => setDescription(e.target.value),\n            placeholder: \"Descripci\\xF3n opcional del d\\xEDa...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), existingDay && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"info\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"D\\xEDa existente:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this), \" \", existingDay.type, \" - \", existingDay.hours, \"h\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            children: existingDay.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: onHide,\n        children: \"Cancelar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: handleSubmit,\n        disabled: loading || !useShift && !dayType,\n        children: loading ? 'Guardando...' : existingDay ? 'Actualizar' : 'Guardar'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 165,\n    columnNumber: 5\n  }, this);\n}\n_s(RealCalendarManager, \"4/O0F/pDvQn/+wZEeL8FItqoQCI=\");\n_c = RealCalendarManager;\nexport default RealCalendarManager;\nvar _c;\n$RefreshReg$(_c, \"RealCalendarManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Modal", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Row", "Col", "Card", "Badge", "createOrUpdateRealDay", "getRealDayByDate", "getAllShifts", "applyShiftToDate", "suggestShiftsForDate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RealCalendarManager", "show", "onHide", "selectedDate", "onDayUpdated", "_s", "dayType", "setDayType", "hours", "setHours", "description", "setDescription", "startTime", "setStartTime", "endTime", "setEndTime", "selectedShift", "setSelectedShift", "useShift", "setUseShift", "loading", "setLoading", "error", "setError", "availableShifts", "setAvailableShifts", "suggestedShifts", "setSuggestedShifts", "existingDay", "setExistingDay", "loadAvailableShifts", "loadExistingDay", "loadSuggestedShifts", "shifts", "err", "console", "dateStr", "toISOString", "split", "day", "type", "shiftId", "resetForm", "suggestions", "handleShiftSelect", "shift", "id", "totalHours", "name", "breakMinutes", "handleSubmit", "e", "preventDefault", "message", "handleDelete", "window", "confirm", "deleteRealDay", "formatDate", "date", "toLocaleDateString", "weekday", "year", "month", "size", "children", "Header", "closeButton", "Title", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "variant", "onSubmit", "length", "map", "md", "onClick", "bg", "Group", "Check", "label", "checked", "onChange", "target", "Label", "Select", "value", "find", "s", "required", "controlId", "Control", "step", "min", "max", "parseFloat", "as", "rows", "placeholder", "Footer", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/components/RealCalendarManager.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Modal, Form, Button, Alert, Row, Col, Card, Badge } from 'react-bootstrap';\nimport {\n  createOrUpdateRealDay,\n  getRealDayByDate,\n  getAllShifts,\n  applyShiftToDate,\n  suggestShiftsForDate\n} from '../services/api';\n\nfunction RealCalendarManager({ show, onHide, selectedDate, onDayUpdated }) {\n  const [dayType, setDayType] = useState('');\n  const [hours, setHours] = useState(0);\n  const [description, setDescription] = useState('');\n  const [startTime, setStartTime] = useState('');\n  const [endTime, setEndTime] = useState('');\n  const [selectedShift, setSelectedShift] = useState('');\n  const [useShift, setUseShift] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [suggestedShifts, setSuggestedShifts] = useState([]);\n  const [existingDay, setExistingDay] = useState(null);\n\n  useEffect(() => {\n    if (show) {\n      loadAvailableShifts();\n      if (selectedDate) {\n        loadExistingDay();\n        loadSuggestedShifts();\n      }\n    }\n  }, [show, selectedDate]);\n\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (err) {\n      console.error('Error loading shifts:', err);\n    }\n  };\n\n  const loadExistingDay = async () => {\n    if (!selectedDate) return;\n    \n    try {\n      const dateStr = selectedDate.toISOString().split('T')[0];\n      const day = await getRealDayByDate(dateStr);\n      \n      if (day) {\n        setExistingDay(day);\n        setDayType(day.type || '');\n        setHours(day.hours || 0);\n        setDescription(day.description || '');\n        if (day.shiftId) {\n          setSelectedShift(day.shiftId);\n          setUseShift(true);\n          setStartTime(day.startTime || '');\n          setEndTime(day.endTime || '');\n        } else {\n          setStartTime('');\n          setEndTime('');\n        }\n      } else {\n        resetForm();\n      }\n    } catch (err) {\n      console.error('Error loading existing day:', err);\n      resetForm();\n    }\n  };\n\n  const loadSuggestedShifts = async () => {\n    if (!selectedDate) return;\n    \n    try {\n      const dateStr = selectedDate.toISOString().split('T')[0];\n      const suggestions = await suggestShiftsForDate(dateStr);\n      setSuggestedShifts(suggestions);\n    } catch (err) {\n      console.error('Error loading suggested shifts:', err);\n    }\n  };\n\n  const resetForm = () => {\n    setExistingDay(null);\n    setDayType('');\n    setHours(0);\n    setDescription('');\n    setSelectedShift('');\n    setUseShift(false);\n    setStartTime('');\n    setEndTime('');\n    setError('');\n  };\n\n  const handleShiftSelect = (shift) => {\n    setSelectedShift(shift.id);\n    setUseShift(true);\n    setDayType('worked');\n    setHours(shift.totalHours); // Use totalHours from shift\n    setDescription(`${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`);\n    setStartTime(shift.startTime);\n    setEndTime(shift.endTime);\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const dateStr = selectedDate.toISOString().split('T')[0];\n      \n      if (useShift && selectedShift) {\n        // Usar turno predefinido\n        await createOrUpdateRealDay(dateStr, dayType, hours, description, selectedShift, startTime, endTime);\n      } else {\n        // Entrada manual\n        await createOrUpdateRealDay(dateStr, dayType, hours, description, null, startTime, endTime);\n      }\n\n      if (onDayUpdated) {\n        onDayUpdated();\n      }\n      onHide();\n    } catch (err) {\n      setError('Error guardando el día: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDelete = async () => {\n    if (!existingDay || !window.confirm('¿Estás seguro de que quieres eliminar este día?')) {\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      const dateStr = selectedDate.toISOString().split('T')[0];\n      await deleteRealDay(dateStr);\n      if (onDayUpdated) {\n        onDayUpdated();\n      }\n      onHide();\n    } catch (err) {\n      setError('Error eliminando el día: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatDate = (date) => {\n    return date?.toLocaleDateString('es-ES', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  return (\n    <Modal show={show} onHide={onHide} size=\"lg\">\n      <Modal.Header closeButton>\n        <Modal.Title>\n          {existingDay ? 'Editar' : 'Registrar'} Día Real\n          {selectedDate && (\n            <div className=\"text-muted fs-6 mt-1\">\n              {formatDate(selectedDate)}\n            </div>\n          )}\n        </Modal.Title>\n      </Modal.Header>\n\n      <Modal.Body>\n        {error && <Alert variant=\"danger\">{error}</Alert>}\n\n        <Form onSubmit={handleSubmit}>\n          {/* Sección de turnos sugeridos */}\n          {suggestedShifts.length > 0 && (\n            <div className=\"mb-4\">\n              <h6>Turnos Sugeridos</h6>\n              <Row>\n                {suggestedShifts.map((shift) => (\n                  <Col md={6} key={shift.id} className=\"mb-2\">\n                    <Card \n                      className={`shift-suggestion-card ${selectedShift === shift.id ? 'selected' : ''}`}\n                      onClick={() => handleShiftSelect(shift)}\n                    >\n                      <Card.Body className=\"p-2\">\n                        <div className=\"d-flex justify-content-between align-items-center\">\n                          <div>\n                            <strong>{shift.name}</strong>\n                            <div className=\"text-muted small\">\n                              {shift.startTime} - {shift.endTime} ({shift.breakMinutes}min)\n                            </div>\n                          </div>\n                          <Badge bg=\"primary\">{shift.totalHours}h</Badge>\n                        </div>\n                      </Card.Body>\n                    </Card>\n                  </Col>\n                ))}\n              </Row>\n            </div>\n          )}\n\n          {/* Opción de usar turno predefinido */}\n          <Form.Group className=\"mb-3\">\n            <Form.Check\n              type=\"checkbox\"\n              id=\"useShift\"\n              label=\"Usar turno predefinido\"\n              checked={useShift}\n              onChange={(e) => setUseShift(e.target.checked)}\n            />\n          </Form.Group>\n\n          {useShift && (\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Turno</Form.Label>\n              <Form.Select\n                value={selectedShift}\n                onChange={(e) => {\n                  const shift = availableShifts.find(s => s.id === e.target.value);\n                  if (shift) {\n                    handleShiftSelect(shift);\n                  } else {\n                    setSelectedShift('');\n                    setStartTime('');\n                    setEndTime('');\n                    setHours(0);\n                    setDescription('');\n                  }\n                }}\n                required\n              >\n                <option value=\"\">Selecciona un turno</option>\n                {availableShifts.map((shift) => (\n                  <option key={shift.id} value={shift.id}>\n                    {shift.name} ({shift.startTime} - {shift.endTime}) - {shift.totalHours}h\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n          )}\n\n          {/* Campos de hora de inicio y fin (visibles si se usa turno o entrada manual) */}\n          {(useShift || dayType === 'worked') && (\n            <Row className=\"mb-3\">\n              <Col>\n                <Form.Group controlId=\"startTime\">\n                  <Form.Label>Hora de Inicio</Form.Label>\n                  <Form.Control\n                    type=\"time\"\n                    value={startTime}\n                    onChange={(e) => setStartTime(e.target.value)}\n                    required={useShift || dayType === 'worked'}\n                  />\n                </Form.Group>\n              </Col>\n              <Col>\n                <Form.Group controlId=\"endTime\">\n                  <Form.Label>Hora de Fin</Form.Label>\n                  <Form.Control\n                    type=\"time\"\n                    value={endTime}\n                    onChange={(e) => setEndTime(e.target.value)}\n                    required={useShift || dayType === 'worked'}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n          )}\n\n          {!useShift && (\n            <>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Tipo de día</Form.Label>\n                <Form.Select\n                  value={dayType}\n                  onChange={(e) => setDayType(e.target.value)}\n                  required\n                >\n                  <option value=\"\">Selecciona el tipo</option>\n                  <option value=\"worked\">Trabajado</option>\n                  <option value=\"rest\">Descanso</option>\n                  <option value=\"vacation\">Vacaciones</option>\n                  <option value=\"sick\">Baja médica</option>\n                  <option value=\"holiday\">Festivo</option>\n                </Form.Select>\n              </Form.Group>\n\n              {dayType === 'worked' && (\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Horas trabajadas (calculado si hay horas de inicio/fin)</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    step=\"0.5\"\n                    min=\"0\"\n                    max=\"24\"\n                    value={hours}\n                    onChange={(e) => setHours(parseFloat(e.target.value) || 0)}\n                    required\n                  />\n                </Form.Group>\n              )}\n            </>\n          )}\n\n          <Form.Group className=\"mb-3\">\n            <Form.Label>Descripción</Form.Label>\n            <Form.Control\n              as=\"textarea\"\n              rows={3}\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              placeholder=\"Descripción opcional del día...\"\n            />\n          </Form.Group>\n\n          {existingDay && (\n            <Alert variant=\"info\">\n              <strong>Día existente:</strong> {existingDay.type} - {existingDay.hours}h\n              <br />\n              <small>{existingDay.description}</small>\n            </Alert>\n          )}\n        </Form>\n      </Modal.Body>\n\n      <Modal.Footer>\n        <Button variant=\"secondary\" onClick={onHide}>\n          Cancelar\n        </Button>\n        <Button \n          variant=\"primary\" \n          onClick={handleSubmit}\n          disabled={loading || (!useShift && !dayType)}\n        >\n          {loading ? 'Guardando...' : (existingDay ? 'Actualizar' : 'Guardar')}\n        </Button>\n      </Modal.Footer>\n    </Modal>\n  );\n}\n\nexport default RealCalendarManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,QAAQ,iBAAiB;AACnF,SACEC,qBAAqB,EACrBC,gBAAgB,EAChBC,YAAY,EACZC,gBAAgB,EAChBC,oBAAoB,QACf,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,SAASC,mBAAmBA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,YAAY;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EACzE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACd,IAAImB,IAAI,EAAE;MACR6B,mBAAmB,CAAC,CAAC;MACrB,IAAI3B,YAAY,EAAE;QAChB4B,eAAe,CAAC,CAAC;QACjBC,mBAAmB,CAAC,CAAC;MACvB;IACF;EACF,CAAC,EAAE,CAAC/B,IAAI,EAAEE,YAAY,CAAC,CAAC;EAExB,MAAM2B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMG,MAAM,GAAG,MAAMxC,YAAY,CAAC,CAAC;MACnCgC,kBAAkB,CAACQ,MAAM,CAAC;IAC5B,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACb,KAAK,CAAC,uBAAuB,EAAEY,GAAG,CAAC;IAC7C;EACF,CAAC;EAED,MAAMH,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAAC5B,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMiC,OAAO,GAAGjC,YAAY,CAACkC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACxD,MAAMC,GAAG,GAAG,MAAM/C,gBAAgB,CAAC4C,OAAO,CAAC;MAE3C,IAAIG,GAAG,EAAE;QACPV,cAAc,CAACU,GAAG,CAAC;QACnBhC,UAAU,CAACgC,GAAG,CAACC,IAAI,IAAI,EAAE,CAAC;QAC1B/B,QAAQ,CAAC8B,GAAG,CAAC/B,KAAK,IAAI,CAAC,CAAC;QACxBG,cAAc,CAAC4B,GAAG,CAAC7B,WAAW,IAAI,EAAE,CAAC;QACrC,IAAI6B,GAAG,CAACE,OAAO,EAAE;UACfxB,gBAAgB,CAACsB,GAAG,CAACE,OAAO,CAAC;UAC7BtB,WAAW,CAAC,IAAI,CAAC;UACjBN,YAAY,CAAC0B,GAAG,CAAC3B,SAAS,IAAI,EAAE,CAAC;UACjCG,UAAU,CAACwB,GAAG,CAACzB,OAAO,IAAI,EAAE,CAAC;QAC/B,CAAC,MAAM;UACLD,YAAY,CAAC,EAAE,CAAC;UAChBE,UAAU,CAAC,EAAE,CAAC;QAChB;MACF,CAAC,MAAM;QACL2B,SAAS,CAAC,CAAC;MACb;IACF,CAAC,CAAC,OAAOR,GAAG,EAAE;MACZC,OAAO,CAACb,KAAK,CAAC,6BAA6B,EAAEY,GAAG,CAAC;MACjDQ,SAAS,CAAC,CAAC;IACb;EACF,CAAC;EAED,MAAMV,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAAC7B,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMiC,OAAO,GAAGjC,YAAY,CAACkC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACxD,MAAMK,WAAW,GAAG,MAAMhD,oBAAoB,CAACyC,OAAO,CAAC;MACvDT,kBAAkB,CAACgB,WAAW,CAAC;IACjC,CAAC,CAAC,OAAOT,GAAG,EAAE;MACZC,OAAO,CAACb,KAAK,CAAC,iCAAiC,EAAEY,GAAG,CAAC;IACvD;EACF,CAAC;EAED,MAAMQ,SAAS,GAAGA,CAAA,KAAM;IACtBb,cAAc,CAAC,IAAI,CAAC;IACpBtB,UAAU,CAAC,EAAE,CAAC;IACdE,QAAQ,CAAC,CAAC,CAAC;IACXE,cAAc,CAAC,EAAE,CAAC;IAClBM,gBAAgB,CAAC,EAAE,CAAC;IACpBE,WAAW,CAAC,KAAK,CAAC;IAClBN,YAAY,CAAC,EAAE,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IACdQ,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMqB,iBAAiB,GAAIC,KAAK,IAAK;IACnC5B,gBAAgB,CAAC4B,KAAK,CAACC,EAAE,CAAC;IAC1B3B,WAAW,CAAC,IAAI,CAAC;IACjBZ,UAAU,CAAC,QAAQ,CAAC;IACpBE,QAAQ,CAACoC,KAAK,CAACE,UAAU,CAAC,CAAC,CAAC;IAC5BpC,cAAc,CAAC,GAAGkC,KAAK,CAACG,IAAI,KAAKH,KAAK,CAACjC,SAAS,MAAMiC,KAAK,CAAC/B,OAAO,KAAK+B,KAAK,CAACI,YAAY,eAAe,CAAC;IAC1GpC,YAAY,CAACgC,KAAK,CAACjC,SAAS,CAAC;IAC7BG,UAAU,CAAC8B,KAAK,CAAC/B,OAAO,CAAC;EAC3B,CAAC;EAED,MAAMoC,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB/B,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMa,OAAO,GAAGjC,YAAY,CAACkC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAExD,IAAIpB,QAAQ,IAAIF,aAAa,EAAE;QAC7B;QACA,MAAMzB,qBAAqB,CAAC6C,OAAO,EAAE9B,OAAO,EAAEE,KAAK,EAAEE,WAAW,EAAEM,aAAa,EAAEJ,SAAS,EAAEE,OAAO,CAAC;MACtG,CAAC,MAAM;QACL;QACA,MAAMvB,qBAAqB,CAAC6C,OAAO,EAAE9B,OAAO,EAAEE,KAAK,EAAEE,WAAW,EAAE,IAAI,EAAEE,SAAS,EAAEE,OAAO,CAAC;MAC7F;MAEA,IAAIV,YAAY,EAAE;QAChBA,YAAY,CAAC,CAAC;MAChB;MACAF,MAAM,CAAC,CAAC;IACV,CAAC,CAAC,OAAOgC,GAAG,EAAE;MACZX,QAAQ,CAAC,0BAA0B,GAAGW,GAAG,CAACmB,OAAO,CAAC;IACpD,CAAC,SAAS;MACRhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAC1B,WAAW,IAAI,CAAC2B,MAAM,CAACC,OAAO,CAAC,iDAAiD,CAAC,EAAE;MACtF;IACF;IACAnC,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,MAAMa,OAAO,GAAGjC,YAAY,CAACkC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACxD,MAAMmB,aAAa,CAACrB,OAAO,CAAC;MAC5B,IAAIhC,YAAY,EAAE;QAChBA,YAAY,CAAC,CAAC;MAChB;MACAF,MAAM,CAAC,CAAC;IACV,CAAC,CAAC,OAAOgC,GAAG,EAAE;MACZX,QAAQ,CAAC,2BAA2B,GAAGW,GAAG,CAACmB,OAAO,CAAC;IACrD,CAAC,SAAS;MACRhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqC,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAOA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,kBAAkB,CAAC,OAAO,EAAE;MACvCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbxB,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,oBACE1C,OAAA,CAACd,KAAK;IAACkB,IAAI,EAAEA,IAAK;IAACC,MAAM,EAAEA,MAAO;IAAC8D,IAAI,EAAC,IAAI;IAAAC,QAAA,gBAC1CpE,OAAA,CAACd,KAAK,CAACmF,MAAM;MAACC,WAAW;MAAAF,QAAA,eACvBpE,OAAA,CAACd,KAAK,CAACqF,KAAK;QAAAH,QAAA,GACTrC,WAAW,GAAG,QAAQ,GAAG,WAAW,EAAC,cACtC,EAACzB,YAAY,iBACXN,OAAA;UAAKwE,SAAS,EAAC,sBAAsB;UAAAJ,QAAA,EAClCP,UAAU,CAACvD,YAAY;QAAC;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEf5E,OAAA,CAACd,KAAK,CAAC2F,IAAI;MAAAT,QAAA,GACR3C,KAAK,iBAAIzB,OAAA,CAACX,KAAK;QAACyF,OAAO,EAAC,QAAQ;QAAAV,QAAA,EAAE3C;MAAK;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAEjD5E,OAAA,CAACb,IAAI;QAAC4F,QAAQ,EAAE1B,YAAa;QAAAe,QAAA,GAE1BvC,eAAe,CAACmD,MAAM,GAAG,CAAC,iBACzBhF,OAAA;UAAKwE,SAAS,EAAC,MAAM;UAAAJ,QAAA,gBACnBpE,OAAA;YAAAoE,QAAA,EAAI;UAAgB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB5E,OAAA,CAACV,GAAG;YAAA8E,QAAA,EACDvC,eAAe,CAACoD,GAAG,CAAEjC,KAAK,iBACzBhD,OAAA,CAACT,GAAG;cAAC2F,EAAE,EAAE,CAAE;cAAgBV,SAAS,EAAC,MAAM;cAAAJ,QAAA,eACzCpE,OAAA,CAACR,IAAI;gBACHgF,SAAS,EAAE,yBAAyBrD,aAAa,KAAK6B,KAAK,CAACC,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;gBACnFkC,OAAO,EAAEA,CAAA,KAAMpC,iBAAiB,CAACC,KAAK,CAAE;gBAAAoB,QAAA,eAExCpE,OAAA,CAACR,IAAI,CAACqF,IAAI;kBAACL,SAAS,EAAC,KAAK;kBAAAJ,QAAA,eACxBpE,OAAA;oBAAKwE,SAAS,EAAC,mDAAmD;oBAAAJ,QAAA,gBAChEpE,OAAA;sBAAAoE,QAAA,gBACEpE,OAAA;wBAAAoE,QAAA,EAASpB,KAAK,CAACG;sBAAI;wBAAAsB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,eAC7B5E,OAAA;wBAAKwE,SAAS,EAAC,kBAAkB;wBAAAJ,QAAA,GAC9BpB,KAAK,CAACjC,SAAS,EAAC,KAAG,EAACiC,KAAK,CAAC/B,OAAO,EAAC,IAAE,EAAC+B,KAAK,CAACI,YAAY,EAAC,MAC3D;sBAAA;wBAAAqB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN5E,OAAA,CAACP,KAAK;sBAAC2F,EAAE,EAAC,SAAS;sBAAAhB,QAAA,GAAEpB,KAAK,CAACE,UAAU,EAAC,GAAC;oBAAA;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC,GAhBQ5B,KAAK,CAACC,EAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBpB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD5E,OAAA,CAACb,IAAI,CAACkG,KAAK;UAACb,SAAS,EAAC,MAAM;UAAAJ,QAAA,eAC1BpE,OAAA,CAACb,IAAI,CAACmG,KAAK;YACT3C,IAAI,EAAC,UAAU;YACfM,EAAE,EAAC,UAAU;YACbsC,KAAK,EAAC,wBAAwB;YAC9BC,OAAO,EAAEnE,QAAS;YAClBoE,QAAQ,EAAGnC,CAAC,IAAKhC,WAAW,CAACgC,CAAC,CAACoC,MAAM,CAACF,OAAO;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,EAEZvD,QAAQ,iBACPrB,OAAA,CAACb,IAAI,CAACkG,KAAK;UAACb,SAAS,EAAC,MAAM;UAAAJ,QAAA,gBAC1BpE,OAAA,CAACb,IAAI,CAACwG,KAAK;YAAAvB,QAAA,EAAC;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9B5E,OAAA,CAACb,IAAI,CAACyG,MAAM;YACVC,KAAK,EAAE1E,aAAc;YACrBsE,QAAQ,EAAGnC,CAAC,IAAK;cACf,MAAMN,KAAK,GAAGrB,eAAe,CAACmE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAKK,CAAC,CAACoC,MAAM,CAACG,KAAK,CAAC;cAChE,IAAI7C,KAAK,EAAE;gBACTD,iBAAiB,CAACC,KAAK,CAAC;cAC1B,CAAC,MAAM;gBACL5B,gBAAgB,CAAC,EAAE,CAAC;gBACpBJ,YAAY,CAAC,EAAE,CAAC;gBAChBE,UAAU,CAAC,EAAE,CAAC;gBACdN,QAAQ,CAAC,CAAC,CAAC;gBACXE,cAAc,CAAC,EAAE,CAAC;cACpB;YACF,CAAE;YACFkF,QAAQ;YAAA5B,QAAA,gBAERpE,OAAA;cAAQ6F,KAAK,EAAC,EAAE;cAAAzB,QAAA,EAAC;YAAmB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAC5CjD,eAAe,CAACsD,GAAG,CAAEjC,KAAK,iBACzBhD,OAAA;cAAuB6F,KAAK,EAAE7C,KAAK,CAACC,EAAG;cAAAmB,QAAA,GACpCpB,KAAK,CAACG,IAAI,EAAC,IAAE,EAACH,KAAK,CAACjC,SAAS,EAAC,KAAG,EAACiC,KAAK,CAAC/B,OAAO,EAAC,MAAI,EAAC+B,KAAK,CAACE,UAAU,EAAC,GACzE;YAAA,GAFaF,KAAK,CAACC,EAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACb,EAGA,CAACvD,QAAQ,IAAIZ,OAAO,KAAK,QAAQ,kBAChCT,OAAA,CAACV,GAAG;UAACkF,SAAS,EAAC,MAAM;UAAAJ,QAAA,gBACnBpE,OAAA,CAACT,GAAG;YAAA6E,QAAA,eACFpE,OAAA,CAACb,IAAI,CAACkG,KAAK;cAACY,SAAS,EAAC,WAAW;cAAA7B,QAAA,gBAC/BpE,OAAA,CAACb,IAAI,CAACwG,KAAK;gBAAAvB,QAAA,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvC5E,OAAA,CAACb,IAAI,CAAC+G,OAAO;gBACXvD,IAAI,EAAC,MAAM;gBACXkD,KAAK,EAAE9E,SAAU;gBACjB0E,QAAQ,EAAGnC,CAAC,IAAKtC,YAAY,CAACsC,CAAC,CAACoC,MAAM,CAACG,KAAK,CAAE;gBAC9CG,QAAQ,EAAE3E,QAAQ,IAAIZ,OAAO,KAAK;cAAS;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN5E,OAAA,CAACT,GAAG;YAAA6E,QAAA,eACFpE,OAAA,CAACb,IAAI,CAACkG,KAAK;cAACY,SAAS,EAAC,SAAS;cAAA7B,QAAA,gBAC7BpE,OAAA,CAACb,IAAI,CAACwG,KAAK;gBAAAvB,QAAA,EAAC;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpC5E,OAAA,CAACb,IAAI,CAAC+G,OAAO;gBACXvD,IAAI,EAAC,MAAM;gBACXkD,KAAK,EAAE5E,OAAQ;gBACfwE,QAAQ,EAAGnC,CAAC,IAAKpC,UAAU,CAACoC,CAAC,CAACoC,MAAM,CAACG,KAAK,CAAE;gBAC5CG,QAAQ,EAAE3E,QAAQ,IAAIZ,OAAO,KAAK;cAAS;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA,CAACvD,QAAQ,iBACRrB,OAAA,CAAAE,SAAA;UAAAkE,QAAA,gBACEpE,OAAA,CAACb,IAAI,CAACkG,KAAK;YAACb,SAAS,EAAC,MAAM;YAAAJ,QAAA,gBAC1BpE,OAAA,CAACb,IAAI,CAACwG,KAAK;cAAAvB,QAAA,EAAC;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpC5E,OAAA,CAACb,IAAI,CAACyG,MAAM;cACVC,KAAK,EAAEpF,OAAQ;cACfgF,QAAQ,EAAGnC,CAAC,IAAK5C,UAAU,CAAC4C,CAAC,CAACoC,MAAM,CAACG,KAAK,CAAE;cAC5CG,QAAQ;cAAA5B,QAAA,gBAERpE,OAAA;gBAAQ6F,KAAK,EAAC,EAAE;gBAAAzB,QAAA,EAAC;cAAkB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C5E,OAAA;gBAAQ6F,KAAK,EAAC,QAAQ;gBAAAzB,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzC5E,OAAA;gBAAQ6F,KAAK,EAAC,MAAM;gBAAAzB,QAAA,EAAC;cAAQ;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC5E,OAAA;gBAAQ6F,KAAK,EAAC,UAAU;gBAAAzB,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C5E,OAAA;gBAAQ6F,KAAK,EAAC,MAAM;gBAAAzB,QAAA,EAAC;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzC5E,OAAA;gBAAQ6F,KAAK,EAAC,SAAS;gBAAAzB,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EAEZnE,OAAO,KAAK,QAAQ,iBACnBT,OAAA,CAACb,IAAI,CAACkG,KAAK;YAACb,SAAS,EAAC,MAAM;YAAAJ,QAAA,gBAC1BpE,OAAA,CAACb,IAAI,CAACwG,KAAK;cAAAvB,QAAA,EAAC;YAAuD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChF5E,OAAA,CAACb,IAAI,CAAC+G,OAAO;cACXvD,IAAI,EAAC,QAAQ;cACbwD,IAAI,EAAC,KAAK;cACVC,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC,IAAI;cACRR,KAAK,EAAElF,KAAM;cACb8E,QAAQ,EAAGnC,CAAC,IAAK1C,QAAQ,CAAC0F,UAAU,CAAChD,CAAC,CAACoC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,CAAE;cAC3DG,QAAQ;YAAA;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CACb;QAAA,eACD,CACH,eAED5E,OAAA,CAACb,IAAI,CAACkG,KAAK;UAACb,SAAS,EAAC,MAAM;UAAAJ,QAAA,gBAC1BpE,OAAA,CAACb,IAAI,CAACwG,KAAK;YAAAvB,QAAA,EAAC;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACpC5E,OAAA,CAACb,IAAI,CAAC+G,OAAO;YACXK,EAAE,EAAC,UAAU;YACbC,IAAI,EAAE,CAAE;YACRX,KAAK,EAAEhF,WAAY;YACnB4E,QAAQ,EAAGnC,CAAC,IAAKxC,cAAc,CAACwC,CAAC,CAACoC,MAAM,CAACG,KAAK,CAAE;YAChDY,WAAW,EAAC;UAAiC;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,EAEZ7C,WAAW,iBACV/B,OAAA,CAACX,KAAK;UAACyF,OAAO,EAAC,MAAM;UAAAV,QAAA,gBACnBpE,OAAA;YAAAoE,QAAA,EAAQ;UAAc;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC7C,WAAW,CAACY,IAAI,EAAC,KAAG,EAACZ,WAAW,CAACpB,KAAK,EAAC,GACxE,eAAAX,OAAA;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN5E,OAAA;YAAAoE,QAAA,EAAQrC,WAAW,CAAClB;UAAW;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEb5E,OAAA,CAACd,KAAK,CAACwH,MAAM;MAAAtC,QAAA,gBACXpE,OAAA,CAACZ,MAAM;QAAC0F,OAAO,EAAC,WAAW;QAACK,OAAO,EAAE9E,MAAO;QAAA+D,QAAA,EAAC;MAE7C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT5E,OAAA,CAACZ,MAAM;QACL0F,OAAO,EAAC,SAAS;QACjBK,OAAO,EAAE9B,YAAa;QACtBsD,QAAQ,EAAEpF,OAAO,IAAK,CAACF,QAAQ,IAAI,CAACZ,OAAS;QAAA2D,QAAA,EAE5C7C,OAAO,GAAG,cAAc,GAAIQ,WAAW,GAAG,YAAY,GAAG;MAAU;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEZ;AAACpE,EAAA,CAjVQL,mBAAmB;AAAAyG,EAAA,GAAnBzG,mBAAmB;AAmV5B,eAAeA,mBAAmB;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}