import React, { useState, useEffect } from 'react';
import { Row, Col, Button, Alert } from 'react-bootstrap';
import {
  getAllPlannedDays,
  getAllPatterns
} from '../services/api';

function PlannedCalendarView({ onDateSelect, selectedDate, onCalendarUpdate }) {
  const [plannedDays, setPlannedDays] = useState([]);
  const [patterns, setPatterns] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    loadPlannedDays();
    loadPatterns();
  }, []);

  const loadPlannedDays = async () => {
    try {
      setLoading(true);
      const days = await getAllPlannedDays();
      setPlannedDays(days);
      setError('');
    } catch (err) {
      console.error('Error loading planned days:', err);
      setError('Error cargando días planificados: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const loadPatterns = async () => {
    try {
      const patternsData = await getAllPatterns();
      setPatterns(patternsData);
    } catch (err) {
      console.error('Error loading patterns:', err);
    }
  };

  const getTotalHours = () => {
    return plannedDays.reduce((total, day) => total + (day.hours || 0), 0);
  };

  return (
    <div>
      <Row className="mb-3">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <h4>Calendario Teórico (Planificado)</h4>
            <div>
              <Button
                variant="success"
                onClick={() => alert('Funcionalidad en desarrollo')}
                className="me-2"
              >
                📋 Aplicar Patrón
              </Button>
              <Button
                variant="outline-danger"
                onClick={() => alert('Funcionalidad en desarrollo')}
                disabled={loading}
              >
                🗑️ Limpiar Todo
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {error && (
        <Row className="mb-3">
          <Col>
            <Alert variant="danger" onClose={() => setError('')} dismissible>
              {error}
            </Alert>
          </Col>
        </Row>
      )}

      <Row className="mb-3">
        <Col md={8}>
          <div className="calendar-container">
            <p>Calendario visual estará disponible pronto</p>
            {loading && <p>Cargando días planificados...</p>}
          </div>
        </Col>
        <Col md={4}>
          <div className="calendar-stats">
            <h5>Estadísticas Planificadas</h5>
            <div className="stat-item">
              <strong>Total de días planificados:</strong> {plannedDays.length}
            </div>
            <div className="stat-item">
              <strong>Total de horas:</strong> {getTotalHours().toFixed(1)}h
            </div>
          </div>

          <div className="pattern-info mt-3">
            <h6>Patrones disponibles:</h6>
            {patterns.length > 0 ? (
              <ul className="list-unstyled">
                {patterns.map(pattern => (
                  <li key={pattern.id} className="mb-1">
                    <small>
                      <strong>{pattern.name}</strong> ({pattern.days.length} días)
                    </small>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-muted">No hay patrones creados</p>
            )}
          </div>
        </Col>
      </Row>
    </div>
  );
}

export default PlannedCalendarView;
