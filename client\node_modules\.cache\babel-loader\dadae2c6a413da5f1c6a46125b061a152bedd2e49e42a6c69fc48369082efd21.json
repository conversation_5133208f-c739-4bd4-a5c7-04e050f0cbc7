{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\CalendarPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Modal, Form, Alert, Badge, Tabs, Tab } from 'react-bootstrap';\nimport DualCalendarView from '../components/DualCalendarView';\nimport RealCalendarManager from '../components/RealCalendarManager';\nimport CalendarAnalysis from '../components/CalendarAnalysis';\nimport { getDayByDate, createOrUpdateDay, getAllShifts, applyShiftToDate, suggestShiftsForDate, applyPattern, getAllPatterns, clearPlannedCalendar } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CalendarPage() {\n  _s();\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('calendar');\n\n  // Estados para modales\n  const [showRealDayModal, setShowRealDayModal] = useState(false);\n  const [showPatternModal, setShowPatternModal] = useState(false);\n\n  // Estados para aplicación de patrones\n  const [selectionStartDate, setSelectionStartDate] = useState(null);\n  const [selectionEndDate, setSelectionEndDate] = useState(null);\n  const [allPatterns, setAllPatterns] = useState([]);\n  const [selectedPatternId, setSelectedPatternId] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estado para forzar actualización del calendario\n  const [calendarKey, setCalendarKey] = useState(0);\n  useEffect(() => {\n    loadAllPatterns();\n  }, []);\n  const loadAllPatterns = async () => {\n    try {\n      const patterns = await getAllPatterns();\n      setAllPatterns(patterns);\n    } catch (error) {\n      console.error('Error loading patterns:', error);\n      setError('Error cargando patrones.');\n    }\n  };\n  const handleDateSelect = date => {\n    setSelectedDate(date);\n  };\n  const handleRealDayUpdated = () => {\n    // Forzar actualización del calendario\n    setCalendarKey(prev => prev + 1);\n  };\n  const handleClearPlannedCalendar = async () => {\n    if (window.confirm('¿Estás seguro de que quieres limpiar todo el calendario planificado?')) {\n      try {\n        setLoading(true);\n        await clearPlannedCalendar();\n        setCalendarKey(prev => prev + 1);\n        setError('');\n      } catch (err) {\n        setError('Error limpiando calendario planificado: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  const handlePatternDateClick = value => {\n    if (!selectionStartDate) {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    } else if (!selectionEndDate) {\n      if (value < selectionStartDate) {\n        setSelectionEndDate(selectionStartDate);\n        setSelectionStartDate(value);\n      } else {\n        setSelectionEndDate(value);\n      }\n    } else {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Sistema de Horarios Dual\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-primary\",\n              size: \"sm\",\n              onClick: () => setShowRealDayModal(true),\n              disabled: !selectedDate,\n              className: \"me-2\",\n              children: \"Registrar D\\xEDa Real\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-danger\",\n              size: \"sm\",\n              onClick: handleClearPlannedCalendar,\n              disabled: loading,\n              children: \"Limpiar Planificado\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Aplicar Patr\\xF3n a Rango Seleccionado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"mb-3\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de inicio\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : '',\n                  onChange: e => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                controlId: \"patternEndDate\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de fin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : '',\n                  onChange: e => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Seleccionar Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: selectedPatternId,\n              onChange: e => setSelectedPatternId(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Seleccionar patr\\xF3n...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), allPatterns.map(pattern => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: pattern.id,\n                children: pattern.name\n              }, pattern.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleApplyPattern,\n            disabled: loading || !selectedPatternId || !selectionStartDate || !selectionEndDate,\n            children: loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => {\n              setSelectionStartDate(null);\n              setSelectionEndDate(null);\n              setSelectedPatternId('');\n              setError('');\n            },\n            className: \"ms-2\",\n            children: \"Limpiar Selecci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Aplicar Patr\\xF3n a Rango Seleccionado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"mb-3\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de inicio\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : '',\n                  onChange: e => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de fin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : '',\n                  onChange: e => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Seleccionar Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: selectedPatternId,\n              onChange: e => setSelectedPatternId(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Seleccionar patr\\xF3n...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), allPatterns.map(pattern => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: pattern.id,\n                children: pattern.name\n              }, pattern.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleApplyPattern,\n            disabled: loading || !selectedPatternId || !selectionStartDate || !selectionEndDate,\n            children: loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => {\n              setSelectionStartDate(null);\n              setSelectionEndDate(null);\n              setSelectedPatternId('');\n              setError('');\n            },\n            className: \"ms-2\",\n            children: \"Limpiar Selecci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n      activeKey: activeTab,\n      onSelect: setActiveTab,\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"calendar\",\n        title: \"\\uD83D\\uDCC5 Calendario Dual\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dual-calendar\",\n          children: /*#__PURE__*/_jsxDEV(DualCalendarView, {\n            onDateSelect: handleDateSelect,\n            selectedDate: selectedDate\n          }, calendarKey, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"analysis\",\n        title: \"\\uD83D\\uDCCA An\\xE1lisis\",\n        children: /*#__PURE__*/_jsxDEV(CalendarAnalysis, {\n          selectedDate: selectedDate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RealCalendarManager, {\n      show: showRealDayModal,\n      onHide: () => setShowRealDayModal(false),\n      selectedDate: selectedDate,\n      onDayUpdated: handleRealDayUpdated\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n}\n_s(CalendarPage, \"uITU9TjMMwqnQXFPHuKhrf0iCQM=\");\n_c = CalendarPage;\nexport default CalendarPage;\nvar _c;\n$RefreshReg$(_c, \"CalendarPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "<PERSON><PERSON>", "Modal", "Form", "<PERSON><PERSON>", "Badge", "Tabs", "Tab", "DualCalendarView", "RealCalendarManager", "CalendarAnalysis", "getDayByDate", "createOrUpdateDay", "getAllShifts", "applyShiftToDate", "suggestShiftsForDate", "applyPattern", "getAllPatterns", "clearPlannedCalendar", "jsxDEV", "_jsxDEV", "CalendarPage", "_s", "selectedDate", "setSelectedDate", "Date", "activeTab", "setActiveTab", "showRealDayModal", "setShowRealDayModal", "showPatternModal", "setShowPatternModal", "selectionStartDate", "setSelectionStartDate", "selectionEndDate", "setSelectionEndDate", "allPatterns", "setAllPatterns", "selectedPatternId", "setSelectedPatternId", "loading", "setLoading", "error", "setError", "calendarKey", "setCalendarKey", "loadAllPatterns", "patterns", "console", "handleDateSelect", "date", "handleRealDayUpdated", "prev", "handleClearPlannedCalendar", "window", "confirm", "err", "message", "handlePatternDateClick", "value", "fluid", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "size", "onClick", "disabled", "md", "Group", "Label", "Control", "type", "toISOString", "split", "onChange", "e", "target", "controlId", "Select", "map", "pattern", "id", "name", "handleApplyPattern", "active<PERSON><PERSON>", "onSelect", "eventKey", "title", "onDateSelect", "show", "onHide", "onDayUpdated", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/CalendarPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON>er, Row, Col, Button, Modal, Form, Alert, Badge, Tabs, Tab } from 'react-bootstrap';\nimport DualCalendarView from '../components/DualCalendarView';\nimport RealCalendarManager from '../components/RealCalendarManager';\nimport CalendarAnalysis from '../components/CalendarAnalysis';\nimport {\n  getDayByDate,\n  createOrUpdateDay,\n  getAllShifts,\n  applyShiftToDate,\n  suggestShiftsForDate,\n  applyPattern,\n  getAllPatterns,\n  clearPlannedCalendar\n} from '../services/api';\n\nfunction CalendarPage() {\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('calendar');\n\n  // Estados para modales\n  const [showRealDayModal, setShowRealDayModal] = useState(false);\n  const [showPatternModal, setShowPatternModal] = useState(false);\n\n  // Estados para aplicación de patrones\n  const [selectionStartDate, setSelectionStartDate] = useState(null);\n  const [selectionEndDate, setSelectionEndDate] = useState(null);\n  const [allPatterns, setAllPatterns] = useState([]);\n  const [selectedPatternId, setSelectedPatternId] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estado para forzar actualización del calendario\n  const [calendarKey, setCalendarKey] = useState(0);\n\n  useEffect(() => {\n    loadAllPatterns();\n  }, []);\n\n  const loadAllPatterns = async () => {\n    try {\n      const patterns = await getAllPatterns();\n      setAllPatterns(patterns);\n    } catch (error) {\n      console.error('Error loading patterns:', error);\n      setError('Error cargando patrones.');\n    }\n  };\n\n  const handleDateSelect = (date) => {\n    setSelectedDate(date);\n  };\n\n  const handleRealDayUpdated = () => {\n    // Forzar actualización del calendario\n    setCalendarKey(prev => prev + 1);\n  };\n\n  const handleClearPlannedCalendar = async () => {\n    if (window.confirm('¿Estás seguro de que quieres limpiar todo el calendario planificado?')) {\n      try {\n        setLoading(true);\n        await clearPlannedCalendar();\n        setCalendarKey(prev => prev + 1);\n        setError('');\n      } catch (err) {\n        setError('Error limpiando calendario planificado: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  const handlePatternDateClick = (value) => {\n    if (!selectionStartDate) {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    } else if (!selectionEndDate) {\n      if (value < selectionStartDate) {\n        setSelectionEndDate(selectionStartDate);\n        setSelectionStartDate(value);\n      } else {\n        setSelectionEndDate(value);\n      }\n    } else {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    }\n  };\n\n  \n\n  return (\n    <Container fluid>\n      <Row className=\"mb-3\">\n        <Col>\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <h2>Sistema de Horarios Dual</h2>\n            <div>\n              <Button\n                variant=\"outline-primary\"\n                size=\"sm\"\n                onClick={() => setShowRealDayModal(true)}\n                disabled={!selectedDate}\n                className=\"me-2\"\n              >\n                Registrar Día Real\n              </Button>\n              <Button\n                variant=\"outline-danger\"\n                size=\"sm\"\n                onClick={handleClearPlannedCalendar}\n                disabled={loading}\n              >\n                Limpiar Planificado\n              </Button>\n            </div>\n          </div>\n        </Col>\n      </Row>\n\n      <Row className=\"mb-3\">\n        <Col>\n          <h3>Aplicar Patrón a Rango Seleccionado</h3>\n          {error && (\n            <Alert variant=\"danger\" className=\"mb-3\">\n              {error}\n            </Alert>\n          )}\n          <Form>\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Fecha de inicio</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : ''}\n                    onChange={(e) => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group controlId=\"patternEndDate\">\n                  <Form.Label>Fecha de fin</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : ''}\n                    onChange={(e) => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Seleccionar Patrón</Form.Label>\n              <Form.Select\n                value={selectedPatternId}\n                onChange={(e) => setSelectedPatternId(e.target.value)}\n              >\n                <option value=\"\">Seleccionar patrón...</option>\n                {allPatterns.map((pattern) => (\n                  <option key={pattern.id} value={pattern.id}>\n                    {pattern.name}\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n\n            <Button\n              variant=\"primary\"\n              onClick={handleApplyPattern}\n              disabled={loading || !selectedPatternId || !selectionStartDate || !selectionEndDate}\n            >\n              {loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'}\n            </Button>\n            <Button\n              variant=\"secondary\"\n              onClick={() => {\n                setSelectionStartDate(null);\n                setSelectionEndDate(null);\n                setSelectedPatternId('');\n                setError('');\n              }}\n              className=\"ms-2\"\n            >\n              Limpiar Selección\n            </Button>\n          </Form>\n        </Col>\n      </Row>\n\n      <Row className=\"mb-3\">\n        <Col>\n          <h3>Aplicar Patrón a Rango Seleccionado</h3>\n          {error && (\n            <Alert variant=\"danger\" className=\"mb-3\">\n              {error}\n            </Alert>\n          )}\n          <Form>\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Fecha de inicio</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : ''}\n                    onChange={(e) => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Fecha de fin</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : ''}\n                    onChange={(e) => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Seleccionar Patrón</Form.Label>\n              <Form.Select\n                value={selectedPatternId}\n                onChange={(e) => setSelectedPatternId(e.target.value)}\n              >\n                <option value=\"\">Seleccionar patrón...</option>\n                {allPatterns.map((pattern) => (\n                  <option key={pattern.id} value={pattern.id}>\n                    {pattern.name}\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n\n            <Button\n              variant=\"primary\"\n              onClick={handleApplyPattern}\n              disabled={loading || !selectedPatternId || !selectionStartDate || !selectionEndDate}\n            >\n              {loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'}\n            </Button>\n            <Button\n              variant=\"secondary\"\n              onClick={() => {\n                setSelectionStartDate(null);\n                setSelectionEndDate(null);\n                setSelectedPatternId('');\n                setError('');\n              }}\n              className=\"ms-2\"\n            >\n              Limpiar Selección\n            </Button>\n          </Form>\n        </Col>\n      </Row>\n\n      {error && (\n        <Row className=\"mb-3\">\n          <Col>\n            <Alert variant=\"danger\">{error}</Alert>\n          </Col>\n        </Row>\n      )}\n\n      <Tabs activeKey={activeTab} onSelect={setActiveTab} className=\"mb-3\">\n        <Tab eventKey=\"calendar\" title=\"📅 Calendario Dual\">\n          <div className=\"dual-calendar\">\n            <DualCalendarView\n              key={calendarKey}\n              onDateSelect={handleDateSelect}\n              selectedDate={selectedDate}\n            />\n          </div>\n        </Tab>\n\n        <Tab eventKey=\"analysis\" title=\"📊 Análisis\">\n          <CalendarAnalysis selectedDate={selectedDate} />\n        </Tab>\n      </Tabs>\n\n      {/* Modal para gestión de días reales */}\n      <RealCalendarManager\n        show={showRealDayModal}\n        onHide={() => setShowRealDayModal(false)}\n        selectedDate={selectedDate}\n        onDayUpdated={handleRealDayUpdated}\n      />\n\n      \n    </Container>\n  );\n}\n\nexport default CalendarPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,QAAQ,iBAAiB;AACnG,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,SACEC,YAAY,EACZC,iBAAiB,EACjBC,YAAY,EACZC,gBAAgB,EAChBC,oBAAoB,EACpBC,YAAY,EACZC,cAAc,EACdC,oBAAoB,QACf,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI6B,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,UAAU,CAAC;;EAEtD;EACA,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACoC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACsC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACdiD,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM9B,cAAc,CAAC,CAAC;MACvCoB,cAAc,CAACU,QAAQ,CAAC;IAC1B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,0BAA0B,CAAC;IACtC;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAIC,IAAI,IAAK;IACjC1B,eAAe,CAAC0B,IAAI,CAAC;EACvB,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAN,cAAc,CAACO,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;EAClC,CAAC;EAED,MAAMC,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAIC,MAAM,CAACC,OAAO,CAAC,sEAAsE,CAAC,EAAE;MAC1F,IAAI;QACFd,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMvB,oBAAoB,CAAC,CAAC;QAC5B2B,cAAc,CAACO,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAChCT,QAAQ,CAAC,EAAE,CAAC;MACd,CAAC,CAAC,OAAOa,GAAG,EAAE;QACZb,QAAQ,CAAC,0CAA0C,GAAGa,GAAG,CAACC,OAAO,CAAC;MACpE,CAAC,SAAS;QACRhB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;EAED,MAAMiB,sBAAsB,GAAIC,KAAK,IAAK;IACxC,IAAI,CAAC3B,kBAAkB,EAAE;MACvBC,qBAAqB,CAAC0B,KAAK,CAAC;MAC5BxB,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM,IAAI,CAACD,gBAAgB,EAAE;MAC5B,IAAIyB,KAAK,GAAG3B,kBAAkB,EAAE;QAC9BG,mBAAmB,CAACH,kBAAkB,CAAC;QACvCC,qBAAqB,CAAC0B,KAAK,CAAC;MAC9B,CAAC,MAAM;QACLxB,mBAAmB,CAACwB,KAAK,CAAC;MAC5B;IACF,CAAC,MAAM;MACL1B,qBAAqB,CAAC0B,KAAK,CAAC;MAC5BxB,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAID,oBACEf,OAAA,CAACtB,SAAS;IAAC8D,KAAK;IAAAC,QAAA,gBACdzC,OAAA,CAACrB,GAAG;MAAC+D,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBzC,OAAA,CAACpB,GAAG;QAAA6D,QAAA,eACFzC,OAAA;UAAK0C,SAAS,EAAC,mDAAmD;UAAAD,QAAA,gBAChEzC,OAAA;YAAAyC,QAAA,EAAI;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjC9C,OAAA;YAAAyC,QAAA,gBACEzC,OAAA,CAACnB,MAAM;cACLkE,OAAO,EAAC,iBAAiB;cACzBC,IAAI,EAAC,IAAI;cACTC,OAAO,EAAEA,CAAA,KAAMxC,mBAAmB,CAAC,IAAI,CAAE;cACzCyC,QAAQ,EAAE,CAAC/C,YAAa;cACxBuC,SAAS,EAAC,MAAM;cAAAD,QAAA,EACjB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9C,OAAA,CAACnB,MAAM;cACLkE,OAAO,EAAC,gBAAgB;cACxBC,IAAI,EAAC,IAAI;cACTC,OAAO,EAAEhB,0BAA2B;cACpCiB,QAAQ,EAAE9B,OAAQ;cAAAqB,QAAA,EACnB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN9C,OAAA,CAACrB,GAAG;MAAC+D,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBzC,OAAA,CAACpB,GAAG;QAAA6D,QAAA,gBACFzC,OAAA;UAAAyC,QAAA,EAAI;QAAmC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC3CxB,KAAK,iBACJtB,OAAA,CAAChB,KAAK;UAAC+D,OAAO,EAAC,QAAQ;UAACL,SAAS,EAAC,MAAM;UAAAD,QAAA,EACrCnB;QAAK;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eACD9C,OAAA,CAACjB,IAAI;UAAA0D,QAAA,gBACHzC,OAAA,CAACrB,GAAG;YAAA8D,QAAA,gBACFzC,OAAA,CAACpB,GAAG;cAACuE,EAAE,EAAE,CAAE;cAAAV,QAAA,eACTzC,OAAA,CAACjB,IAAI,CAACqE,KAAK;gBAACV,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BzC,OAAA,CAACjB,IAAI,CAACsE,KAAK;kBAAAZ,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxC9C,OAAA,CAACjB,IAAI,CAACuE,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXhB,KAAK,EAAE3B,kBAAkB,GAAGA,kBAAkB,CAAC4C,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;kBAChFC,QAAQ,EAAGC,CAAC,IAAK9C,qBAAqB,CAAC8C,CAAC,CAACC,MAAM,CAACrB,KAAK,GAAG,IAAIlC,IAAI,CAACsD,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAC,GAAG,IAAI;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN9C,OAAA,CAACpB,GAAG;cAACuE,EAAE,EAAE,CAAE;cAAAV,QAAA,eACTzC,OAAA,CAACjB,IAAI,CAACqE,KAAK;gBAACS,SAAS,EAAC,gBAAgB;gBAAApB,QAAA,gBACpCzC,OAAA,CAACjB,IAAI,CAACsE,KAAK;kBAAAZ,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrC9C,OAAA,CAACjB,IAAI,CAACuE,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXhB,KAAK,EAAEzB,gBAAgB,GAAGA,gBAAgB,CAAC0C,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;kBAC5EC,QAAQ,EAAGC,CAAC,IAAK5C,mBAAmB,CAAC4C,CAAC,CAACC,MAAM,CAACrB,KAAK,GAAG,IAAIlC,IAAI,CAACsD,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAC,GAAG,IAAI;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9C,OAAA,CAACjB,IAAI,CAACqE,KAAK;YAACV,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BzC,OAAA,CAACjB,IAAI,CAACsE,KAAK;cAAAZ,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3C9C,OAAA,CAACjB,IAAI,CAAC+E,MAAM;cACVvB,KAAK,EAAErB,iBAAkB;cACzBwC,QAAQ,EAAGC,CAAC,IAAKxC,oBAAoB,CAACwC,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;cAAAE,QAAA,gBAEtDzC,OAAA;gBAAQuC,KAAK,EAAC,EAAE;gBAAAE,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9C9B,WAAW,CAAC+C,GAAG,CAAEC,OAAO,iBACvBhE,OAAA;gBAAyBuC,KAAK,EAAEyB,OAAO,CAACC,EAAG;gBAAAxB,QAAA,EACxCuB,OAAO,CAACE;cAAI,GADFF,OAAO,CAACC,EAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEb9C,OAAA,CAACnB,MAAM;YACLkE,OAAO,EAAC,SAAS;YACjBE,OAAO,EAAEkB,kBAAmB;YAC5BjB,QAAQ,EAAE9B,OAAO,IAAI,CAACF,iBAAiB,IAAI,CAACN,kBAAkB,IAAI,CAACE,gBAAiB;YAAA2B,QAAA,EAEnFrB,OAAO,GAAG,cAAc,GAAG;UAAwB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACT9C,OAAA,CAACnB,MAAM;YACLkE,OAAO,EAAC,WAAW;YACnBE,OAAO,EAAEA,CAAA,KAAM;cACbpC,qBAAqB,CAAC,IAAI,CAAC;cAC3BE,mBAAmB,CAAC,IAAI,CAAC;cACzBI,oBAAoB,CAAC,EAAE,CAAC;cACxBI,QAAQ,CAAC,EAAE,CAAC;YACd,CAAE;YACFmB,SAAS,EAAC,MAAM;YAAAD,QAAA,EACjB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN9C,OAAA,CAACrB,GAAG;MAAC+D,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBzC,OAAA,CAACpB,GAAG;QAAA6D,QAAA,gBACFzC,OAAA;UAAAyC,QAAA,EAAI;QAAmC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC3CxB,KAAK,iBACJtB,OAAA,CAAChB,KAAK;UAAC+D,OAAO,EAAC,QAAQ;UAACL,SAAS,EAAC,MAAM;UAAAD,QAAA,EACrCnB;QAAK;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eACD9C,OAAA,CAACjB,IAAI;UAAA0D,QAAA,gBACHzC,OAAA,CAACrB,GAAG;YAAA8D,QAAA,gBACFzC,OAAA,CAACpB,GAAG;cAACuE,EAAE,EAAE,CAAE;cAAAV,QAAA,eACTzC,OAAA,CAACjB,IAAI,CAACqE,KAAK;gBAACV,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BzC,OAAA,CAACjB,IAAI,CAACsE,KAAK;kBAAAZ,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxC9C,OAAA,CAACjB,IAAI,CAACuE,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXhB,KAAK,EAAE3B,kBAAkB,GAAGA,kBAAkB,CAAC4C,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;kBAChFC,QAAQ,EAAGC,CAAC,IAAK9C,qBAAqB,CAAC8C,CAAC,CAACC,MAAM,CAACrB,KAAK,GAAG,IAAIlC,IAAI,CAACsD,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAC,GAAG,IAAI;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN9C,OAAA,CAACpB,GAAG;cAACuE,EAAE,EAAE,CAAE;cAAAV,QAAA,eACTzC,OAAA,CAACjB,IAAI,CAACqE,KAAK;gBAACV,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BzC,OAAA,CAACjB,IAAI,CAACsE,KAAK;kBAAAZ,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrC9C,OAAA,CAACjB,IAAI,CAACuE,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXhB,KAAK,EAAEzB,gBAAgB,GAAGA,gBAAgB,CAAC0C,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;kBAC5EC,QAAQ,EAAGC,CAAC,IAAK5C,mBAAmB,CAAC4C,CAAC,CAACC,MAAM,CAACrB,KAAK,GAAG,IAAIlC,IAAI,CAACsD,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAC,GAAG,IAAI;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9C,OAAA,CAACjB,IAAI,CAACqE,KAAK;YAACV,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BzC,OAAA,CAACjB,IAAI,CAACsE,KAAK;cAAAZ,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3C9C,OAAA,CAACjB,IAAI,CAAC+E,MAAM;cACVvB,KAAK,EAAErB,iBAAkB;cACzBwC,QAAQ,EAAGC,CAAC,IAAKxC,oBAAoB,CAACwC,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;cAAAE,QAAA,gBAEtDzC,OAAA;gBAAQuC,KAAK,EAAC,EAAE;gBAAAE,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9C9B,WAAW,CAAC+C,GAAG,CAAEC,OAAO,iBACvBhE,OAAA;gBAAyBuC,KAAK,EAAEyB,OAAO,CAACC,EAAG;gBAAAxB,QAAA,EACxCuB,OAAO,CAACE;cAAI,GADFF,OAAO,CAACC,EAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEb9C,OAAA,CAACnB,MAAM;YACLkE,OAAO,EAAC,SAAS;YACjBE,OAAO,EAAEkB,kBAAmB;YAC5BjB,QAAQ,EAAE9B,OAAO,IAAI,CAACF,iBAAiB,IAAI,CAACN,kBAAkB,IAAI,CAACE,gBAAiB;YAAA2B,QAAA,EAEnFrB,OAAO,GAAG,cAAc,GAAG;UAAwB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACT9C,OAAA,CAACnB,MAAM;YACLkE,OAAO,EAAC,WAAW;YACnBE,OAAO,EAAEA,CAAA,KAAM;cACbpC,qBAAqB,CAAC,IAAI,CAAC;cAC3BE,mBAAmB,CAAC,IAAI,CAAC;cACzBI,oBAAoB,CAAC,EAAE,CAAC;cACxBI,QAAQ,CAAC,EAAE,CAAC;YACd,CAAE;YACFmB,SAAS,EAAC,MAAM;YAAAD,QAAA,EACjB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELxB,KAAK,iBACJtB,OAAA,CAACrB,GAAG;MAAC+D,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBzC,OAAA,CAACpB,GAAG;QAAA6D,QAAA,eACFzC,OAAA,CAAChB,KAAK;UAAC+D,OAAO,EAAC,QAAQ;UAAAN,QAAA,EAAEnB;QAAK;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED9C,OAAA,CAACd,IAAI;MAACkF,SAAS,EAAE9D,SAAU;MAAC+D,QAAQ,EAAE9D,YAAa;MAACmC,SAAS,EAAC,MAAM;MAAAD,QAAA,gBAClEzC,OAAA,CAACb,GAAG;QAACmF,QAAQ,EAAC,UAAU;QAACC,KAAK,EAAC,8BAAoB;QAAA9B,QAAA,eACjDzC,OAAA;UAAK0C,SAAS,EAAC,eAAe;UAAAD,QAAA,eAC5BzC,OAAA,CAACZ,gBAAgB;YAEfoF,YAAY,EAAE3C,gBAAiB;YAC/B1B,YAAY,EAAEA;UAAa,GAFtBqB,WAAW;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9C,OAAA,CAACb,GAAG;QAACmF,QAAQ,EAAC,UAAU;QAACC,KAAK,EAAC,0BAAa;QAAA9B,QAAA,eAC1CzC,OAAA,CAACV,gBAAgB;UAACa,YAAY,EAAEA;QAAa;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP9C,OAAA,CAACX,mBAAmB;MAClBoF,IAAI,EAAEjE,gBAAiB;MACvBkE,MAAM,EAAEA,CAAA,KAAMjE,mBAAmB,CAAC,KAAK,CAAE;MACzCN,YAAY,EAAEA,YAAa;MAC3BwE,YAAY,EAAE5C;IAAqB;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGO,CAAC;AAEhB;AAAC5C,EAAA,CAxRQD,YAAY;AAAA2E,EAAA,GAAZ3E,YAAY;AA0RrB,eAAeA,YAAY;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}