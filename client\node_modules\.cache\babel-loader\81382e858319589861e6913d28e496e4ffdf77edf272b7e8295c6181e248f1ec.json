{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"LinkContainer\", {\n  enumerable: true,\n  get: function get() {\n    return _LinkContainer[\"default\"];\n  }\n});\nvar _LinkContainer = _interopRequireDefault(require(\"./LinkContainer\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "enumerable", "get", "_LinkContainer", "_interopRequireDefault", "require", "obj", "__esModule"], "sources": ["D:/Proyectos Python/Horario/client/node_modules/react-router-bootstrap/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"LinkContainer\", {\n  enumerable: true,\n  get: function get() {\n    return _LinkContainer[\"default\"];\n  }\n});\n\nvar _LinkContainer = _interopRequireDefault(require(\"./LinkContainer\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFH,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,eAAe,EAAE;EAC9CE,UAAU,EAAE,IAAI;EAChBC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,OAAOC,cAAc,CAAC,SAAS,CAAC;EAClC;AACF,CAAC,CAAC;AAEF,IAAIA,cAAc,GAAGC,sBAAsB,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAEvE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}