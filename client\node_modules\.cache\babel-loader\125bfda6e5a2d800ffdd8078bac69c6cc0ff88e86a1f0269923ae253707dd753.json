{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:5000/api/days';\nconst SHIFTS_API_URL = 'http://localhost:5000/api/shifts';\nconst PATTERNS_API_URL = 'http://localhost:5000/api/patterns';\nexport const getAllDays = async () => {\n  try {\n    const response = await axios.get(API_URL);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all days:', error);\n    throw error;\n  }\n};\nexport const getDayByDate = async date => {\n  try {\n    const response = await axios.get(`${API_URL}/${date}`);\n    return response.data;\n  } catch (error) {\n    if (error.response && error.response.status === 404) {\n      return null; // Day not found, return null instead of throwing error\n    }\n    console.error(`Error fetching day for ${date}:`, error);\n    throw error;\n  }\n};\nexport const createOrUpdateDay = async (date, type, hours, description) => {\n  try {\n    const response = await axios.post(API_URL, {\n      date,\n      type,\n      hours,\n      description\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error creating or updating day:', error);\n    throw error;\n  }\n};\n\n// ===== FUNCIONES PARA TURNOS PREDEFINIDOS =====\n\n/**\n * Obtiene todos los turnos predefinidos\n */\nexport const getAllShifts = async () => {\n  try {\n    const response = await axios.get(SHIFTS_API_URL);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all shifts:', error);\n    throw error;\n  }\n};\n\n/**\n * Obtiene un turno específico por ID\n */\nexport const getShiftById = async shiftId => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/${shiftId}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error fetching shift ${shiftId}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un turno predefinido a una fecha específica\n */\nexport const applyShiftToDate = async (date, shiftId, description = '') => {\n  try {\n    const response = await axios.post(`${SHIFTS_API_URL}/apply`, {\n      date,\n      shiftId,\n      description\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying shift to date:', error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un turno a múltiples fechas\n */\nexport const applyShiftToMultipleDates = async (dates, shiftId, description = '') => {\n  try {\n    const response = await axios.post(`${SHIFTS_API_URL}/apply-multiple`, {\n      dates,\n      shiftId,\n      description\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying shift to multiple dates:', error);\n    throw error;\n  }\n};\n\n/**\n * Obtiene estadísticas de turnos aplicados\n */\nexport const getShiftStatistics = async () => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/statistics`);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching shift statistics:', error);\n    throw error;\n  }\n};\n\n/**\n * Sugiere turnos apropiados para una fecha\n */\nexport const suggestShiftsForDate = async date => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/suggest/${date}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error suggesting shifts for date ${date}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un patrón de días a un rango de fechas.\n */\nexport const applyPattern = async (startDate, endDate, pattern) => {\n  try {\n    const response = await axios.post(`${API_URL}/apply-pattern`, {\n      startDate,\n      endDate,\n      pattern\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying pattern:', error);\n    throw error;\n  }\n};\n\n// ===== FUNCIONES PARA PATRONES =====\n\nexport const getAllPatterns = async () => {\n  try {\n    const response = await axios.get(PATTERNS_API_URL);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all patterns:', error);\n    throw error;\n  }\n};\nexport const getPatternById = async id => {\n  try {\n    const response = await axios.get(`${PATTERNS_API_URL}/${id}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error fetching pattern ${id}:`, error);\n    throw error;\n  }\n};\nexport const createPattern = async (name, patternDays) => {\n  try {\n    const response = await axios.post(PATTERNS_API_URL, {\n      name,\n      patternDays\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error creating pattern:', error);\n    throw error;\n  }\n};\nexport const updatePattern = async (id, name, patternDays) => {\n  try {\n    const response = await axios.put(`${PATTERNS_API_URL}/${id}`, {\n      name,\n      patternDays\n    });\n    return response.data;\n  } catch (error) {\n    console.error(`Error updating pattern ${id}:`, error);\n    throw error;\n  }\n};\nexport const deletePattern = async id => {\n  try {\n    await axios.delete(`${PATTERNS_API_URL}/${id}`);\n    return {\n      message: 'Pattern deleted successfully'\n    };\n  } catch (error) {\n    console.error(`Error deleting pattern ${id}:`, error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["axios", "API_URL", "SHIFTS_API_URL", "PATTERNS_API_URL", "getAllDays", "response", "get", "data", "error", "console", "getDayByDate", "date", "status", "createOrUpdateDay", "type", "hours", "description", "post", "getAllShifts", "getShiftById", "shiftId", "applyShiftToDate", "applyShiftToMultipleDates", "dates", "getShiftStatistics", "suggestShiftsForDate", "applyPattern", "startDate", "endDate", "pattern", "getAllPatterns", "getPatternById", "id", "createPattern", "name", "patternDays", "updatePattern", "put", "deletePattern", "delete", "message"], "sources": ["D:/Proyectos Python/Horario/client/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:5000/api/days';\nconst SHIFTS_API_URL = 'http://localhost:5000/api/shifts';\nconst PATTERNS_API_URL = 'http://localhost:5000/api/patterns';\n\nexport const getAllDays = async () => {\n  try {\n    const response = await axios.get(API_URL);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all days:', error);\n    throw error;\n  }\n};\n\nexport const getDayByDate = async (date) => {\n  try {\n    const response = await axios.get(`${API_URL}/${date}`);\n    return response.data;\n  } catch (error) {\n    if (error.response && error.response.status === 404) {\n      return null; // Day not found, return null instead of throwing error\n    }\n    console.error(`Error fetching day for ${date}:`, error);\n    throw error;\n  }\n};\n\nexport const createOrUpdateDay = async (date, type, hours, description) => {\n  try {\n    const response = await axios.post(API_URL, { date, type, hours, description });\n    return response.data;\n  } catch (error) {\n    console.error('Error creating or updating day:', error);\n    throw error;\n  }\n};\n\n// ===== FUNCIONES PARA TURNOS PREDEFINIDOS =====\n\n/**\n * Obtiene todos los turnos predefinidos\n */\nexport const getAllShifts = async () => {\n  try {\n    const response = await axios.get(SHIFTS_API_URL);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all shifts:', error);\n    throw error;\n  }\n};\n\n/**\n * Obtiene un turno específico por ID\n */\nexport const getShiftById = async (shiftId) => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/${shiftId}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error fetching shift ${shiftId}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un turno predefinido a una fecha específica\n */\nexport const applyShiftToDate = async (date, shiftId, description = '') => {\n  try {\n    const response = await axios.post(`${SHIFTS_API_URL}/apply`, {\n      date,\n      shiftId,\n      description\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying shift to date:', error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un turno a múltiples fechas\n */\nexport const applyShiftToMultipleDates = async (dates, shiftId, description = '') => {\n  try {\n    const response = await axios.post(`${SHIFTS_API_URL}/apply-multiple`, {\n      dates,\n      shiftId,\n      description\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying shift to multiple dates:', error);\n    throw error;\n  }\n};\n\n/**\n * Obtiene estadísticas de turnos aplicados\n */\nexport const getShiftStatistics = async () => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/statistics`);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching shift statistics:', error);\n    throw error;\n  }\n};\n\n/**\n * Sugiere turnos apropiados para una fecha\n */\nexport const suggestShiftsForDate = async (date) => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/suggest/${date}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error suggesting shifts for date ${date}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un patrón de días a un rango de fechas.\n */\nexport const applyPattern = async (startDate, endDate, pattern) => {\n  try {\n    const response = await axios.post(`${API_URL}/apply-pattern`, { startDate, endDate, pattern });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying pattern:', error);\n    throw error;\n  }\n};\n\n// ===== FUNCIONES PARA PATRONES =====\n\nexport const getAllPatterns = async () => {\n  try {\n    const response = await axios.get(PATTERNS_API_URL);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all patterns:', error);\n    throw error;\n  }\n};\n\nexport const getPatternById = async (id) => {\n  try {\n    const response = await axios.get(`${PATTERNS_API_URL}/${id}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error fetching pattern ${id}:`, error);\n    throw error;\n  }\n};\n\nexport const createPattern = async (name, patternDays) => {\n  try {\n    const response = await axios.post(PATTERNS_API_URL, { name, patternDays });\n    return response.data;\n  } catch (error) {\n    console.error('Error creating pattern:', error);\n    throw error;\n  }\n};\n\nexport const updatePattern = async (id, name, patternDays) => {\n  try {\n    const response = await axios.put(`${PATTERNS_API_URL}/${id}`, { name, patternDays });\n    return response.data;\n  } catch (error) {\n    console.error(`Error updating pattern ${id}:`, error);\n    throw error;\n  }\n};\n\nexport const deletePattern = async (id) => {\n  try {\n    await axios.delete(`${PATTERNS_API_URL}/${id}`);\n    return { message: 'Pattern deleted successfully' };\n  } catch (error) {\n    console.error(`Error deleting pattern ${id}:`, error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,gCAAgC;AAChD,MAAMC,cAAc,GAAG,kCAAkC;AACzD,MAAMC,gBAAgB,GAAG,oCAAoC;AAE7D,OAAO,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;EACpC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAML,KAAK,CAACM,GAAG,CAACL,OAAO,CAAC;IACzC,OAAOI,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAME,YAAY,GAAG,MAAOC,IAAI,IAAK;EAC1C,IAAI;IACF,MAAMN,QAAQ,GAAG,MAAML,KAAK,CAACM,GAAG,CAAC,GAAGL,OAAO,IAAIU,IAAI,EAAE,CAAC;IACtD,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,IAAIA,KAAK,CAACH,QAAQ,IAAIG,KAAK,CAACH,QAAQ,CAACO,MAAM,KAAK,GAAG,EAAE;MACnD,OAAO,IAAI,CAAC,CAAC;IACf;IACAH,OAAO,CAACD,KAAK,CAAC,0BAA0BG,IAAI,GAAG,EAAEH,KAAK,CAAC;IACvD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMK,iBAAiB,GAAG,MAAAA,CAAOF,IAAI,EAAEG,IAAI,EAAEC,KAAK,EAAEC,WAAW,KAAK;EACzE,IAAI;IACF,MAAMX,QAAQ,GAAG,MAAML,KAAK,CAACiB,IAAI,CAAChB,OAAO,EAAE;MAAEU,IAAI;MAAEG,IAAI;MAAEC,KAAK;MAAEC;IAAY,CAAC,CAAC;IAC9E,OAAOX,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACvD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;;AAEA;AACA;AACA;AACA,OAAO,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;EACtC,IAAI;IACF,MAAMb,QAAQ,GAAG,MAAML,KAAK,CAACM,GAAG,CAACJ,cAAc,CAAC;IAChD,OAAOG,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMW,YAAY,GAAG,MAAOC,OAAO,IAAK;EAC7C,IAAI;IACF,MAAMf,QAAQ,GAAG,MAAML,KAAK,CAACM,GAAG,CAAC,GAAGJ,cAAc,IAAIkB,OAAO,EAAE,CAAC;IAChE,OAAOf,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,wBAAwBY,OAAO,GAAG,EAAEZ,KAAK,CAAC;IACxD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMa,gBAAgB,GAAG,MAAAA,CAAOV,IAAI,EAAES,OAAO,EAAEJ,WAAW,GAAG,EAAE,KAAK;EACzE,IAAI;IACF,MAAMX,QAAQ,GAAG,MAAML,KAAK,CAACiB,IAAI,CAAC,GAAGf,cAAc,QAAQ,EAAE;MAC3DS,IAAI;MACJS,OAAO;MACPJ;IACF,CAAC,CAAC;IACF,OAAOX,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMc,yBAAyB,GAAG,MAAAA,CAAOC,KAAK,EAAEH,OAAO,EAAEJ,WAAW,GAAG,EAAE,KAAK;EACnF,IAAI;IACF,MAAMX,QAAQ,GAAG,MAAML,KAAK,CAACiB,IAAI,CAAC,GAAGf,cAAc,iBAAiB,EAAE;MACpEqB,KAAK;MACLH,OAAO;MACPJ;IACF,CAAC,CAAC;IACF,OAAOX,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IAC/D,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMgB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;EAC5C,IAAI;IACF,MAAMnB,QAAQ,GAAG,MAAML,KAAK,CAACM,GAAG,CAAC,GAAGJ,cAAc,aAAa,CAAC;IAChE,OAAOG,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMiB,oBAAoB,GAAG,MAAOd,IAAI,IAAK;EAClD,IAAI;IACF,MAAMN,QAAQ,GAAG,MAAML,KAAK,CAACM,GAAG,CAAC,GAAGJ,cAAc,YAAYS,IAAI,EAAE,CAAC;IACrE,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,oCAAoCG,IAAI,GAAG,EAAEH,KAAK,CAAC;IACjE,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMkB,YAAY,GAAG,MAAAA,CAAOC,SAAS,EAAEC,OAAO,EAAEC,OAAO,KAAK;EACjE,IAAI;IACF,MAAMxB,QAAQ,GAAG,MAAML,KAAK,CAACiB,IAAI,CAAC,GAAGhB,OAAO,gBAAgB,EAAE;MAAE0B,SAAS;MAAEC,OAAO;MAAEC;IAAQ,CAAC,CAAC;IAC9F,OAAOxB,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;;AAEA,OAAO,MAAMsB,cAAc,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMzB,QAAQ,GAAG,MAAML,KAAK,CAACM,GAAG,CAACH,gBAAgB,CAAC;IAClD,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMuB,cAAc,GAAG,MAAOC,EAAE,IAAK;EAC1C,IAAI;IACF,MAAM3B,QAAQ,GAAG,MAAML,KAAK,CAACM,GAAG,CAAC,GAAGH,gBAAgB,IAAI6B,EAAE,EAAE,CAAC;IAC7D,OAAO3B,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0BwB,EAAE,GAAG,EAAExB,KAAK,CAAC;IACrD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMyB,aAAa,GAAG,MAAAA,CAAOC,IAAI,EAAEC,WAAW,KAAK;EACxD,IAAI;IACF,MAAM9B,QAAQ,GAAG,MAAML,KAAK,CAACiB,IAAI,CAACd,gBAAgB,EAAE;MAAE+B,IAAI;MAAEC;IAAY,CAAC,CAAC;IAC1E,OAAO9B,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAM4B,aAAa,GAAG,MAAAA,CAAOJ,EAAE,EAAEE,IAAI,EAAEC,WAAW,KAAK;EAC5D,IAAI;IACF,MAAM9B,QAAQ,GAAG,MAAML,KAAK,CAACqC,GAAG,CAAC,GAAGlC,gBAAgB,IAAI6B,EAAE,EAAE,EAAE;MAAEE,IAAI;MAAEC;IAAY,CAAC,CAAC;IACpF,OAAO9B,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0BwB,EAAE,GAAG,EAAExB,KAAK,CAAC;IACrD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAM8B,aAAa,GAAG,MAAON,EAAE,IAAK;EACzC,IAAI;IACF,MAAMhC,KAAK,CAACuC,MAAM,CAAC,GAAGpC,gBAAgB,IAAI6B,EAAE,EAAE,CAAC;IAC/C,OAAO;MAAEQ,OAAO,EAAE;IAA+B,CAAC;EACpD,CAAC,CAAC,OAAOhC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0BwB,EAAE,GAAG,EAAExB,KAAK,CAAC;IACrD,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}