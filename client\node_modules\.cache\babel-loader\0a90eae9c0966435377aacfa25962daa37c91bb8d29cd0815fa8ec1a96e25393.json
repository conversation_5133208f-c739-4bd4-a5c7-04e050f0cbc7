{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\components\\\\PlannedCalendarView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Button, Alert, Form, Modal } from 'react-bootstrap';\nimport Calendar from 'react-calendar';\nimport { getAllPlannedDays, getAllPatterns, applyPattern, clearPlannedCalendar, deletePlannedDay } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction PlannedCalendarView({\n  onDateSelect,\n  selectedDate,\n  onCalendarUpdate\n}) {\n  _s();\n  const [plannedDays, setPlannedDays] = useState([]);\n  const [patterns, setPatterns] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estados para aplicación de patrones\n  const [showPatternModal, setShowPatternModal] = useState(false);\n  const [selectedPatternId, setSelectedPatternId] = useState('');\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n  useEffect(() => {\n    loadPlannedDays();\n    loadPatterns();\n  }, []);\n  const loadPlannedDays = async () => {\n    try {\n      setLoading(true);\n      const days = await getAllPlannedDays();\n      setPlannedDays(days);\n      setError('');\n    } catch (err) {\n      console.error('Error loading planned days:', err);\n      setError('Error cargando días planificados: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadPatterns = async () => {\n    try {\n      const patternsData = await getAllPatterns();\n      setPatterns(patternsData);\n    } catch (err) {\n      console.error('Error loading patterns:', err);\n    }\n  };\n  const handleApplyPattern = async () => {\n    if (!selectedPatternId || !startDate || !endDate) {\n      setError('Todos los campos son obligatorios');\n      return;\n    }\n    try {\n      setLoading(true);\n      setError('');\n      await applyPattern(selectedPatternId, startDate, endDate);\n      await loadPlannedDays();\n      onCalendarUpdate();\n      setShowPatternModal(false);\n      resetPatternForm();\n    } catch (err) {\n      setError('Error aplicando patrón: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleClearCalendar = async () => {\n    if (window.confirm('¿Estás seguro de que quieres limpiar todo el calendario planificado?')) {\n      try {\n        setLoading(true);\n        await clearPlannedCalendar();\n        await loadPlannedDays();\n        onCalendarUpdate();\n        setError('');\n      } catch (err) {\n        setError('Error limpiando calendario: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  const handleDeleteDay = async dayId => {\n    if (window.confirm('¿Eliminar este día del calendario planificado?')) {\n      try {\n        setLoading(true);\n        await deletePlannedDay(dayId);\n        await loadPlannedDays();\n        onCalendarUpdate();\n      } catch (err) {\n        setError('Error eliminando día: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  const resetPatternForm = () => {\n    setSelectedPatternId('');\n    setStartDate('');\n    setEndDate('');\n  };\n  const getTileContent = ({\n    date,\n    view\n  }) => {\n    if (view !== 'month') return null;\n    const dayData = plannedDays.find(day => new Date(day.date).toDateString() === date.toDateString());\n    if (dayData) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"calendar-tile-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"badge bg-secondary\",\n          children: [dayData.hours, \"h\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), dayData.shiftName && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"shift-name\",\n          children: dayData.shiftName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const getTileClassName = ({\n    date,\n    view\n  }) => {\n    if (view !== 'month') return '';\n    const dayData = plannedDays.find(day => new Date(day.date).toDateString() === date.toDateString());\n    if (dayData) {\n      return 'has-planned-work';\n    }\n    return '';\n  };\n  const getTotalHours = () => {\n    return plannedDays.reduce((total, day) => total + (day.hours || 0), 0);\n  };\n  const getCurrentMonthHours = () => {\n    const currentMonth = new Date().getMonth();\n    const currentYear = new Date().getFullYear();\n    return plannedDays.filter(day => {\n      const dayDate = new Date(day.date);\n      return dayDate.getMonth() === currentMonth && dayDate.getFullYear() === currentYear;\n    }).reduce((total, day) => total + (day.hours || 0), 0);\n  };\n  const getSelectedDayData = () => {\n    if (!selectedDate) return null;\n    return plannedDays.find(day => new Date(day.date).toDateString() === selectedDate.toDateString());\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Calendario Te\\xF3rico (Planificado)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"success\",\n              onClick: () => setShowPatternModal(true),\n              className: \"me-2\",\n              children: \"\\uD83D\\uDCCB Aplicar Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-danger\",\n              onClick: handleClearCalendar,\n              disabled: loading,\n              children: \"\\uD83D\\uDDD1\\uFE0F Limpiar Todo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          onClose: () => setError(''),\n          dismissible: true,\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"calendar-container\",\n          children: /*#__PURE__*/_jsxDEV(Calendar, {\n            onChange: onDateSelect,\n            value: selectedDate,\n            tileContent: getTileContent,\n            tileClassName: getTileClassName,\n            locale: \"es-ES\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 4,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"calendar-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Estad\\xEDsticas Planificadas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total de d\\xEDas planificados:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), \" \", plannedDays.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total de horas:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), \" \", getTotalHours().toFixed(1), \"h\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Horas este mes:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), \" \", getCurrentMonthHours().toFixed(1), \"h\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Promedio por d\\xEDa:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), \" \", plannedDays.length > 0 ? (getTotalHours() / plannedDays.length).toFixed(1) : 0, \"h\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), selectedDate && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"selected-date-info mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Fecha seleccionada:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: selectedDate.toLocaleDateString('es-ES', {\n              weekday: 'long',\n              year: 'numeric',\n              month: 'long',\n              day: 'numeric'\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this), (() => {\n            const dayData = getSelectedDayData();\n            return dayData ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"day-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Horas planificadas:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 24\n                }, this), \" \", dayData.hours, \"h\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 21\n              }, this), dayData.shiftName && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Turno:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 46\n                }, this), \" \", dayData.shiftName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 43\n              }, this), dayData.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descripci\\xF3n:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 48\n                }, this), \" \", dayData.description]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-danger\",\n                size: \"sm\",\n                onClick: () => handleDeleteDay(dayData.id),\n                disabled: loading,\n                children: \"\\uD83D\\uDDD1\\uFE0F Eliminar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"No hay planificaci\\xF3n para este d\\xEDa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 19\n            }, this);\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pattern-info mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Patrones disponibles:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), patterns.length > 0 ? /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"list-unstyled\",\n            children: patterns.map(pattern => /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"mb-1\",\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: pattern.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 23\n                }, this), \" (\", pattern.days.length, \" d\\xEDas)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 21\n              }, this)\n            }, pattern.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-muted\",\n            children: \"No hay patrones creados\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPatternModal,\n      onHide: () => setShowPatternModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Aplicar Patr\\xF3n al Calendario Te\\xF3rico\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de inicio\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: startDate,\n                  onChange: e => setStartDate(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de fin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: endDate,\n                  onChange: e => setEndDate(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Seleccionar patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: selectedPatternId,\n              onChange: e => setSelectedPatternId(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Seleccionar patr\\xF3n...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), patterns.map(pattern => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: pattern.id,\n                children: [pattern.name, \" (\", pattern.days.length, \" d\\xEDas)\"]\n              }, pattern.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this), selectedPatternId && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pattern-preview\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Vista previa del patr\\xF3n:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this), (() => {\n              const pattern = patterns.find(p => p.id === selectedPatternId);\n              return pattern ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pattern-days\",\n                children: pattern.days.map((day, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"pattern-day-preview\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [\"D\\xEDa \", index + 1, \":\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 27\n                  }, this), \" \", day.shiftName, \" (\", day.hours, \"h)\"]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 21\n              }, this) : null;\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowPatternModal(false),\n          children: \"Cancelar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleApplyPattern,\n          disabled: loading || !selectedPatternId || !startDate || !endDate,\n          children: loading ? 'Aplicando...' : 'Aplicar Patrón'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 5\n  }, this);\n}\n_s(PlannedCalendarView, \"bHLOIOTv80Vusyuq5JzquYOe2YY=\");\n_c = PlannedCalendarView;\nexport default PlannedCalendarView;\nvar _c;\n$RefreshReg$(_c, \"PlannedCalendarView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "<PERSON><PERSON>", "<PERSON><PERSON>", "Form", "Modal", "Calendar", "getAllPlannedDays", "getAllPatterns", "applyPattern", "clearPlannedCalendar", "deletePlannedDay", "jsxDEV", "_jsxDEV", "PlannedCalendarView", "onDateSelect", "selectedDate", "onCalendarUpdate", "_s", "plannedDays", "setPlannedDays", "patterns", "setPatterns", "loading", "setLoading", "error", "setError", "showPatternModal", "setShowPatternModal", "selectedPatternId", "setSelectedPatternId", "startDate", "setStartDate", "endDate", "setEndDate", "loadPlannedDays", "loadPatterns", "days", "err", "console", "message", "patternsData", "handleApplyPattern", "resetPatternForm", "handleClearCalendar", "window", "confirm", "handleDeleteDay", "dayId", "getTileContent", "date", "view", "dayData", "find", "day", "Date", "toDateString", "className", "children", "hours", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "shiftName", "getTileClassName", "getTotalHours", "reduce", "total", "getCurrentMonthHours", "currentMonth", "getMonth", "currentYear", "getFullYear", "filter", "dayDate", "getSelectedDayData", "variant", "onClick", "disabled", "onClose", "dismissible", "md", "onChange", "value", "tileContent", "tileClassName", "locale", "length", "toFixed", "toLocaleDateString", "weekday", "year", "month", "description", "size", "id", "map", "pattern", "name", "show", "onHide", "Header", "closeButton", "Title", "Body", "Group", "Label", "Control", "type", "e", "target", "Select", "p", "index", "Footer", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/components/PlannedCalendarView.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Button, Alert, Form, Modal } from 'react-bootstrap';\nimport Calendar from 'react-calendar';\nimport { \n  getAllPlannedDays, \n  getAllPatterns, \n  applyPattern, \n  clearPlannedCalendar,\n  deletePlannedDay\n} from '../services/api';\n\nfunction PlannedCalendarView({ onDateSelect, selectedDate, onCalendarUpdate }) {\n  const [plannedDays, setPlannedDays] = useState([]);\n  const [patterns, setPatterns] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  \n  // Estados para aplicación de patrones\n  const [showPatternModal, setShowPatternModal] = useState(false);\n  const [selectedPatternId, setSelectedPatternId] = useState('');\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n\n  useEffect(() => {\n    loadPlannedDays();\n    loadPatterns();\n  }, []);\n\n  const loadPlannedDays = async () => {\n    try {\n      setLoading(true);\n      const days = await getAllPlannedDays();\n      setPlannedDays(days);\n      setError('');\n    } catch (err) {\n      console.error('Error loading planned days:', err);\n      setError('Error cargando días planificados: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadPatterns = async () => {\n    try {\n      const patternsData = await getAllPatterns();\n      setPatterns(patternsData);\n    } catch (err) {\n      console.error('Error loading patterns:', err);\n    }\n  };\n\n  const handleApplyPattern = async () => {\n    if (!selectedPatternId || !startDate || !endDate) {\n      setError('Todos los campos son obligatorios');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError('');\n      \n      await applyPattern(selectedPatternId, startDate, endDate);\n      await loadPlannedDays();\n      onCalendarUpdate();\n      setShowPatternModal(false);\n      resetPatternForm();\n    } catch (err) {\n      setError('Error aplicando patrón: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleClearCalendar = async () => {\n    if (window.confirm('¿Estás seguro de que quieres limpiar todo el calendario planificado?')) {\n      try {\n        setLoading(true);\n        await clearPlannedCalendar();\n        await loadPlannedDays();\n        onCalendarUpdate();\n        setError('');\n      } catch (err) {\n        setError('Error limpiando calendario: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  const handleDeleteDay = async (dayId) => {\n    if (window.confirm('¿Eliminar este día del calendario planificado?')) {\n      try {\n        setLoading(true);\n        await deletePlannedDay(dayId);\n        await loadPlannedDays();\n        onCalendarUpdate();\n      } catch (err) {\n        setError('Error eliminando día: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  const resetPatternForm = () => {\n    setSelectedPatternId('');\n    setStartDate('');\n    setEndDate('');\n  };\n\n  const getTileContent = ({ date, view }) => {\n    if (view !== 'month') return null;\n    \n    const dayData = plannedDays.find(day => \n      new Date(day.date).toDateString() === date.toDateString()\n    );\n    \n    if (dayData) {\n      return (\n        <div className=\"calendar-tile-content\">\n          <div className=\"badge bg-secondary\">{dayData.hours}h</div>\n          {dayData.shiftName && (\n            <div className=\"shift-name\">{dayData.shiftName}</div>\n          )}\n        </div>\n      );\n    }\n    return null;\n  };\n\n  const getTileClassName = ({ date, view }) => {\n    if (view !== 'month') return '';\n    \n    const dayData = plannedDays.find(day => \n      new Date(day.date).toDateString() === date.toDateString()\n    );\n    \n    if (dayData) {\n      return 'has-planned-work';\n    }\n    return '';\n  };\n\n  const getTotalHours = () => {\n    return plannedDays.reduce((total, day) => total + (day.hours || 0), 0);\n  };\n\n  const getCurrentMonthHours = () => {\n    const currentMonth = new Date().getMonth();\n    const currentYear = new Date().getFullYear();\n    \n    return plannedDays\n      .filter(day => {\n        const dayDate = new Date(day.date);\n        return dayDate.getMonth() === currentMonth && dayDate.getFullYear() === currentYear;\n      })\n      .reduce((total, day) => total + (day.hours || 0), 0);\n  };\n\n  const getSelectedDayData = () => {\n    if (!selectedDate) return null;\n    return plannedDays.find(day => \n      new Date(day.date).toDateString() === selectedDate.toDateString()\n    );\n  };\n\n  return (\n    <div>\n      <Row className=\"mb-3\">\n        <Col>\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <h4>Calendario Teórico (Planificado)</h4>\n            <div>\n              <Button\n                variant=\"success\"\n                onClick={() => setShowPatternModal(true)}\n                className=\"me-2\"\n              >\n                📋 Aplicar Patrón\n              </Button>\n              <Button\n                variant=\"outline-danger\"\n                onClick={handleClearCalendar}\n                disabled={loading}\n              >\n                🗑️ Limpiar Todo\n              </Button>\n            </div>\n          </div>\n        </Col>\n      </Row>\n\n      {error && (\n        <Row className=\"mb-3\">\n          <Col>\n            <Alert variant=\"danger\" onClose={() => setError('')} dismissible>\n              {error}\n            </Alert>\n          </Col>\n        </Row>\n      )}\n\n      <Row className=\"mb-3\">\n        <Col md={8}>\n          <div className=\"calendar-container\">\n            <Calendar\n              onChange={onDateSelect}\n              value={selectedDate}\n              tileContent={getTileContent}\n              tileClassName={getTileClassName}\n              locale=\"es-ES\"\n            />\n          </div>\n        </Col>\n        <Col md={4}>\n          <div className=\"calendar-stats\">\n            <h5>Estadísticas Planificadas</h5>\n            <div className=\"stat-item\">\n              <strong>Total de días planificados:</strong> {plannedDays.length}\n            </div>\n            <div className=\"stat-item\">\n              <strong>Total de horas:</strong> {getTotalHours().toFixed(1)}h\n            </div>\n            <div className=\"stat-item\">\n              <strong>Horas este mes:</strong> {getCurrentMonthHours().toFixed(1)}h\n            </div>\n            <div className=\"stat-item\">\n              <strong>Promedio por día:</strong> {plannedDays.length > 0 ? (getTotalHours() / plannedDays.length).toFixed(1) : 0}h\n            </div>\n          </div>\n\n          {selectedDate && (\n            <div className=\"selected-date-info mt-3\">\n              <h6>Fecha seleccionada:</h6>\n              <p>{selectedDate.toLocaleDateString('es-ES', { \n                weekday: 'long', \n                year: 'numeric', \n                month: 'long', \n                day: 'numeric' \n              })}</p>\n              \n              {(() => {\n                const dayData = getSelectedDayData();\n                return dayData ? (\n                  <div className=\"day-details\">\n                    <p><strong>Horas planificadas:</strong> {dayData.hours}h</p>\n                    {dayData.shiftName && <p><strong>Turno:</strong> {dayData.shiftName}</p>}\n                    {dayData.description && <p><strong>Descripción:</strong> {dayData.description}</p>}\n                    <Button \n                      variant=\"outline-danger\" \n                      size=\"sm\"\n                      onClick={() => handleDeleteDay(dayData.id)}\n                      disabled={loading}\n                    >\n                      🗑️ Eliminar\n                    </Button>\n                  </div>\n                ) : (\n                  <p className=\"text-muted\">No hay planificación para este día</p>\n                );\n              })()}\n            </div>\n          )}\n\n          <div className=\"pattern-info mt-3\">\n            <h6>Patrones disponibles:</h6>\n            {patterns.length > 0 ? (\n              <ul className=\"list-unstyled\">\n                {patterns.map(pattern => (\n                  <li key={pattern.id} className=\"mb-1\">\n                    <small>\n                      <strong>{pattern.name}</strong> ({pattern.days.length} días)\n                    </small>\n                  </li>\n                ))}\n              </ul>\n            ) : (\n              <p className=\"text-muted\">No hay patrones creados</p>\n            )}\n          </div>\n        </Col>\n      </Row>\n\n      {/* Modal para aplicar patrones */}\n      <Modal show={showPatternModal} onHide={() => setShowPatternModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>Aplicar Patrón al Calendario Teórico</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <Form>\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Fecha de inicio</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={startDate}\n                    onChange={(e) => setStartDate(e.target.value)}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Fecha de fin</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={endDate}\n                    onChange={(e) => setEndDate(e.target.value)}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n            \n            <Form.Group className=\"mb-3\">\n              <Form.Label>Seleccionar patrón</Form.Label>\n              <Form.Select\n                value={selectedPatternId}\n                onChange={(e) => setSelectedPatternId(e.target.value)}\n              >\n                <option value=\"\">Seleccionar patrón...</option>\n                {patterns.map(pattern => (\n                  <option key={pattern.id} value={pattern.id}>\n                    {pattern.name} ({pattern.days.length} días)\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n\n            {selectedPatternId && (\n              <div className=\"pattern-preview\">\n                <h6>Vista previa del patrón:</h6>\n                {(() => {\n                  const pattern = patterns.find(p => p.id === selectedPatternId);\n                  return pattern ? (\n                    <div className=\"pattern-days\">\n                      {pattern.days.map((day, index) => (\n                        <div key={index} className=\"pattern-day-preview\">\n                          <strong>Día {index + 1}:</strong> {day.shiftName} ({day.hours}h)\n                        </div>\n                      ))}\n                    </div>\n                  ) : null;\n                })()}\n              </div>\n            )}\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowPatternModal(false)}>\n            Cancelar\n          </Button>\n          <Button\n            variant=\"primary\"\n            onClick={handleApplyPattern}\n            disabled={loading || !selectedPatternId || !startDate || !endDate}\n          >\n            {loading ? 'Aplicando...' : 'Aplicar Patrón'}\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </div>\n  );\n}\n\nexport default PlannedCalendarView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,QAAQ,iBAAiB;AACtE,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,SACEC,iBAAiB,EACjBC,cAAc,EACdC,YAAY,EACZC,oBAAoB,EACpBC,gBAAgB,QACX,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,SAASC,mBAAmBA,CAAC;EAAEC,YAAY;EAAEC,YAAY;EAAEC;AAAiB,CAAC,EAAE;EAAAC,EAAA;EAC7E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAM,CAAC6B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC+B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACdoC,eAAe,CAAC,CAAC;IACjBC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMa,IAAI,GAAG,MAAM9B,iBAAiB,CAAC,CAAC;MACtCa,cAAc,CAACiB,IAAI,CAAC;MACpBX,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOY,GAAG,EAAE;MACZC,OAAO,CAACd,KAAK,CAAC,6BAA6B,EAAEa,GAAG,CAAC;MACjDZ,QAAQ,CAAC,oCAAoC,GAAGY,GAAG,CAACE,OAAO,CAAC;IAC9D,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMY,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMK,YAAY,GAAG,MAAMjC,cAAc,CAAC,CAAC;MAC3Cc,WAAW,CAACmB,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAOH,GAAG,EAAE;MACZC,OAAO,CAACd,KAAK,CAAC,yBAAyB,EAAEa,GAAG,CAAC;IAC/C;EACF,CAAC;EAED,MAAMI,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACb,iBAAiB,IAAI,CAACE,SAAS,IAAI,CAACE,OAAO,EAAE;MAChDP,QAAQ,CAAC,mCAAmC,CAAC;MAC7C;IACF;IAEA,IAAI;MACFF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMjB,YAAY,CAACoB,iBAAiB,EAAEE,SAAS,EAAEE,OAAO,CAAC;MACzD,MAAME,eAAe,CAAC,CAAC;MACvBlB,gBAAgB,CAAC,CAAC;MAClBW,mBAAmB,CAAC,KAAK,CAAC;MAC1Be,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOL,GAAG,EAAE;MACZZ,QAAQ,CAAC,0BAA0B,GAAGY,GAAG,CAACE,OAAO,CAAC;IACpD,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAIC,MAAM,CAACC,OAAO,CAAC,sEAAsE,CAAC,EAAE;MAC1F,IAAI;QACFtB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMd,oBAAoB,CAAC,CAAC;QAC5B,MAAMyB,eAAe,CAAC,CAAC;QACvBlB,gBAAgB,CAAC,CAAC;QAClBS,QAAQ,CAAC,EAAE,CAAC;MACd,CAAC,CAAC,OAAOY,GAAG,EAAE;QACZZ,QAAQ,CAAC,8BAA8B,GAAGY,GAAG,CAACE,OAAO,CAAC;MACxD,CAAC,SAAS;QACRhB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;EAED,MAAMuB,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,IAAIH,MAAM,CAACC,OAAO,CAAC,gDAAgD,CAAC,EAAE;MACpE,IAAI;QACFtB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMb,gBAAgB,CAACqC,KAAK,CAAC;QAC7B,MAAMb,eAAe,CAAC,CAAC;QACvBlB,gBAAgB,CAAC,CAAC;MACpB,CAAC,CAAC,OAAOqB,GAAG,EAAE;QACZZ,QAAQ,CAAC,wBAAwB,GAAGY,GAAG,CAACE,OAAO,CAAC;MAClD,CAAC,SAAS;QACRhB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;EAED,MAAMmB,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bb,oBAAoB,CAAC,EAAE,CAAC;IACxBE,YAAY,CAAC,EAAE,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;EAChB,CAAC;EAED,MAAMe,cAAc,GAAGA,CAAC;IAAEC,IAAI;IAAEC;EAAK,CAAC,KAAK;IACzC,IAAIA,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;IAEjC,MAAMC,OAAO,GAAGjC,WAAW,CAACkC,IAAI,CAACC,GAAG,IAClC,IAAIC,IAAI,CAACD,GAAG,CAACJ,IAAI,CAAC,CAACM,YAAY,CAAC,CAAC,KAAKN,IAAI,CAACM,YAAY,CAAC,CAC1D,CAAC;IAED,IAAIJ,OAAO,EAAE;MACX,oBACEvC,OAAA;QAAK4C,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpC7C,OAAA;UAAK4C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,GAAEN,OAAO,CAACO,KAAK,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EACzDX,OAAO,CAACY,SAAS,iBAChBnD,OAAA;UAAK4C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAEN,OAAO,CAACY;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACrD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAEV;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAME,gBAAgB,GAAGA,CAAC;IAAEf,IAAI;IAAEC;EAAK,CAAC,KAAK;IAC3C,IAAIA,IAAI,KAAK,OAAO,EAAE,OAAO,EAAE;IAE/B,MAAMC,OAAO,GAAGjC,WAAW,CAACkC,IAAI,CAACC,GAAG,IAClC,IAAIC,IAAI,CAACD,GAAG,CAACJ,IAAI,CAAC,CAACM,YAAY,CAAC,CAAC,KAAKN,IAAI,CAACM,YAAY,CAAC,CAC1D,CAAC;IAED,IAAIJ,OAAO,EAAE;MACX,OAAO,kBAAkB;IAC3B;IACA,OAAO,EAAE;EACX,CAAC;EAED,MAAMc,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAO/C,WAAW,CAACgD,MAAM,CAAC,CAACC,KAAK,EAAEd,GAAG,KAAKc,KAAK,IAAId,GAAG,CAACK,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACxE,CAAC;EAED,MAAMU,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,YAAY,GAAG,IAAIf,IAAI,CAAC,CAAC,CAACgB,QAAQ,CAAC,CAAC;IAC1C,MAAMC,WAAW,GAAG,IAAIjB,IAAI,CAAC,CAAC,CAACkB,WAAW,CAAC,CAAC;IAE5C,OAAOtD,WAAW,CACfuD,MAAM,CAACpB,GAAG,IAAI;MACb,MAAMqB,OAAO,GAAG,IAAIpB,IAAI,CAACD,GAAG,CAACJ,IAAI,CAAC;MAClC,OAAOyB,OAAO,CAACJ,QAAQ,CAAC,CAAC,KAAKD,YAAY,IAAIK,OAAO,CAACF,WAAW,CAAC,CAAC,KAAKD,WAAW;IACrF,CAAC,CAAC,CACDL,MAAM,CAAC,CAACC,KAAK,EAAEd,GAAG,KAAKc,KAAK,IAAId,GAAG,CAACK,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACxD,CAAC;EAED,MAAMiB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAAC5D,YAAY,EAAE,OAAO,IAAI;IAC9B,OAAOG,WAAW,CAACkC,IAAI,CAACC,GAAG,IACzB,IAAIC,IAAI,CAACD,GAAG,CAACJ,IAAI,CAAC,CAACM,YAAY,CAAC,CAAC,KAAKxC,YAAY,CAACwC,YAAY,CAAC,CAClE,CAAC;EACH,CAAC;EAED,oBACE3C,OAAA;IAAA6C,QAAA,gBACE7C,OAAA,CAACb,GAAG;MAACyD,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnB7C,OAAA,CAACZ,GAAG;QAAAyD,QAAA,eACF7C,OAAA;UAAK4C,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChE7C,OAAA;YAAA6C,QAAA,EAAI;UAAgC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzClD,OAAA;YAAA6C,QAAA,gBACE7C,OAAA,CAACX,MAAM;cACL2E,OAAO,EAAC,SAAS;cACjBC,OAAO,EAAEA,CAAA,KAAMlD,mBAAmB,CAAC,IAAI,CAAE;cACzC6B,SAAS,EAAC,MAAM;cAAAC,QAAA,EACjB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlD,OAAA,CAACX,MAAM;cACL2E,OAAO,EAAC,gBAAgB;cACxBC,OAAO,EAAElC,mBAAoB;cAC7BmC,QAAQ,EAAExD,OAAQ;cAAAmC,QAAA,EACnB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELtC,KAAK,iBACJZ,OAAA,CAACb,GAAG;MAACyD,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnB7C,OAAA,CAACZ,GAAG;QAAAyD,QAAA,eACF7C,OAAA,CAACV,KAAK;UAAC0E,OAAO,EAAC,QAAQ;UAACG,OAAO,EAAEA,CAAA,KAAMtD,QAAQ,CAAC,EAAE,CAAE;UAACuD,WAAW;UAAAvB,QAAA,EAC7DjC;QAAK;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDlD,OAAA,CAACb,GAAG;MAACyD,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB7C,OAAA,CAACZ,GAAG;QAACiF,EAAE,EAAE,CAAE;QAAAxB,QAAA,eACT7C,OAAA;UAAK4C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjC7C,OAAA,CAACP,QAAQ;YACP6E,QAAQ,EAAEpE,YAAa;YACvBqE,KAAK,EAAEpE,YAAa;YACpBqE,WAAW,EAAEpC,cAAe;YAC5BqC,aAAa,EAAErB,gBAAiB;YAChCsB,MAAM,EAAC;UAAO;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNlD,OAAA,CAACZ,GAAG;QAACiF,EAAE,EAAE,CAAE;QAAAxB,QAAA,gBACT7C,OAAA;UAAK4C,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B7C,OAAA;YAAA6C,QAAA,EAAI;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClClD,OAAA;YAAK4C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB7C,OAAA;cAAA6C,QAAA,EAAQ;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC5C,WAAW,CAACqE,MAAM;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACNlD,OAAA;YAAK4C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB7C,OAAA;cAAA6C,QAAA,EAAQ;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACG,aAAa,CAAC,CAAC,CAACuB,OAAO,CAAC,CAAC,CAAC,EAAC,GAC/D;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlD,OAAA;YAAK4C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB7C,OAAA;cAAA6C,QAAA,EAAQ;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACM,oBAAoB,CAAC,CAAC,CAACoB,OAAO,CAAC,CAAC,CAAC,EAAC,GACtE;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlD,OAAA;YAAK4C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB7C,OAAA;cAAA6C,QAAA,EAAQ;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC5C,WAAW,CAACqE,MAAM,GAAG,CAAC,GAAG,CAACtB,aAAa,CAAC,CAAC,GAAG/C,WAAW,CAACqE,MAAM,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,GACrH;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL/C,YAAY,iBACXH,OAAA;UAAK4C,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC7C,OAAA;YAAA6C,QAAA,EAAI;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BlD,OAAA;YAAA6C,QAAA,EAAI1C,YAAY,CAAC0E,kBAAkB,CAAC,OAAO,EAAE;cAC3CC,OAAO,EAAE,MAAM;cACfC,IAAI,EAAE,SAAS;cACfC,KAAK,EAAE,MAAM;cACbvC,GAAG,EAAE;YACP,CAAC;UAAC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEN,CAAC,MAAM;YACN,MAAMX,OAAO,GAAGwB,kBAAkB,CAAC,CAAC;YACpC,OAAOxB,OAAO,gBACZvC,OAAA;cAAK4C,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7C,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACX,OAAO,CAACO,KAAK,EAAC,GAAC;cAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EAC3DX,OAAO,CAACY,SAAS,iBAAInD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACX,OAAO,CAACY,SAAS;cAAA;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACvEX,OAAO,CAAC0C,WAAW,iBAAIjF,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACX,OAAO,CAAC0C,WAAW;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClFlD,OAAA,CAACX,MAAM;gBACL2E,OAAO,EAAC,gBAAgB;gBACxBkB,IAAI,EAAC,IAAI;gBACTjB,OAAO,EAAEA,CAAA,KAAM/B,eAAe,CAACK,OAAO,CAAC4C,EAAE,CAAE;gBAC3CjB,QAAQ,EAAExD,OAAQ;gBAAAmC,QAAA,EACnB;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,gBAENlD,OAAA;cAAG4C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAChE;UACH,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,eAEDlD,OAAA;UAAK4C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC7C,OAAA;YAAA6C,QAAA,EAAI;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC7B1C,QAAQ,CAACmE,MAAM,GAAG,CAAC,gBAClB3E,OAAA;YAAI4C,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC1BrC,QAAQ,CAAC4E,GAAG,CAACC,OAAO,iBACnBrF,OAAA;cAAqB4C,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnC7C,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAA6C,QAAA,EAASwC,OAAO,CAACC;gBAAI;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,MAAE,EAACmC,OAAO,CAAC7D,IAAI,CAACmD,MAAM,EAAC,WACxD;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC,GAHDmC,OAAO,CAACF,EAAE;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIf,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,gBAELlD,OAAA;YAAG4C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACrD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlD,OAAA,CAACR,KAAK;MAAC+F,IAAI,EAAEzE,gBAAiB;MAAC0E,MAAM,EAAEA,CAAA,KAAMzE,mBAAmB,CAAC,KAAK,CAAE;MAACmE,IAAI,EAAC,IAAI;MAAArC,QAAA,gBAChF7C,OAAA,CAACR,KAAK,CAACiG,MAAM;QAACC,WAAW;QAAA7C,QAAA,eACvB7C,OAAA,CAACR,KAAK,CAACmG,KAAK;UAAA9C,QAAA,EAAC;QAAoC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eACflD,OAAA,CAACR,KAAK,CAACoG,IAAI;QAAA/C,QAAA,eACT7C,OAAA,CAACT,IAAI;UAAAsD,QAAA,gBACH7C,OAAA,CAACb,GAAG;YAAA0D,QAAA,gBACF7C,OAAA,CAACZ,GAAG;cAACiF,EAAE,EAAE,CAAE;cAAAxB,QAAA,eACT7C,OAAA,CAACT,IAAI,CAACsG,KAAK;gBAACjD,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B7C,OAAA,CAACT,IAAI,CAACuG,KAAK;kBAAAjD,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxClD,OAAA,CAACT,IAAI,CAACwG,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXzB,KAAK,EAAErD,SAAU;kBACjBoD,QAAQ,EAAG2B,CAAC,IAAK9E,YAAY,CAAC8E,CAAC,CAACC,MAAM,CAAC3B,KAAK;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNlD,OAAA,CAACZ,GAAG;cAACiF,EAAE,EAAE,CAAE;cAAAxB,QAAA,eACT7C,OAAA,CAACT,IAAI,CAACsG,KAAK;gBAACjD,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B7C,OAAA,CAACT,IAAI,CAACuG,KAAK;kBAAAjD,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrClD,OAAA,CAACT,IAAI,CAACwG,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXzB,KAAK,EAAEnD,OAAQ;kBACfkD,QAAQ,EAAG2B,CAAC,IAAK5E,UAAU,CAAC4E,CAAC,CAACC,MAAM,CAAC3B,KAAK;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlD,OAAA,CAACT,IAAI,CAACsG,KAAK;YAACjD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1B7C,OAAA,CAACT,IAAI,CAACuG,KAAK;cAAAjD,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3ClD,OAAA,CAACT,IAAI,CAAC4G,MAAM;cACV5B,KAAK,EAAEvD,iBAAkB;cACzBsD,QAAQ,EAAG2B,CAAC,IAAKhF,oBAAoB,CAACgF,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAE;cAAA1B,QAAA,gBAEtD7C,OAAA;gBAAQuE,KAAK,EAAC,EAAE;gBAAA1B,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9C1C,QAAQ,CAAC4E,GAAG,CAACC,OAAO,iBACnBrF,OAAA;gBAAyBuE,KAAK,EAAEc,OAAO,CAACF,EAAG;gBAAAtC,QAAA,GACxCwC,OAAO,CAACC,IAAI,EAAC,IAAE,EAACD,OAAO,CAAC7D,IAAI,CAACmD,MAAM,EAAC,WACvC;cAAA,GAFaU,OAAO,CAACF,EAAE;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EAEZlC,iBAAiB,iBAChBhB,OAAA;YAAK4C,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B7C,OAAA;cAAA6C,QAAA,EAAI;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAChC,CAAC,MAAM;cACN,MAAMmC,OAAO,GAAG7E,QAAQ,CAACgC,IAAI,CAAC4D,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAKnE,iBAAiB,CAAC;cAC9D,OAAOqE,OAAO,gBACZrF,OAAA;gBAAK4C,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAC1BwC,OAAO,CAAC7D,IAAI,CAAC4D,GAAG,CAAC,CAAC3C,GAAG,EAAE4D,KAAK,kBAC3BrG,OAAA;kBAAiB4C,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAC9C7C,OAAA;oBAAA6C,QAAA,GAAQ,SAAI,EAACwD,KAAK,GAAG,CAAC,EAAC,GAAC;kBAAA;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACT,GAAG,CAACU,SAAS,EAAC,IAAE,EAACV,GAAG,CAACK,KAAK,EAAC,IAChE;gBAAA,GAFUuD,KAAK;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,GACJ,IAAI;YACV,CAAC,EAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACblD,OAAA,CAACR,KAAK,CAAC8G,MAAM;QAAAzD,QAAA,gBACX7C,OAAA,CAACX,MAAM;UAAC2E,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMlD,mBAAmB,CAAC,KAAK,CAAE;UAAA8B,QAAA,EAAC;QAEvE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlD,OAAA,CAACX,MAAM;UACL2E,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAEpC,kBAAmB;UAC5BqC,QAAQ,EAAExD,OAAO,IAAI,CAACM,iBAAiB,IAAI,CAACE,SAAS,IAAI,CAACE,OAAQ;UAAAyB,QAAA,EAEjEnC,OAAO,GAAG,cAAc,GAAG;QAAgB;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV;AAAC7C,EAAA,CA/VQJ,mBAAmB;AAAAsG,EAAA,GAAnBtG,mBAAmB;AAiW5B,eAAeA,mBAAmB;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}