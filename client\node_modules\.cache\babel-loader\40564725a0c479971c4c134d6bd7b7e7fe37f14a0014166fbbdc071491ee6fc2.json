{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\SettingsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Form, Alert, ListGroup, Modal } from 'react-bootstrap';\nimport { getAllPatterns, createPattern, updatePattern, deletePattern, getAllShifts } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SettingsPage() {\n  _s();\n  const [patterns, setPatterns] = useState([]);\n  const [currentPattern, setCurrentPattern] = useState(null);\n  const [showPatternForm, setShowPatternForm] = useState(false);\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    loadPatterns();\n    loadAvailableShifts();\n  }, []);\n  const loadPatterns = async () => {\n    setLoading(true);\n    try {\n      const data = await getAllPatterns();\n      setPatterns(data);\n    } catch (err) {\n      setError('Error cargando patrones.');\n      console.error('Error loading patterns:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (err) {\n      console.error('Error loading shifts:', err);\n    }\n  };\n  const handleAddPattern = () => {\n    setCurrentPattern({\n      name: '',\n      basePattern: Array.from({\n        length: 7\n      }, () => ({\n        type: '',\n        hours: 0,\n        description: '',\n        shiftId: null\n      })),\n      overrides: []\n    });\n    setShowPatternForm(true);\n  };\n  const handleEditPattern = pattern => {\n    setCurrentPattern({\n      ...pattern\n    });\n    setShowPatternForm(true);\n  };\n  const handleDeletePattern = async id => {\n    if (window.confirm('¿Estás seguro de que quieres eliminar este patrón?')) {\n      setLoading(true);\n      try {\n        await deletePattern(id);\n        await loadPatterns();\n      } catch (err) {\n        setError('Error eliminando patrón.');\n        console.error('Error deleting pattern:', err);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  const handleSavePattern = async () => {\n    if (!currentPattern || !currentPattern.name || !currentPattern.basePattern) {\n      setError('El nombre del patrón y el patrón base son requeridos.');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      if (currentPattern.id) {\n        await updatePattern(currentPattern.id, currentPattern.name, currentPattern.basePattern, currentPattern.overrides);\n      } else {\n        await createPattern(currentPattern.name, currentPattern.basePattern, currentPattern.overrides);\n      }\n      setShowPatternForm(false);\n      setCurrentPattern(null);\n      await loadPatterns();\n    } catch (err) {\n      setError('Error guardando patrón.');\n      console.error('Error saving pattern:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCancelEdit = () => {\n    setShowPatternForm(false);\n    setCurrentPattern(null);\n    setError('');\n  };\n  const handleBasePatternDayChange = (index, field, value) => {\n    const newBasePattern = [...currentPattern.basePattern];\n    newBasePattern[index][field] = value;\n    if (field === 'shiftId' && value) {\n      const shift = availableShifts.find(s => s.id === value);\n      if (shift) {\n        newBasePattern[index].type = 'worked';\n        newBasePattern[index].hours = shift.totalHours;\n        newBasePattern[index].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;\n      }\n    } else if (field === 'shiftId' && !value) {\n      newBasePattern[index].description = '';\n    }\n    setCurrentPattern({\n      ...currentPattern,\n      basePattern: newBasePattern\n    });\n  };\n  const handleOverrideChange = (overrideIndex, field, value) => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex][field] = value;\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: newOverrides\n    });\n  };\n  const handleOverridePatternDayChange = (overrideIndex, dayIndex, field, value) => {\n    const newOverrides = [...currentPattern.overrides];\n    const newOverridePattern = [...newOverrides[overrideIndex].overridePattern];\n    newOverridePattern[dayIndex][field] = value;\n    if (field === 'shiftId' && value) {\n      const shift = availableShifts.find(s => s.id === value);\n      if (shift) {\n        newOverridePattern[dayIndex].type = 'worked';\n        newOverridePattern[dayIndex].hours = shift.totalHours;\n        newOverridePattern[dayIndex].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;\n      }\n    } else if (field === 'shiftId' && !value) {\n      newOverridePattern[dayIndex].description = '';\n    }\n    newOverrides[overrideIndex].overridePattern = newOverridePattern;\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: newOverrides\n    });\n  };\n  const handleAddOverride = () => {\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: [...currentPattern.overrides, {\n        startDate: '',\n        endDate: '',\n        overridePattern: [{\n          type: '',\n          hours: 0,\n          description: '',\n          shiftId: null\n        }]\n      }]\n    });\n  };\n  const handleRemoveOverride = overrideIndex => {\n    const newOverrides = currentPattern.overrides.filter((_, i) => i !== overrideIndex);\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: newOverrides\n    });\n  };\n  const handleAddOverridePatternDay = overrideIndex => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex].overridePattern.push({\n      type: '',\n      hours: 0,\n      description: '',\n      shiftId: null\n    });\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: newOverrides\n    });\n  };\n  const handleRemoveOverridePatternDay = (overrideIndex, dayIndex) => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex].overridePattern = newOverrides[overrideIndex].overridePattern.filter((_, i) => i !== dayIndex);\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: newOverrides\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"my-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Configuraci\\xF3n de Patrones de Calendario\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleAddPattern,\n          className: \"mb-3\",\n          children: \"Crear Nuevo Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Cargando patrones...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this) : patterns.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No hay patrones definidos.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ListGroup, {\n          children: patterns.map(pattern => /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n            className: \"d-flex justify-content-between align-items-center\",\n            children: [pattern.name, /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"info\",\n                size: \"sm\",\n                className: \"me-2\",\n                onClick: () => handleEditPattern(pattern),\n                children: \"Editar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"danger\",\n                size: \"sm\",\n                onClick: () => handleDeletePattern(pattern.id),\n                children: \"Eliminar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 19\n            }, this)]\n          }, pattern.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPatternForm,\n      onHide: handleCancelEdit,\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: currentPattern && currentPattern.id ? 'Editar Patrón' : 'Crear Nuevo Patrón'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: currentPattern && /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Nombre del Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              value: currentPattern.name,\n              onChange: e => setCurrentPattern({\n                ...currentPattern,\n                name: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Patr\\xF3n Base (Semanal):\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this), currentPattern.basePattern.map((day, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border p-3 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: [\"D\\xEDa \", ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'][index]]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Tipo de D\\xEDa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: day.type,\n                onChange: e => handleBasePatternDayChange(index, 'type', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Seleccionar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"worked\",\n                  children: \"Trabajado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"holiday\",\n                  children: \"Vacaciones\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"permit\",\n                  children: \"Permiso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"negative\",\n                  children: \"C\\xF3mputo Negativo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Horas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"number\",\n                value: day.hours,\n                onChange: e => handleBasePatternDayChange(index, 'hours', parseFloat(e.target.value))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Turno Predefinido\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: day.shiftId || '',\n                onChange: e => handleBasePatternDayChange(index, 'shiftId', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ninguno\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 23\n                }, this), availableShifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: shift.id,\n                  children: [shift.name, \" - \", shift.startTime, \" a \", shift.endTime, \" (\", shift.totalHours, \"h)\"]\n                }, shift.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Descripci\\xF3n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                as: \"textarea\",\n                rows: 1,\n                value: day.description,\n                onChange: e => handleBasePatternDayChange(index, 'description', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 17\n          }, this)), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mt-4\",\n            children: \"Anulaciones (Overrides):\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), currentPattern.overrides.map((override, overrideIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border p-3 mb-2 bg-light\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: [\"Anulaci\\xF3n \", overrideIndex + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  controlId: `overrideStartDate-${overrideIndex}`,\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Fecha de Inicio\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: override.startDate,\n                    onChange: e => handleOverrideChange(overrideIndex, 'startDate', e.target.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  controlId: `overrideEndDate-${overrideIndex}`,\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Fecha de Fin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: override.endDate,\n                    onChange: e => handleOverrideChange(overrideIndex, 'endDate', e.target.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"D\\xEDas de la Anulaci\\xF3n:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 19\n            }, this), override.overridePattern.map((day, dayIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border p-3 mb-2 ms-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: [\"D\\xEDa \", dayIndex + 1]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Tipo de D\\xEDa\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: day.type,\n                  onChange: e => handleOverridePatternDayChange(overrideIndex, dayIndex, 'type', e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Seleccionar\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"worked\",\n                    children: \"Trabajado\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"holiday\",\n                    children: \"Vacaciones\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"permit\",\n                    children: \"Permiso\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"negative\",\n                    children: \"C\\xF3mputo Negativo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Horas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  value: day.hours,\n                  onChange: e => handleOverridePatternDayChange(overrideIndex, dayIndex, 'hours', parseFloat(e.target.value))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Turno Predefinido\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: day.shiftId || '',\n                  onChange: e => handleOverridePatternDayChange(overrideIndex, dayIndex, 'shiftId', e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Ninguno\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 27\n                  }, this), availableShifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: shift.id,\n                    children: [shift.name, \" - \", shift.startTime, \" a \", shift.endTime, \" (\", shift.totalHours, \"h)\"]\n                  }, shift.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 29\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Descripci\\xF3n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  as: \"textarea\",\n                  rows: 1,\n                  value: day.description,\n                  onChange: e => handleOverridePatternDayChange(overrideIndex, dayIndex, 'description', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"danger\",\n                size: \"sm\",\n                onClick: () => handleRemoveOverridePatternDay(overrideIndex, dayIndex),\n                children: \"Eliminar D\\xEDa de Anulaci\\xF3n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 23\n              }, this)]\n            }, dayIndex, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 21\n            }, this)), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"secondary\",\n              onClick: () => handleAddOverridePatternDay(overrideIndex),\n              className: \"mt-3\",\n              children: \"A\\xF1adir D\\xEDa a Anulaci\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              onClick: () => handleRemoveOverride(overrideIndex),\n              className: \"mt-3 ms-2\",\n              children: \"Eliminar Anulaci\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 19\n            }, this)]\n          }, overrideIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 17\n          }, this)), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"info\",\n            onClick: handleAddOverride,\n            className: \"mt-3\",\n            children: \"A\\xF1adir Anulaci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: handleCancelEdit,\n          children: \"Cancelar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSavePattern,\n          disabled: loading,\n          children: \"Guardar Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 5\n  }, this);\n}\n_s(SettingsPage, \"b7P1wL7WR8a8/hQr8hgslhhk7SU=\");\n_c = SettingsPage;\nexport default SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "<PERSON><PERSON>", "Form", "<PERSON><PERSON>", "ListGroup", "Modal", "getAllPatterns", "createPattern", "updatePattern", "deletePattern", "getAllShifts", "jsxDEV", "_jsxDEV", "SettingsPage", "_s", "patterns", "setPatterns", "currentPattern", "setCurrentPattern", "showPatternForm", "setShowPatternForm", "availableShifts", "setAvailableShifts", "loading", "setLoading", "error", "setError", "loadPatterns", "loadAvailableShifts", "data", "err", "console", "shifts", "handleAddPattern", "name", "basePattern", "Array", "from", "length", "type", "hours", "description", "shiftId", "overrides", "handleEditPattern", "pattern", "handleDeletePattern", "id", "window", "confirm", "handleSavePattern", "handleCancelEdit", "handleBasePatternDayChange", "index", "field", "value", "newBasePattern", "shift", "find", "s", "totalHours", "startTime", "endTime", "breakMinutes", "handleOverrideChange", "overrideIndex", "newOverrides", "handleOverridePatternDayChange", "dayIndex", "newOverridePattern", "overridePattern", "handleAddOverride", "startDate", "endDate", "handleRemoveOverride", "filter", "_", "i", "handleAddOverridePatternDay", "push", "handleRemoveOverridePatternDay", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "map", "<PERSON><PERSON>", "size", "show", "onHide", "Header", "closeButton", "Title", "Body", "Group", "Label", "Control", "onChange", "e", "target", "day", "Select", "parseFloat", "as", "rows", "override", "controlId", "Footer", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/SettingsPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Form, Alert, ListGroup, Modal } from 'react-bootstrap';\nimport { getAllPatterns, createPattern, updatePattern, deletePattern, getAllShifts } from '../services/api';\n\nfunction SettingsPage() {\n  const [patterns, setPatterns] = useState([]);\n  const [currentPattern, setCurrentPattern] = useState(null);\n  const [showPatternForm, setShowPatternForm] = useState(false);\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    loadPatterns();\n    loadAvailableShifts();\n  }, []);\n\n  const loadPatterns = async () => {\n    setLoading(true);\n    try {\n      const data = await getAllPatterns();\n      setPatterns(data);\n    } catch (err) {\n      setError('Error cargando patrones.');\n      console.error('Error loading patterns:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (err) {\n      console.error('Error loading shifts:', err);\n    }\n  };\n\n  const handleAddPattern = () => {\n    setCurrentPattern({\n      name: '',\n      basePattern: Array.from({ length: 7 }, () => ({ type: '', hours: 0, description: '', shiftId: null })),\n      overrides: [],\n    });\n    setShowPatternForm(true);\n  };\n\n  const handleEditPattern = (pattern) => {\n    setCurrentPattern({ ...pattern });\n    setShowPatternForm(true);\n  };\n\n  const handleDeletePattern = async (id) => {\n    if (window.confirm('¿Estás seguro de que quieres eliminar este patrón?')) {\n      setLoading(true);\n      try {\n        await deletePattern(id);\n        await loadPatterns();\n      } catch (err) {\n        setError('Error eliminando patrón.');\n        console.error('Error deleting pattern:', err);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  const handleSavePattern = async () => {\n    if (!currentPattern || !currentPattern.name || !currentPattern.basePattern) {\n      setError('El nombre del patrón y el patrón base son requeridos.');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    try {\n      if (currentPattern.id) {\n        await updatePattern(currentPattern.id, currentPattern.name, currentPattern.basePattern, currentPattern.overrides);\n      } else {\n        await createPattern(currentPattern.name, currentPattern.basePattern, currentPattern.overrides);\n      }\n      setShowPatternForm(false);\n      setCurrentPattern(null);\n      await loadPatterns();\n    } catch (err) {\n      setError('Error guardando patrón.');\n      console.error('Error saving pattern:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancelEdit = () => {\n    setShowPatternForm(false);\n    setCurrentPattern(null);\n    setError('');\n  };\n\n  const handleBasePatternDayChange = (index, field, value) => {\n    const newBasePattern = [...currentPattern.basePattern];\n    newBasePattern[index][field] = value;\n\n    if (field === 'shiftId' && value) {\n      const shift = availableShifts.find(s => s.id === value);\n      if (shift) {\n        newBasePattern[index].type = 'worked';\n        newBasePattern[index].hours = shift.totalHours;\n        newBasePattern[index].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;\n      }\n    } else if (field === 'shiftId' && !value) {\n      newBasePattern[index].description = '';\n    }\n    setCurrentPattern({ ...currentPattern, basePattern: newBasePattern });\n  };\n\n  const handleOverrideChange = (overrideIndex, field, value) => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex][field] = value;\n    setCurrentPattern({ ...currentPattern, overrides: newOverrides });\n  };\n\n  const handleOverridePatternDayChange = (overrideIndex, dayIndex, field, value) => {\n    const newOverrides = [...currentPattern.overrides];\n    const newOverridePattern = [...newOverrides[overrideIndex].overridePattern];\n    newOverridePattern[dayIndex][field] = value;\n\n    if (field === 'shiftId' && value) {\n      const shift = availableShifts.find(s => s.id === value);\n      if (shift) {\n        newOverridePattern[dayIndex].type = 'worked';\n        newOverridePattern[dayIndex].hours = shift.totalHours;\n        newOverridePattern[dayIndex].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;\n      }\n    } else if (field === 'shiftId' && !value) {\n      newOverridePattern[dayIndex].description = '';\n    }\n\n    newOverrides[overrideIndex].overridePattern = newOverridePattern;\n    setCurrentPattern({ ...currentPattern, overrides: newOverrides });\n  };\n\n  const handleAddOverride = () => {\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: [...currentPattern.overrides, { startDate: '', endDate: '', overridePattern: [{ type: '', hours: 0, description: '', shiftId: null }] }]\n    });\n  };\n\n  const handleRemoveOverride = (overrideIndex) => {\n    const newOverrides = currentPattern.overrides.filter((_, i) => i !== overrideIndex);\n    setCurrentPattern({ ...currentPattern, overrides: newOverrides });\n  };\n\n  const handleAddOverridePatternDay = (overrideIndex) => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex].overridePattern.push({ type: '', hours: 0, description: '', shiftId: null });\n    setCurrentPattern({ ...currentPattern, overrides: newOverrides });\n  };\n\n  const handleRemoveOverridePatternDay = (overrideIndex, dayIndex) => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex].overridePattern = newOverrides[overrideIndex].overridePattern.filter((_, i) => i !== dayIndex);\n    setCurrentPattern({ ...currentPattern, overrides: newOverrides });\n  };\n\n  return (\n    <Container>\n      <Row className=\"my-4\">\n        <Col>\n          <h2>Configuración de Patrones de Calendario</h2>\n          {error && <Alert variant=\"danger\">{error}</Alert>}\n          <Button onClick={handleAddPattern} className=\"mb-3\">Crear Nuevo Patrón</Button>\n\n          {loading ? (\n            <p>Cargando patrones...</p>\n          ) : patterns.length === 0 ? (\n            <p>No hay patrones definidos.</p>\n          ) : (\n            <ListGroup>\n              {patterns.map(pattern => (\n                <ListGroup.Item key={pattern.id} className=\"d-flex justify-content-between align-items-center\">\n                  {pattern.name}\n                  <div>\n                    <Button variant=\"info\" size=\"sm\" className=\"me-2\" onClick={() => handleEditPattern(pattern)}>Editar</Button>\n                    <Button variant=\"danger\" size=\"sm\" onClick={() => handleDeletePattern(pattern.id)}>Eliminar</Button>\n                  </div>\n                </ListGroup.Item>\n              ))}\n            </ListGroup>\n          )}\n        </Col>\n      </Row>\n\n      <Modal show={showPatternForm} onHide={handleCancelEdit} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>{currentPattern && currentPattern.id ? 'Editar Patrón' : 'Crear Nuevo Patrón'}</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {currentPattern && (\n            <Form>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Nombre del Patrón</Form.Label>\n                <Form.Control\n                  type=\"text\"\n                  value={currentPattern.name}\n                  onChange={(e) => setCurrentPattern({ ...currentPattern, name: e.target.value })}\n                />\n              </Form.Group>\n\n              <h5>Patrón Base (Semanal):</h5>\n              {currentPattern.basePattern.map((day, index) => (\n                <div key={index} className=\"border p-3 mb-2\">\n                  <h6>Día {['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'][index]}</h6>\n                  <Form.Group className=\"mb-2\">\n                    <Form.Label>Tipo de Día</Form.Label>\n                    <Form.Select\n                      value={day.type}\n                      onChange={(e) => handleBasePatternDayChange(index, 'type', e.target.value)}\n                    >\n                      <option value=\"\">Seleccionar</option>\n                      <option value=\"worked\">Trabajado</option>\n                      <option value=\"holiday\">Vacaciones</option>\n                      <option value=\"permit\">Permiso</option>\n                      <option value=\"negative\">Cómputo Negativo</option>\n                    </Form.Select>\n                  </Form.Group>\n                  <Form.Group className=\"mb-2\">\n                    <Form.Label>Horas</Form.Label>\n                    <Form.Control\n                      type=\"number\"\n                      value={day.hours}\n                      onChange={(e) => handleBasePatternDayChange(index, 'hours', parseFloat(e.target.value))}\n                    />\n                  </Form.Group>\n                  <Form.Group className=\"mb-2\">\n                    <Form.Label>Turno Predefinido</Form.Label>\n                    <Form.Select\n                      value={day.shiftId || ''}\n                      onChange={(e) => handleBasePatternDayChange(index, 'shiftId', e.target.value)}\n                    >\n                      <option value=\"\">Ninguno</option>\n                      {availableShifts.map((shift) => (\n                        <option key={shift.id} value={shift.id}>\n                          {shift.name} - {shift.startTime} a {shift.endTime} ({shift.totalHours}h)\n                        </option>\n                      ))}\n                    </Form.Select>\n                  </Form.Group>\n                  <Form.Group className=\"mb-2\">\n                    <Form.Label>Descripción</Form.Label>\n                    <Form.Control\n                      as=\"textarea\"\n                      rows={1}\n                      value={day.description}\n                      onChange={(e) => handleBasePatternDayChange(index, 'description', e.target.value)}\n                    />\n                  </Form.Group>\n                </div>\n              ))}\n\n              <h5 className=\"mt-4\">Anulaciones (Overrides):</h5>\n              {currentPattern.overrides.map((override, overrideIndex) => (\n                <div key={overrideIndex} className=\"border p-3 mb-2 bg-light\">\n                  <h6>Anulación {overrideIndex + 1}</h6>\n                  <Row className=\"mb-2\">\n                    <Col>\n                      <Form.Group controlId={`overrideStartDate-${overrideIndex}`}>\n                        <Form.Label>Fecha de Inicio</Form.Label>\n                        <Form.Control\n                          type=\"date\"\n                          value={override.startDate}\n                          onChange={(e) => handleOverrideChange(overrideIndex, 'startDate', e.target.value)}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col>\n                      <Form.Group controlId={`overrideEndDate-${overrideIndex}`}>\n                        <Form.Label>Fecha de Fin</Form.Label>\n                        <Form.Control\n                          type=\"date\"\n                          value={override.endDate}\n                          onChange={(e) => handleOverrideChange(overrideIndex, 'endDate', e.target.value)}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n                  <h6>Días de la Anulación:</h6>\n                  {override.overridePattern.map((day, dayIndex) => (\n                    <div key={dayIndex} className=\"border p-3 mb-2 ms-3\">\n                      <h6>Día {dayIndex + 1}</h6>\n                      <Form.Group className=\"mb-2\">\n                        <Form.Label>Tipo de Día</Form.Label>\n                        <Form.Select\n                          value={day.type}\n                          onChange={(e) => handleOverridePatternDayChange(overrideIndex, dayIndex, 'type', e.target.value)}\n                        >\n                          <option value=\"\">Seleccionar</option>\n                          <option value=\"worked\">Trabajado</option>\n                          <option value=\"holiday\">Vacaciones</option>\n                          <option value=\"permit\">Permiso</option>\n                          <option value=\"negative\">Cómputo Negativo</option>\n                        </Form.Select>\n                      </Form.Group>\n                      <Form.Group className=\"mb-2\">\n                        <Form.Label>Horas</Form.Label>\n                        <Form.Control\n                          type=\"number\"\n                          value={day.hours}\n                          onChange={(e) => handleOverridePatternDayChange(overrideIndex, dayIndex, 'hours', parseFloat(e.target.value))}\n                        />\n                      </Form.Group>\n                      <Form.Group className=\"mb-2\">\n                        <Form.Label>Turno Predefinido</Form.Label>\n                        <Form.Select\n                          value={day.shiftId || ''}\n                          onChange={(e) => handleOverridePatternDayChange(overrideIndex, dayIndex, 'shiftId', e.target.value)}\n                        >\n                          <option value=\"\">Ninguno</option>\n                          {availableShifts.map((shift) => (\n                            <option key={shift.id} value={shift.id}>\n                              {shift.name} - {shift.startTime} a {shift.endTime} ({shift.totalHours}h)\n                            </option>\n                          ))}\n                        </Form.Select>\n                      </Form.Group>\n                      <Form.Group className=\"mb-2\">\n                        <Form.Label>Descripción</Form.Label>\n                        <Form.Control\n                          as=\"textarea\"\n                          rows={1}\n                          value={day.description}\n                          onChange={(e) => handleOverridePatternDayChange(overrideIndex, dayIndex, 'description', e.target.value)}\n                        />\n                      </Form.Group>\n                      <Button variant=\"danger\" size=\"sm\" onClick={() => handleRemoveOverridePatternDay(overrideIndex, dayIndex)}>\n                        Eliminar Día de Anulación\n                      </Button>\n                    </div>\n                  ))}\n                  <Button variant=\"secondary\" onClick={() => handleAddOverridePatternDay(overrideIndex)} className=\"mt-3\">Añadir Día a Anulación</Button>\n                  <Button variant=\"danger\" onClick={() => handleRemoveOverride(overrideIndex)} className=\"mt-3 ms-2\">Eliminar Anulación</Button>\n                </div>\n              ))}\n              <Button variant=\"info\" onClick={handleAddOverride} className=\"mt-3\">Añadir Anulación</Button>\n            </Form>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={handleCancelEdit}>Cancelar</Button>\n          <Button variant=\"primary\" onClick={handleSavePattern} disabled={loading}>Guardar Patrón</Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n}\n\nexport default SettingsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAQ,iBAAiB;AAC5F,SAASC,cAAc,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5G,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd8B,YAAY,CAAC,CAAC;IACdC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMK,IAAI,GAAG,MAAMvB,cAAc,CAAC,CAAC;MACnCU,WAAW,CAACa,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZJ,QAAQ,CAAC,0BAA0B,CAAC;MACpCK,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEK,GAAG,CAAC;IAC/C,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMI,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMI,MAAM,GAAG,MAAMtB,YAAY,CAAC,CAAC;MACnCY,kBAAkB,CAACU,MAAM,CAAC;IAC5B,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAACN,KAAK,CAAC,uBAAuB,EAAEK,GAAG,CAAC;IAC7C;EACF,CAAC;EAED,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bf,iBAAiB,CAAC;MAChBgB,IAAI,EAAE,EAAE;MACRC,WAAW,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,EAAE,OAAO;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,WAAW,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC,CAAC;MACtGC,SAAS,EAAE;IACb,CAAC,CAAC;IACFvB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMwB,iBAAiB,GAAIC,OAAO,IAAK;IACrC3B,iBAAiB,CAAC;MAAE,GAAG2B;IAAQ,CAAC,CAAC;IACjCzB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM0B,mBAAmB,GAAG,MAAOC,EAAE,IAAK;IACxC,IAAIC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACxEzB,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMf,aAAa,CAACsC,EAAE,CAAC;QACvB,MAAMpB,YAAY,CAAC,CAAC;MACtB,CAAC,CAAC,OAAOG,GAAG,EAAE;QACZJ,QAAQ,CAAC,0BAA0B,CAAC;QACpCK,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEK,GAAG,CAAC;MAC/C,CAAC,SAAS;QACRN,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;EAED,MAAM0B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACjC,cAAc,IAAI,CAACA,cAAc,CAACiB,IAAI,IAAI,CAACjB,cAAc,CAACkB,WAAW,EAAE;MAC1ET,QAAQ,CAAC,uDAAuD,CAAC;MACjE;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,IAAIT,cAAc,CAAC8B,EAAE,EAAE;QACrB,MAAMvC,aAAa,CAACS,cAAc,CAAC8B,EAAE,EAAE9B,cAAc,CAACiB,IAAI,EAAEjB,cAAc,CAACkB,WAAW,EAAElB,cAAc,CAAC0B,SAAS,CAAC;MACnH,CAAC,MAAM;QACL,MAAMpC,aAAa,CAACU,cAAc,CAACiB,IAAI,EAAEjB,cAAc,CAACkB,WAAW,EAAElB,cAAc,CAAC0B,SAAS,CAAC;MAChG;MACAvB,kBAAkB,CAAC,KAAK,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;MACvB,MAAMS,YAAY,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZJ,QAAQ,CAAC,yBAAyB,CAAC;MACnCK,OAAO,CAACN,KAAK,CAAC,uBAAuB,EAAEK,GAAG,CAAC;IAC7C,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B/B,kBAAkB,CAAC,KAAK,CAAC;IACzBF,iBAAiB,CAAC,IAAI,CAAC;IACvBQ,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAM0B,0BAA0B,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,KAAK;IAC1D,MAAMC,cAAc,GAAG,CAAC,GAAGvC,cAAc,CAACkB,WAAW,CAAC;IACtDqB,cAAc,CAACH,KAAK,CAAC,CAACC,KAAK,CAAC,GAAGC,KAAK;IAEpC,IAAID,KAAK,KAAK,SAAS,IAAIC,KAAK,EAAE;MAChC,MAAME,KAAK,GAAGpC,eAAe,CAACqC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,EAAE,KAAKQ,KAAK,CAAC;MACvD,IAAIE,KAAK,EAAE;QACTD,cAAc,CAACH,KAAK,CAAC,CAACd,IAAI,GAAG,QAAQ;QACrCiB,cAAc,CAACH,KAAK,CAAC,CAACb,KAAK,GAAGiB,KAAK,CAACG,UAAU;QAC9CJ,cAAc,CAACH,KAAK,CAAC,CAACZ,WAAW,GAAG,GAAGgB,KAAK,CAACvB,IAAI,KAAKuB,KAAK,CAACI,SAAS,MAAMJ,KAAK,CAACK,OAAO,KAAKL,KAAK,CAACM,YAAY,eAAe;MAChI;IACF,CAAC,MAAM,IAAIT,KAAK,KAAK,SAAS,IAAI,CAACC,KAAK,EAAE;MACxCC,cAAc,CAACH,KAAK,CAAC,CAACZ,WAAW,GAAG,EAAE;IACxC;IACAvB,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAEkB,WAAW,EAAEqB;IAAe,CAAC,CAAC;EACvE,CAAC;EAED,MAAMQ,oBAAoB,GAAGA,CAACC,aAAa,EAAEX,KAAK,EAAEC,KAAK,KAAK;IAC5D,MAAMW,YAAY,GAAG,CAAC,GAAGjD,cAAc,CAAC0B,SAAS,CAAC;IAClDuB,YAAY,CAACD,aAAa,CAAC,CAACX,KAAK,CAAC,GAAGC,KAAK;IAC1CrC,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAE0B,SAAS,EAAEuB;IAAa,CAAC,CAAC;EACnE,CAAC;EAED,MAAMC,8BAA8B,GAAGA,CAACF,aAAa,EAAEG,QAAQ,EAAEd,KAAK,EAAEC,KAAK,KAAK;IAChF,MAAMW,YAAY,GAAG,CAAC,GAAGjD,cAAc,CAAC0B,SAAS,CAAC;IAClD,MAAM0B,kBAAkB,GAAG,CAAC,GAAGH,YAAY,CAACD,aAAa,CAAC,CAACK,eAAe,CAAC;IAC3ED,kBAAkB,CAACD,QAAQ,CAAC,CAACd,KAAK,CAAC,GAAGC,KAAK;IAE3C,IAAID,KAAK,KAAK,SAAS,IAAIC,KAAK,EAAE;MAChC,MAAME,KAAK,GAAGpC,eAAe,CAACqC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,EAAE,KAAKQ,KAAK,CAAC;MACvD,IAAIE,KAAK,EAAE;QACTY,kBAAkB,CAACD,QAAQ,CAAC,CAAC7B,IAAI,GAAG,QAAQ;QAC5C8B,kBAAkB,CAACD,QAAQ,CAAC,CAAC5B,KAAK,GAAGiB,KAAK,CAACG,UAAU;QACrDS,kBAAkB,CAACD,QAAQ,CAAC,CAAC3B,WAAW,GAAG,GAAGgB,KAAK,CAACvB,IAAI,KAAKuB,KAAK,CAACI,SAAS,MAAMJ,KAAK,CAACK,OAAO,KAAKL,KAAK,CAACM,YAAY,eAAe;MACvI;IACF,CAAC,MAAM,IAAIT,KAAK,KAAK,SAAS,IAAI,CAACC,KAAK,EAAE;MACxCc,kBAAkB,CAACD,QAAQ,CAAC,CAAC3B,WAAW,GAAG,EAAE;IAC/C;IAEAyB,YAAY,CAACD,aAAa,CAAC,CAACK,eAAe,GAAGD,kBAAkB;IAChEnD,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAE0B,SAAS,EAAEuB;IAAa,CAAC,CAAC;EACnE,CAAC;EAED,MAAMK,iBAAiB,GAAGA,CAAA,KAAM;IAC9BrD,iBAAiB,CAAC;MAChB,GAAGD,cAAc;MACjB0B,SAAS,EAAE,CAAC,GAAG1B,cAAc,CAAC0B,SAAS,EAAE;QAAE6B,SAAS,EAAE,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEH,eAAe,EAAE,CAAC;UAAE/B,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,CAAC;UAAEC,WAAW,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAK,CAAC;MAAE,CAAC;IACpJ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgC,oBAAoB,GAAIT,aAAa,IAAK;IAC9C,MAAMC,YAAY,GAAGjD,cAAc,CAAC0B,SAAS,CAACgC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKZ,aAAa,CAAC;IACnF/C,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAE0B,SAAS,EAAEuB;IAAa,CAAC,CAAC;EACnE,CAAC;EAED,MAAMY,2BAA2B,GAAIb,aAAa,IAAK;IACrD,MAAMC,YAAY,GAAG,CAAC,GAAGjD,cAAc,CAAC0B,SAAS,CAAC;IAClDuB,YAAY,CAACD,aAAa,CAAC,CAACK,eAAe,CAACS,IAAI,CAAC;MAAExC,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,WAAW,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IACxGxB,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAE0B,SAAS,EAAEuB;IAAa,CAAC,CAAC;EACnE,CAAC;EAED,MAAMc,8BAA8B,GAAGA,CAACf,aAAa,EAAEG,QAAQ,KAAK;IAClE,MAAMF,YAAY,GAAG,CAAC,GAAGjD,cAAc,CAAC0B,SAAS,CAAC;IAClDuB,YAAY,CAACD,aAAa,CAAC,CAACK,eAAe,GAAGJ,YAAY,CAACD,aAAa,CAAC,CAACK,eAAe,CAACK,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKT,QAAQ,CAAC;IAC1HlD,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAE0B,SAAS,EAAEuB;IAAa,CAAC,CAAC;EACnE,CAAC;EAED,oBACEtD,OAAA,CAACd,SAAS;IAAAmF,QAAA,gBACRrE,OAAA,CAACb,GAAG;MAACmF,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBrE,OAAA,CAACZ,GAAG;QAAAiF,QAAA,gBACFrE,OAAA;UAAAqE,QAAA,EAAI;QAAuC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC/C7D,KAAK,iBAAIb,OAAA,CAACT,KAAK;UAACoF,OAAO,EAAC,QAAQ;UAAAN,QAAA,EAAExD;QAAK;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACjD1E,OAAA,CAACX,MAAM;UAACuF,OAAO,EAAEvD,gBAAiB;UAACiD,SAAS,EAAC,MAAM;UAAAD,QAAA,EAAC;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAE9E/D,OAAO,gBACNX,OAAA;UAAAqE,QAAA,EAAG;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,GACzBvE,QAAQ,CAACuB,MAAM,KAAK,CAAC,gBACvB1B,OAAA;UAAAqE,QAAA,EAAG;QAA0B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAEjC1E,OAAA,CAACR,SAAS;UAAA6E,QAAA,EACPlE,QAAQ,CAAC0E,GAAG,CAAC5C,OAAO,iBACnBjC,OAAA,CAACR,SAAS,CAACsF,IAAI;YAAkBR,SAAS,EAAC,mDAAmD;YAAAD,QAAA,GAC3FpC,OAAO,CAACX,IAAI,eACbtB,OAAA;cAAAqE,QAAA,gBACErE,OAAA,CAACX,MAAM;gBAACsF,OAAO,EAAC,MAAM;gBAACI,IAAI,EAAC,IAAI;gBAACT,SAAS,EAAC,MAAM;gBAACM,OAAO,EAAEA,CAAA,KAAM5C,iBAAiB,CAACC,OAAO,CAAE;gBAAAoC,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5G1E,OAAA,CAACX,MAAM;gBAACsF,OAAO,EAAC,QAAQ;gBAACI,IAAI,EAAC,IAAI;gBAACH,OAAO,EAAEA,CAAA,KAAM1C,mBAAmB,CAACD,OAAO,CAACE,EAAE,CAAE;gBAAAkC,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjG,CAAC;UAAA,GALazC,OAAO,CAACE,EAAE;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMf,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CACZ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN1E,OAAA,CAACP,KAAK;MAACuF,IAAI,EAAEzE,eAAgB;MAAC0E,MAAM,EAAE1C,gBAAiB;MAACwC,IAAI,EAAC,IAAI;MAAAV,QAAA,gBAC/DrE,OAAA,CAACP,KAAK,CAACyF,MAAM;QAACC,WAAW;QAAAd,QAAA,eACvBrE,OAAA,CAACP,KAAK,CAAC2F,KAAK;UAAAf,QAAA,EAAEhE,cAAc,IAAIA,cAAc,CAAC8B,EAAE,GAAG,eAAe,GAAG;QAAoB;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F,CAAC,eACf1E,OAAA,CAACP,KAAK,CAAC4F,IAAI;QAAAhB,QAAA,EACRhE,cAAc,iBACbL,OAAA,CAACV,IAAI;UAAA+E,QAAA,gBACHrE,OAAA,CAACV,IAAI,CAACgG,KAAK;YAAChB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BrE,OAAA,CAACV,IAAI,CAACiG,KAAK;cAAAlB,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1C1E,OAAA,CAACV,IAAI,CAACkG,OAAO;cACX7D,IAAI,EAAC,MAAM;cACXgB,KAAK,EAAEtC,cAAc,CAACiB,IAAK;cAC3BmE,QAAQ,EAAGC,CAAC,IAAKpF,iBAAiB,CAAC;gBAAE,GAAGD,cAAc;gBAAEiB,IAAI,EAAEoE,CAAC,CAACC,MAAM,CAAChD;cAAM,CAAC;YAAE;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEb1E,OAAA;YAAAqE,QAAA,EAAI;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC9BrE,cAAc,CAACkB,WAAW,CAACsD,GAAG,CAAC,CAACe,GAAG,EAAEnD,KAAK,kBACzCzC,OAAA;YAAiBsE,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC1CrE,OAAA;cAAAqE,QAAA,GAAI,SAAI,EAAC,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC5B,KAAK,CAAC;YAAA;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChG1E,OAAA,CAACV,IAAI,CAACgG,KAAK;cAAChB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BrE,OAAA,CAACV,IAAI,CAACiG,KAAK;gBAAAlB,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpC1E,OAAA,CAACV,IAAI,CAACuG,MAAM;gBACVlD,KAAK,EAAEiD,GAAG,CAACjE,IAAK;gBAChB8D,QAAQ,EAAGC,CAAC,IAAKlD,0BAA0B,CAACC,KAAK,EAAE,MAAM,EAAEiD,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAE;gBAAA0B,QAAA,gBAE3ErE,OAAA;kBAAQ2C,KAAK,EAAC,EAAE;kBAAA0B,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrC1E,OAAA;kBAAQ2C,KAAK,EAAC,QAAQ;kBAAA0B,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzC1E,OAAA;kBAAQ2C,KAAK,EAAC,SAAS;kBAAA0B,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3C1E,OAAA;kBAAQ2C,KAAK,EAAC,QAAQ;kBAAA0B,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvC1E,OAAA;kBAAQ2C,KAAK,EAAC,UAAU;kBAAA0B,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACb1E,OAAA,CAACV,IAAI,CAACgG,KAAK;cAAChB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BrE,OAAA,CAACV,IAAI,CAACiG,KAAK;gBAAAlB,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9B1E,OAAA,CAACV,IAAI,CAACkG,OAAO;gBACX7D,IAAI,EAAC,QAAQ;gBACbgB,KAAK,EAAEiD,GAAG,CAAChE,KAAM;gBACjB6D,QAAQ,EAAGC,CAAC,IAAKlD,0BAA0B,CAACC,KAAK,EAAE,OAAO,EAAEqD,UAAU,CAACJ,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAC;cAAE;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eACb1E,OAAA,CAACV,IAAI,CAACgG,KAAK;cAAChB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BrE,OAAA,CAACV,IAAI,CAACiG,KAAK;gBAAAlB,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1C1E,OAAA,CAACV,IAAI,CAACuG,MAAM;gBACVlD,KAAK,EAAEiD,GAAG,CAAC9D,OAAO,IAAI,EAAG;gBACzB2D,QAAQ,EAAGC,CAAC,IAAKlD,0BAA0B,CAACC,KAAK,EAAE,SAAS,EAAEiD,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAE;gBAAA0B,QAAA,gBAE9ErE,OAAA;kBAAQ2C,KAAK,EAAC,EAAE;kBAAA0B,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAChCjE,eAAe,CAACoE,GAAG,CAAEhC,KAAK,iBACzB7C,OAAA;kBAAuB2C,KAAK,EAAEE,KAAK,CAACV,EAAG;kBAAAkC,QAAA,GACpCxB,KAAK,CAACvB,IAAI,EAAC,KAAG,EAACuB,KAAK,CAACI,SAAS,EAAC,KAAG,EAACJ,KAAK,CAACK,OAAO,EAAC,IAAE,EAACL,KAAK,CAACG,UAAU,EAAC,IACxE;gBAAA,GAFaH,KAAK,CAACV,EAAE;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACb1E,OAAA,CAACV,IAAI,CAACgG,KAAK;cAAChB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BrE,OAAA,CAACV,IAAI,CAACiG,KAAK;gBAAAlB,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpC1E,OAAA,CAACV,IAAI,CAACkG,OAAO;gBACXO,EAAE,EAAC,UAAU;gBACbC,IAAI,EAAE,CAAE;gBACRrD,KAAK,EAAEiD,GAAG,CAAC/D,WAAY;gBACvB4D,QAAQ,EAAGC,CAAC,IAAKlD,0BAA0B,CAACC,KAAK,EAAE,aAAa,EAAEiD,CAAC,CAACC,MAAM,CAAChD,KAAK;cAAE;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA,GA7CLjC,KAAK;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8CV,CACN,CAAC,eAEF1E,OAAA;YAAIsE,SAAS,EAAC,MAAM;YAAAD,QAAA,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACjDrE,cAAc,CAAC0B,SAAS,CAAC8C,GAAG,CAAC,CAACoB,QAAQ,EAAE5C,aAAa,kBACpDrD,OAAA;YAAyBsE,SAAS,EAAC,0BAA0B;YAAAD,QAAA,gBAC3DrE,OAAA;cAAAqE,QAAA,GAAI,eAAU,EAAChB,aAAa,GAAG,CAAC;YAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtC1E,OAAA,CAACb,GAAG;cAACmF,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBrE,OAAA,CAACZ,GAAG;gBAAAiF,QAAA,eACFrE,OAAA,CAACV,IAAI,CAACgG,KAAK;kBAACY,SAAS,EAAE,qBAAqB7C,aAAa,EAAG;kBAAAgB,QAAA,gBAC1DrE,OAAA,CAACV,IAAI,CAACiG,KAAK;oBAAAlB,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACxC1E,OAAA,CAACV,IAAI,CAACkG,OAAO;oBACX7D,IAAI,EAAC,MAAM;oBACXgB,KAAK,EAAEsD,QAAQ,CAACrC,SAAU;oBAC1B6B,QAAQ,EAAGC,CAAC,IAAKtC,oBAAoB,CAACC,aAAa,EAAE,WAAW,EAAEqC,CAAC,CAACC,MAAM,CAAChD,KAAK;kBAAE;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN1E,OAAA,CAACZ,GAAG;gBAAAiF,QAAA,eACFrE,OAAA,CAACV,IAAI,CAACgG,KAAK;kBAACY,SAAS,EAAE,mBAAmB7C,aAAa,EAAG;kBAAAgB,QAAA,gBACxDrE,OAAA,CAACV,IAAI,CAACiG,KAAK;oBAAAlB,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrC1E,OAAA,CAACV,IAAI,CAACkG,OAAO;oBACX7D,IAAI,EAAC,MAAM;oBACXgB,KAAK,EAAEsD,QAAQ,CAACpC,OAAQ;oBACxB4B,QAAQ,EAAGC,CAAC,IAAKtC,oBAAoB,CAACC,aAAa,EAAE,SAAS,EAAEqC,CAAC,CAACC,MAAM,CAAChD,KAAK;kBAAE;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1E,OAAA;cAAAqE,QAAA,EAAI;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC7BuB,QAAQ,CAACvC,eAAe,CAACmB,GAAG,CAAC,CAACe,GAAG,EAAEpC,QAAQ,kBAC1CxD,OAAA;cAAoBsE,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBAClDrE,OAAA;gBAAAqE,QAAA,GAAI,SAAI,EAACb,QAAQ,GAAG,CAAC;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3B1E,OAAA,CAACV,IAAI,CAACgG,KAAK;gBAAChB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BrE,OAAA,CAACV,IAAI,CAACiG,KAAK;kBAAAlB,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpC1E,OAAA,CAACV,IAAI,CAACuG,MAAM;kBACVlD,KAAK,EAAEiD,GAAG,CAACjE,IAAK;kBAChB8D,QAAQ,EAAGC,CAAC,IAAKnC,8BAA8B,CAACF,aAAa,EAAEG,QAAQ,EAAE,MAAM,EAAEkC,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAE;kBAAA0B,QAAA,gBAEjGrE,OAAA;oBAAQ2C,KAAK,EAAC,EAAE;oBAAA0B,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrC1E,OAAA;oBAAQ2C,KAAK,EAAC,QAAQ;oBAAA0B,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzC1E,OAAA;oBAAQ2C,KAAK,EAAC,SAAS;oBAAA0B,QAAA,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC3C1E,OAAA;oBAAQ2C,KAAK,EAAC,QAAQ;oBAAA0B,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvC1E,OAAA;oBAAQ2C,KAAK,EAAC,UAAU;oBAAA0B,QAAA,EAAC;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACb1E,OAAA,CAACV,IAAI,CAACgG,KAAK;gBAAChB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BrE,OAAA,CAACV,IAAI,CAACiG,KAAK;kBAAAlB,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9B1E,OAAA,CAACV,IAAI,CAACkG,OAAO;kBACX7D,IAAI,EAAC,QAAQ;kBACbgB,KAAK,EAAEiD,GAAG,CAAChE,KAAM;kBACjB6D,QAAQ,EAAGC,CAAC,IAAKnC,8BAA8B,CAACF,aAAa,EAAEG,QAAQ,EAAE,OAAO,EAAEsC,UAAU,CAACJ,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAC;gBAAE;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eACb1E,OAAA,CAACV,IAAI,CAACgG,KAAK;gBAAChB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BrE,OAAA,CAACV,IAAI,CAACiG,KAAK;kBAAAlB,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1C1E,OAAA,CAACV,IAAI,CAACuG,MAAM;kBACVlD,KAAK,EAAEiD,GAAG,CAAC9D,OAAO,IAAI,EAAG;kBACzB2D,QAAQ,EAAGC,CAAC,IAAKnC,8BAA8B,CAACF,aAAa,EAAEG,QAAQ,EAAE,SAAS,EAAEkC,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAE;kBAAA0B,QAAA,gBAEpGrE,OAAA;oBAAQ2C,KAAK,EAAC,EAAE;oBAAA0B,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAChCjE,eAAe,CAACoE,GAAG,CAAEhC,KAAK,iBACzB7C,OAAA;oBAAuB2C,KAAK,EAAEE,KAAK,CAACV,EAAG;oBAAAkC,QAAA,GACpCxB,KAAK,CAACvB,IAAI,EAAC,KAAG,EAACuB,KAAK,CAACI,SAAS,EAAC,KAAG,EAACJ,KAAK,CAACK,OAAO,EAAC,IAAE,EAACL,KAAK,CAACG,UAAU,EAAC,IACxE;kBAAA,GAFaH,KAAK,CAACV,EAAE;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEb,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACb1E,OAAA,CAACV,IAAI,CAACgG,KAAK;gBAAChB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BrE,OAAA,CAACV,IAAI,CAACiG,KAAK;kBAAAlB,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpC1E,OAAA,CAACV,IAAI,CAACkG,OAAO;kBACXO,EAAE,EAAC,UAAU;kBACbC,IAAI,EAAE,CAAE;kBACRrD,KAAK,EAAEiD,GAAG,CAAC/D,WAAY;kBACvB4D,QAAQ,EAAGC,CAAC,IAAKnC,8BAA8B,CAACF,aAAa,EAAEG,QAAQ,EAAE,aAAa,EAAEkC,CAAC,CAACC,MAAM,CAAChD,KAAK;gBAAE;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eACb1E,OAAA,CAACX,MAAM;gBAACsF,OAAO,EAAC,QAAQ;gBAACI,IAAI,EAAC,IAAI;gBAACH,OAAO,EAAEA,CAAA,KAAMR,8BAA8B,CAACf,aAAa,EAAEG,QAAQ,CAAE;gBAAAa,QAAA,EAAC;cAE3G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GAhDDlB,QAAQ;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiDb,CACN,CAAC,eACF1E,OAAA,CAACX,MAAM;cAACsF,OAAO,EAAC,WAAW;cAACC,OAAO,EAAEA,CAAA,KAAMV,2BAA2B,CAACb,aAAa,CAAE;cAACiB,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvI1E,OAAA,CAACX,MAAM;cAACsF,OAAO,EAAC,QAAQ;cAACC,OAAO,EAAEA,CAAA,KAAMd,oBAAoB,CAACT,aAAa,CAAE;cAACiB,SAAS,EAAC,WAAW;cAAAD,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,GA9EtHrB,aAAa;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+ElB,CACN,CAAC,eACF1E,OAAA,CAACX,MAAM;YAACsF,OAAO,EAAC,MAAM;YAACC,OAAO,EAAEjB,iBAAkB;YAACW,SAAS,EAAC,MAAM;YAAAD,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACb1E,OAAA,CAACP,KAAK,CAAC0G,MAAM;QAAA9B,QAAA,gBACXrE,OAAA,CAACX,MAAM;UAACsF,OAAO,EAAC,WAAW;UAACC,OAAO,EAAErC,gBAAiB;UAAA8B,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxE1E,OAAA,CAACX,MAAM;UAACsF,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEtC,iBAAkB;UAAC8D,QAAQ,EAAEzF,OAAQ;UAAA0D,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB;AAACxE,EAAA,CA/VQD,YAAY;AAAAoG,EAAA,GAAZpG,YAAY;AAiWrB,eAAeA,YAAY;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}