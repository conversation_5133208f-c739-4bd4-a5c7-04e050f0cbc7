const patternService = require('./src/services/patternService');

console.log('Testing pattern service...');

try {
    // Test getting all patterns
    console.log('1. Getting all patterns...');
    const patterns = patternService.getAllPatterns();
    console.log('Patterns:', patterns);
    
    // Test creating a pattern
    console.log('2. Creating a test pattern...');
    const basePattern = [
        { type: 'holiday', hours: 0, description: 'Domingo' },
        { type: 'worked', hours: 8, description: 'Lu<PERSON>' },
        { type: 'worked', hours: 8, description: '<PERSON><PERSON>' },
        { type: 'worked', hours: 8, description: 'Miércoles' },
        { type: 'worked', hours: 8, description: 'Jueves' },
        { type: 'worked', hours: 8, description: '<PERSON><PERSON><PERSON>' },
        { type: 'holiday', hours: 0, description: 'Sábado' }
    ];
    
    const newPattern = patternService.createPattern('Test Pattern', basePattern, []);
    console.log('Created pattern:', newPattern);
    
    // Test getting patterns again
    console.log('3. Getting all patterns after creation...');
    const patternsAfter = patternService.getAllPatterns();
    console.log('Patterns after creation:', patternsAfter);
    
} catch (error) {
    console.error('Error testing pattern service:', error);
}
