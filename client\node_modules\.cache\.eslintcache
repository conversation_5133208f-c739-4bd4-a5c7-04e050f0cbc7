[{"D:\\Proyectos Python\\Horario\\client\\src\\index.js": "1", "D:\\Proyectos Python\\Horario\\client\\src\\App.js": "2", "D:\\Proyectos Python\\Horario\\client\\src\\reportWebVitals.js": "3", "D:\\Proyectos Python\\Horario\\client\\src\\pages\\SettingsPage.js": "4", "D:\\Proyectos Python\\Horario\\client\\src\\pages\\CalendarPage.js": "5", "D:\\Proyectos Python\\Horario\\client\\src\\pages\\ReportsPage.js": "6", "D:\\Proyectos Python\\Horario\\client\\src\\components\\NavbarComponent.js": "7", "D:\\Proyectos Python\\Horario\\client\\src\\services\\api.js": "8", "D:\\Proyectos Python\\Horario\\client\\src\\components\\RealCalendarManager.js": "9", "D:\\Proyectos Python\\Horario\\client\\src\\components\\DualCalendarView.js": "10", "D:\\Proyectos Python\\Horario\\client\\src\\components\\CalendarAnalysis.js": "11"}, {"size": 512, "mtime": 1751325868914, "results": "12", "hashOfConfig": "13"}, {"size": 732, "mtime": 1751325868913, "results": "14", "hashOfConfig": "13"}, {"size": 362, "mtime": 1751325821295, "results": "15", "hashOfConfig": "13"}, {"size": 18252, "mtime": 1751360560488, "results": "16", "hashOfConfig": "13"}, {"size": 9393, "mtime": 1751404022201, "results": "17", "hashOfConfig": "13"}, {"size": 5910, "mtime": 1751367804647, "results": "18", "hashOfConfig": "13"}, {"size": 737, "mtime": 1751326777823, "results": "19", "hashOfConfig": "13"}, {"size": 9006, "mtime": 1751398065150, "results": "20", "hashOfConfig": "13"}, {"size": 11320, "mtime": 1751399793119, "results": "21", "hashOfConfig": "13"}, {"size": 9553, "mtime": 1751386263695, "results": "22", "hashOfConfig": "13"}, {"size": 10314, "mtime": 1751386458858, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "18a8vct", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 44, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Proyectos Python\\Horario\\client\\src\\index.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\App.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\reportWebVitals.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\pages\\SettingsPage.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\pages\\CalendarPage.js", ["57", "58", "59", "60", "61", "62", "63", "64", "65", "66", "67", "68", "69", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "110", "111"], [], "D:\\Proyectos Python\\Horario\\client\\src\\pages\\ReportsPage.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\components\\NavbarComponent.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\services\\api.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\components\\RealCalendarManager.js", ["112", "113", "114"], [], "D:\\Proyectos Python\\Horario\\client\\src\\components\\DualCalendarView.js", ["115", "116", "117"], [], "D:\\Proyectos Python\\Horario\\client\\src\\components\\CalendarAnalysis.js", ["118"], [], {"ruleId": "119", "severity": 1, "message": "120", "line": 2, "column": 39, "nodeType": "121", "messageId": "122", "endLine": 2, "endColumn": 44}, {"ruleId": "119", "severity": 1, "message": "123", "line": 2, "column": 59, "nodeType": "121", "messageId": "122", "endLine": 2, "endColumn": 64}, {"ruleId": "119", "severity": 1, "message": "124", "line": 7, "column": 3, "nodeType": "121", "messageId": "122", "endLine": 7, "endColumn": 15}, {"ruleId": "119", "severity": 1, "message": "125", "line": 8, "column": 3, "nodeType": "121", "messageId": "122", "endLine": 8, "endColumn": 20}, {"ruleId": "119", "severity": 1, "message": "126", "line": 9, "column": 3, "nodeType": "121", "messageId": "122", "endLine": 9, "endColumn": 15}, {"ruleId": "119", "severity": 1, "message": "127", "line": 10, "column": 3, "nodeType": "121", "messageId": "122", "endLine": 10, "endColumn": 19}, {"ruleId": "119", "severity": 1, "message": "128", "line": 11, "column": 3, "nodeType": "121", "messageId": "122", "endLine": 11, "endColumn": 23}, {"ruleId": "119", "severity": 1, "message": "129", "line": 12, "column": 3, "nodeType": "121", "messageId": "122", "endLine": 12, "endColumn": 15}, {"ruleId": "119", "severity": 1, "message": "130", "line": 23, "column": 10, "nodeType": "121", "messageId": "122", "endLine": 23, "endColumn": 26}, {"ruleId": "119", "severity": 1, "message": "131", "line": 23, "column": 28, "nodeType": "121", "messageId": "122", "endLine": 23, "endColumn": 47}, {"ruleId": "132", "severity": 2, "message": "133", "line": 39, "column": 7, "nodeType": "121", "messageId": "134", "endLine": 39, "endColumn": 21}, {"ruleId": "119", "severity": 1, "message": "135", "line": 70, "column": 9, "nodeType": "121", "messageId": "122", "endLine": 70, "endColumn": 31}, {"ruleId": "132", "severity": 2, "message": "136", "line": 71, "column": 10, "nodeType": "121", "messageId": "134", "endLine": 71, "endColumn": 28}, {"ruleId": "132", "severity": 2, "message": "137", "line": 72, "column": 7, "nodeType": "121", "messageId": "134", "endLine": 72, "endColumn": 28}, {"ruleId": "132", "severity": 2, "message": "138", "line": 73, "column": 7, "nodeType": "121", "messageId": "134", "endLine": 73, "endColumn": 26}, {"ruleId": "132", "severity": 2, "message": "139", "line": 74, "column": 17, "nodeType": "121", "messageId": "134", "endLine": 74, "endColumn": 33}, {"ruleId": "132", "severity": 2, "message": "136", "line": 75, "column": 19, "nodeType": "121", "messageId": "134", "endLine": 75, "endColumn": 37}, {"ruleId": "132", "severity": 2, "message": "138", "line": 76, "column": 9, "nodeType": "121", "messageId": "134", "endLine": 76, "endColumn": 28}, {"ruleId": "132", "severity": 2, "message": "136", "line": 76, "column": 29, "nodeType": "121", "messageId": "134", "endLine": 76, "endColumn": 47}, {"ruleId": "132", "severity": 2, "message": "137", "line": 77, "column": 9, "nodeType": "121", "messageId": "134", "endLine": 77, "endColumn": 30}, {"ruleId": "132", "severity": 2, "message": "138", "line": 79, "column": 9, "nodeType": "121", "messageId": "134", "endLine": 79, "endColumn": 28}, {"ruleId": "132", "severity": 2, "message": "137", "line": 82, "column": 7, "nodeType": "121", "messageId": "134", "endLine": 82, "endColumn": 28}, {"ruleId": "132", "severity": 2, "message": "138", "line": 83, "column": 7, "nodeType": "121", "messageId": "134", "endLine": 83, "endColumn": 26}, {"ruleId": "132", "severity": 2, "message": "136", "line": 133, "column": 28, "nodeType": "121", "messageId": "134", "endLine": 133, "endColumn": 46}, {"ruleId": "132", "severity": 2, "message": "136", "line": 133, "column": 49, "nodeType": "121", "messageId": "134", "endLine": 133, "endColumn": 67}, {"ruleId": "132", "severity": 2, "message": "137", "line": 134, "column": 38, "nodeType": "121", "messageId": "134", "endLine": 134, "endColumn": 59}, {"ruleId": "132", "severity": 2, "message": "139", "line": 143, "column": 28, "nodeType": "121", "messageId": "134", "endLine": 143, "endColumn": 44}, {"ruleId": "132", "severity": 2, "message": "139", "line": 143, "column": 47, "nodeType": "121", "messageId": "134", "endLine": 143, "endColumn": 63}, {"ruleId": "132", "severity": 2, "message": "138", "line": 144, "column": 38, "nodeType": "121", "messageId": "134", "endLine": 144, "endColumn": 57}, {"ruleId": "132", "severity": 2, "message": "140", "line": 153, "column": 24, "nodeType": "121", "messageId": "134", "endLine": 153, "endColumn": 41}, {"ruleId": "132", "severity": 2, "message": "141", "line": 154, "column": 34, "nodeType": "121", "messageId": "134", "endLine": 154, "endColumn": 54}, {"ruleId": "132", "severity": 2, "message": "142", "line": 157, "column": 18, "nodeType": "121", "messageId": "134", "endLine": 157, "endColumn": 29}, {"ruleId": "132", "severity": 2, "message": "143", "line": 167, "column": 24, "nodeType": "121", "messageId": "134", "endLine": 167, "endColumn": 42}, {"ruleId": "132", "severity": 2, "message": "140", "line": 168, "column": 37, "nodeType": "121", "messageId": "134", "endLine": 168, "endColumn": 54}, {"ruleId": "132", "severity": 2, "message": "136", "line": 168, "column": 59, "nodeType": "121", "messageId": "134", "endLine": 168, "endColumn": 77}, {"ruleId": "132", "severity": 2, "message": "139", "line": 168, "column": 82, "nodeType": "121", "messageId": "134", "endLine": 168, "endColumn": 98}, {"ruleId": "132", "severity": 2, "message": "137", "line": 175, "column": 17, "nodeType": "121", "messageId": "134", "endLine": 175, "endColumn": 38}, {"ruleId": "132", "severity": 2, "message": "138", "line": 176, "column": 17, "nodeType": "121", "messageId": "134", "endLine": 176, "endColumn": 36}, {"ruleId": "132", "severity": 2, "message": "141", "line": 177, "column": 17, "nodeType": "121", "messageId": "134", "endLine": 177, "endColumn": 37}, {"ruleId": "132", "severity": 2, "message": "136", "line": 203, "column": 28, "nodeType": "121", "messageId": "134", "endLine": 203, "endColumn": 46}, {"ruleId": "132", "severity": 2, "message": "136", "line": 203, "column": 49, "nodeType": "121", "messageId": "134", "endLine": 203, "endColumn": 67}, {"ruleId": "132", "severity": 2, "message": "137", "line": 204, "column": 38, "nodeType": "121", "messageId": "134", "endLine": 204, "endColumn": 59}, {"ruleId": "132", "severity": 2, "message": "139", "line": 213, "column": 28, "nodeType": "121", "messageId": "134", "endLine": 213, "endColumn": 44}, {"ruleId": "132", "severity": 2, "message": "139", "line": 213, "column": 47, "nodeType": "121", "messageId": "134", "endLine": 213, "endColumn": 63}, {"ruleId": "132", "severity": 2, "message": "138", "line": 214, "column": 38, "nodeType": "121", "messageId": "134", "endLine": 214, "endColumn": 57}, {"ruleId": "132", "severity": 2, "message": "140", "line": 223, "column": 24, "nodeType": "121", "messageId": "134", "endLine": 223, "endColumn": 41}, {"ruleId": "132", "severity": 2, "message": "141", "line": 224, "column": 34, "nodeType": "121", "messageId": "134", "endLine": 224, "endColumn": 54}, {"ruleId": "132", "severity": 2, "message": "142", "line": 227, "column": 18, "nodeType": "121", "messageId": "134", "endLine": 227, "endColumn": 29}, {"ruleId": "132", "severity": 2, "message": "143", "line": 237, "column": 24, "nodeType": "121", "messageId": "134", "endLine": 237, "endColumn": 42}, {"ruleId": "132", "severity": 2, "message": "140", "line": 238, "column": 37, "nodeType": "121", "messageId": "134", "endLine": 238, "endColumn": 54}, {"ruleId": "132", "severity": 2, "message": "136", "line": 238, "column": 59, "nodeType": "121", "messageId": "134", "endLine": 238, "endColumn": 77}, {"ruleId": "132", "severity": 2, "message": "139", "line": 238, "column": 82, "nodeType": "121", "messageId": "134", "endLine": 238, "endColumn": 98}, {"ruleId": "132", "severity": 2, "message": "137", "line": 245, "column": 17, "nodeType": "121", "messageId": "134", "endLine": 245, "endColumn": 38}, {"ruleId": "132", "severity": 2, "message": "138", "line": 246, "column": 17, "nodeType": "121", "messageId": "134", "endLine": 246, "endColumn": 36}, {"ruleId": "132", "severity": 2, "message": "141", "line": 247, "column": 17, "nodeType": "121", "messageId": "134", "endLine": 247, "endColumn": 37}, {"ruleId": "119", "severity": 1, "message": "127", "line": 7, "column": 3, "nodeType": "121", "messageId": "122", "endLine": 7, "endColumn": 19}, {"ruleId": "144", "severity": 1, "message": "145", "line": 33, "column": 6, "nodeType": "146", "endLine": 33, "endColumn": 26, "suggestions": "147"}, {"ruleId": "132", "severity": 2, "message": "148", "line": 143, "column": 13, "nodeType": "121", "messageId": "134", "endLine": 143, "endColumn": 26}, {"ruleId": "119", "severity": 1, "message": "149", "line": 8, "column": 3, "nodeType": "121", "messageId": "122", "endLine": 8, "endColumn": 19}, {"ruleId": "119", "severity": 1, "message": "150", "line": 9, "column": 3, "nodeType": "121", "messageId": "122", "endLine": 9, "endColumn": 22}, {"ruleId": "144", "severity": 1, "message": "151", "line": 29, "column": 6, "nodeType": "146", "endLine": 29, "endColumn": 43, "suggestions": "152"}, {"ruleId": "144", "severity": 1, "message": "153", "line": 37, "column": 6, "nodeType": "146", "endLine": 37, "endColumn": 17, "suggestions": "154"}, "no-unused-vars", "'Modal' is defined but never used.", "Identifier", "unusedVar", "'Badge' is defined but never used.", "'getDayByDate' is defined but never used.", "'createOrUpdateDay' is defined but never used.", "'getAllShifts' is defined but never used.", "'applyShiftToDate' is defined but never used.", "'suggestShiftsForDate' is defined but never used.", "'applyPattern' is defined but never used.", "'showPatternModal' is assigned a value but never used.", "'setShowPatternModal' is assigned a value but never used.", "no-undef", "'setAllPatterns' is not defined.", "undef", "'handlePatternDateClick' is assigned a value but never used.", "'selectionStartDate' is not defined.", "'setSelectionStartDate' is not defined.", "'setSelectionEndDate' is not defined.", "'selectionEndDate' is not defined.", "'selectedPatternId' is not defined.", "'setSelectedPatternId' is not defined.", "'allPatterns' is not defined.", "'handleApplyPattern' is not defined.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadExistingDay' and 'loadSuggestedShifts'. Either include them or remove the dependency array.", "ArrayExpression", ["155"], "'deleteRealDay' is not defined.", "'getRealDayByDate' is defined but never used.", "'getPlannedDayByDate' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadComparison'. Either include it or remove the dependency array.", ["156"], "React Hook useEffect has a missing dependency: 'loadAnalysis'. Either include it or remove the dependency array.", ["157"], {"desc": "158", "fix": "159"}, {"desc": "160", "fix": "161"}, {"desc": "162", "fix": "163"}, "Update the dependencies array to be: [show, selectedDate, loadExistingDay, loadSuggestedShifts]", {"range": "164", "text": "165"}, "Update the dependencies array to be: [selectedDate, realDays, plannedDays, loadComparison]", {"range": "166", "text": "167"}, "Update the dependencies array to be: [dateRange, loadAnalysis]", {"range": "168", "text": "169"}, [1140, 1160], "[show, selectedDate, loadExistingDay, loadSuggestedShifts]", [861, 898], "[selectedDate, realDays, plannedDays, loadComparison]", [1194, 1205], "[dateR<PERSON>e, loadAnalysis]"]