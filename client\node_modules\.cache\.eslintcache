[{"D:\\Proyectos Python\\Horario\\client\\src\\index.js": "1", "D:\\Proyectos Python\\Horario\\client\\src\\App.js": "2", "D:\\Proyectos Python\\Horario\\client\\src\\reportWebVitals.js": "3", "D:\\Proyectos Python\\Horario\\client\\src\\pages\\SettingsPage.js": "4", "D:\\Proyectos Python\\Horario\\client\\src\\pages\\CalendarPage.js": "5", "D:\\Proyectos Python\\Horario\\client\\src\\pages\\ReportsPage.js": "6", "D:\\Proyectos Python\\Horario\\client\\src\\components\\NavbarComponent.js": "7", "D:\\Proyectos Python\\Horario\\client\\src\\services\\api.js": "8", "D:\\Proyectos Python\\Horario\\client\\src\\components\\RealCalendarManager.js": "9", "D:\\Proyectos Python\\Horario\\client\\src\\components\\DualCalendarView.js": "10", "D:\\Proyectos Python\\Horario\\client\\src\\components\\CalendarAnalysis.js": "11", "D:\\Proyectos Python\\Horario\\client\\src\\components\\RealCalendarView.js": "12", "D:\\Proyectos Python\\Horario\\client\\src\\components\\RealDayModal.js": "13", "D:\\Proyectos Python\\Horario\\client\\src\\components\\PlannedCalendarView.js": "14"}, {"size": 512, "mtime": 1751325868914, "results": "15", "hashOfConfig": "16"}, {"size": 732, "mtime": 1751325868913, "results": "17", "hashOfConfig": "16"}, {"size": 362, "mtime": 1751325821295, "results": "18", "hashOfConfig": "16"}, {"size": 18252, "mtime": 1751360560488, "results": "19", "hashOfConfig": "16"}, {"size": 1797, "mtime": 1751466298925, "results": "20", "hashOfConfig": "16"}, {"size": 5910, "mtime": 1751367804647, "results": "21", "hashOfConfig": "16"}, {"size": 737, "mtime": 1751326777823, "results": "22", "hashOfConfig": "16"}, {"size": 9169, "mtime": 1751405483667, "results": "23", "hashOfConfig": "16"}, {"size": 11320, "mtime": 1751399793119, "results": "24", "hashOfConfig": "16"}, {"size": 9553, "mtime": 1751386263695, "results": "25", "hashOfConfig": "16"}, {"size": 10314, "mtime": 1751386458858, "results": "26", "hashOfConfig": "16"}, {"size": 12386, "mtime": 1751465904500, "results": "27", "hashOfConfig": "16"}, {"size": 10869, "mtime": 1751405296710, "results": "28", "hashOfConfig": "16"}, {"size": 10674, "mtime": 1751466179652, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "18a8vct", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Proyectos Python\\Horario\\client\\src\\index.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\App.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\reportWebVitals.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\pages\\SettingsPage.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\pages\\CalendarPage.js", ["72"], [], "D:\\Proyectos Python\\Horario\\client\\src\\pages\\ReportsPage.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\components\\NavbarComponent.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\services\\api.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\components\\RealCalendarManager.js", ["73", "74", "75"], [], "D:\\Proyectos Python\\Horario\\client\\src\\components\\DualCalendarView.js", ["76", "77", "78"], [], "D:\\Proyectos Python\\Horario\\client\\src\\components\\CalendarAnalysis.js", ["79"], [], "D:\\Proyectos Python\\Horario\\client\\src\\components\\RealCalendarView.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\components\\RealDayModal.js", ["80", "81"], [], "D:\\Proyectos Python\\Horario\\client\\src\\components\\PlannedCalendarView.js", [], [], {"ruleId": "82", "severity": 1, "message": "83", "line": 2, "column": 42, "nodeType": "84", "messageId": "85", "endLine": 2, "endColumn": 47}, {"ruleId": "82", "severity": 1, "message": "86", "line": 7, "column": 3, "nodeType": "84", "messageId": "85", "endLine": 7, "endColumn": 19}, {"ruleId": "87", "severity": 1, "message": "88", "line": 33, "column": 6, "nodeType": "89", "endLine": 33, "endColumn": 26, "suggestions": "90"}, {"ruleId": "91", "severity": 2, "message": "92", "line": 143, "column": 13, "nodeType": "84", "messageId": "93", "endLine": 143, "endColumn": 26}, {"ruleId": "82", "severity": 1, "message": "94", "line": 8, "column": 3, "nodeType": "84", "messageId": "85", "endLine": 8, "endColumn": 19}, {"ruleId": "82", "severity": 1, "message": "95", "line": 9, "column": 3, "nodeType": "84", "messageId": "85", "endLine": 9, "endColumn": 22}, {"ruleId": "87", "severity": 1, "message": "96", "line": 29, "column": 6, "nodeType": "89", "endLine": 29, "endColumn": 43, "suggestions": "97"}, {"ruleId": "87", "severity": 1, "message": "98", "line": 37, "column": 6, "nodeType": "89", "endLine": 37, "endColumn": 17, "suggestions": "99"}, {"ruleId": "82", "severity": 1, "message": "100", "line": 2, "column": 48, "nodeType": "84", "messageId": "85", "endLine": 2, "endColumn": 59}, {"ruleId": "87", "severity": 1, "message": "101", "line": 48, "column": 6, "nodeType": "89", "endLine": 48, "endColumn": 25, "suggestions": "102"}, "no-unused-vars", "'Alert' is defined but never used.", "Identifier", "unusedVar", "'applyShiftToDate' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadExistingDay' and 'loadSuggestedShifts'. Either include them or remove the dependency array.", "ArrayExpression", ["103"], "no-undef", "'deleteRealDay' is not defined.", "undef", "'getRealDayByDate' is defined but never used.", "'getPlannedDayByDate' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadComparison'. Either include it or remove the dependency array.", ["104"], "React Hook useEffect has a missing dependency: 'loadAnalysis'. Either include it or remove the dependency array.", ["105"], "'ButtonGroup' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadSuggestedShifts'. Either include it or remove the dependency array.", ["106"], {"desc": "107", "fix": "108"}, {"desc": "109", "fix": "110"}, {"desc": "111", "fix": "112"}, {"desc": "113", "fix": "114"}, "Update the dependencies array to be: [show, selectedDate, loadExistingDay, loadSuggestedShifts]", {"range": "115", "text": "116"}, "Update the dependencies array to be: [selectedDate, realDays, plannedDays, loadComparison]", {"range": "117", "text": "118"}, "Update the dependencies array to be: [dateRange, loadAnalysis]", {"range": "119", "text": "120"}, "Update the dependencies array to be: [show, existingDay, loadSuggestedShifts]", {"range": "121", "text": "122"}, [1140, 1160], "[show, selectedDate, loadExistingDay, loadSuggestedShifts]", [861, 898], "[selectedDate, realDays, plannedDays, loadComparison]", [1194, 1205], "[dateR<PERSON>e, loadAnalysis]", [1437, 1456], "[show, existingDay, loadSuggestedShifts]"]