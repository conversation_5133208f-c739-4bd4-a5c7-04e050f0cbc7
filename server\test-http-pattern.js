const http = require('http');

const testPattern = {
    name: "<PERSON><PERSON><PERSON> Prueba",
    basePattern: [
        { type: "rest", hours: 0, description: "Domingo - Descanso" },
        { type: "worked", hours: 8, description: "Lunes - Trabajo", shiftId: "morning" },
        { type: "worked", hours: 8, description: "Martes - Trabajo", shiftId: "morning" },
        { type: "worked", hours: 8, description: "Miércoles - Trabajo", shiftId: "morning" },
        { type: "worked", hours: 8, description: "Jueves - Trabajo", shiftId: "morning" },
        { type: "worked", hours: 8, description: "Viernes - Trabajo", shiftId: "morning" },
        { type: "rest", hours: 0, description: "Sábado - Descanso" }
    ],
    overrides: []
};

const postData = JSON.stringify(testPattern);

const options = {
    hostname: 'localhost',
    port: 5000,
    path: '/api/patterns',
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
    }
};

console.log('Enviando petición POST para crear patrón...');

const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    console.log(`Headers: ${JSON.stringify(res.headers)}`);
    
    let data = '';
    res.on('data', (chunk) => {
        data += chunk;
    });
    
    res.on('end', () => {
        console.log('Respuesta recibida:', data);
        
        // Ahora hacer GET para verificar
        console.log('Haciendo GET para verificar...');
        const getReq = http.request({
            hostname: 'localhost',
            port: 5000,
            path: '/api/patterns',
            method: 'GET'
        }, (getRes) => {
            let getData = '';
            getRes.on('data', (chunk) => {
                getData += chunk;
            });
            getRes.on('end', () => {
                console.log('Patrones disponibles:', getData);
            });
        });
        
        getReq.on('error', (err) => {
            console.error('Error en GET:', err);
        });
        
        getReq.end();
    });
});

req.on('error', (err) => {
    console.error('Error en POST:', err);
});

req.write(postData);
req.end();
