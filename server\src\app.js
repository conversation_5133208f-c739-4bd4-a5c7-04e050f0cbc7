const express = require('express');
const cors = require('cors');
const dayRoutes = require('./routes/dayRoutes');
const shiftRoutes = require('./routes/shiftRoutes');
const patternRoutes = require('./routes/patternRoutes');
const analysisRoutes = require('./routes/analysisRoutes');

const app = express();

app.use(cors());
app.use(express.json());

// Middleware de logging
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    console.log('Headers:', req.headers);
    if (req.body && Object.keys(req.body).length > 0) {
        console.log('Body:', req.body);
    }
    next();
});

app.use('/api/days', dayRoutes);
app.use('/api/shifts', shiftRoutes);
app.use('/api/patterns', patternRoutes);
app.use('/api/analysis', analysisRoutes);

module.exports = app;
