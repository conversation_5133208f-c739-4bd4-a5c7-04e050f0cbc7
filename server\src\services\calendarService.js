const { readData, writeData } = require('../data/dataHandler');
const shiftService = require('./shiftService'); // Import shiftService
const patternService = require('./patternService'); // Import patternService

const getAllDays = () => {
    const data = readData();
    return data.days;
};

const getDayByDate = (date) => {
    const data = readData();
    return data.days.find(day => day.date === date);
};

const createOrUpdateDay = (date, type, hours = 0, description = '', shiftId = null) => {
    const data = readData();
    let day = data.days.find(d => d.date === date);

    if (day) {
        day.type = type;
        day.hours = hours;
        day.description = description;
        day.shiftId = shiftId; // Store shiftId
    } else {
        day = { date, type, hours, description, shiftId }; // Store shiftId
        data.days.push(day);
    }
    writeData(data);
    return day;
};

const applyPatternToDates = (startDate, endDate, patternId) => {
    const data = readData();
    const pattern = patternService.getPatternById(patternId);

    if (!pattern) {
        throw new Error('Pattern not found.');
    }

    const start = new Date(startDate);
    const end = new Date(endDate);
    let currentDate = new Date(start);

    while (currentDate <= end) {
        const formattedDate = currentDate.toISOString().split('T')[0];
        let dayDefinition = null;

        // Check for overrides first
        for (const override of pattern.overrides) {
            const overrideStartDate = new Date(override.startDate);
            const overrideEndDate = new Date(override.endDate);
            if (currentDate >= overrideStartDate && currentDate <= overrideEndDate) {
                const dayIndex = (currentDate.getTime() - overrideStartDate.getTime()) / (1000 * 60 * 60 * 24);
                dayDefinition = override.overridePattern[dayIndex % override.overridePattern.length];
                break;
            }
        }

        // If no override, use base pattern
        if (!dayDefinition) {
            const dayOfWeek = currentDate.getDay(); // 0 for Sunday, 1 for Monday, ..., 6 for Saturday
            dayDefinition = pattern.basePattern[dayOfWeek];
        }

        if (dayDefinition) {
            let type = dayDefinition.type;
            let hours = dayDefinition.hours || 0;
            let description = dayDefinition.description || '';
            let shiftId = dayDefinition.shiftId || null;

            if (shiftId) {
                const shift = shiftService.getShiftById(shiftId);
                if (shift) {
                    type = 'worked'; // Assuming a shift means a worked day
                    hours = shift.totalHours;
                    description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;
                }
            }
            createOrUpdateDay(formattedDate, type, hours, description, shiftId);
        }

        currentDate.setDate(currentDate.getDate() + 1);
    }
    return { message: 'Patrón aplicado exitosamente.' };
};

module.exports = {
    getAllDays,
    getDayByDate,
    createOrUpdateDay,
    applyPatternToDates
};
