import axios from 'axios';

const API_URL = 'http://localhost:5000/api/days';
const SHIFTS_API_URL = 'http://localhost:5000/api/shifts';
const PATTERNS_API_URL = 'http://localhost:5000/api/patterns';

export const getAllDays = async () => {
  try {
    const response = await axios.get(API_URL);
    return response.data;
  } catch (error) {
    console.error('Error fetching all days:', error);
    throw error;
  }
};

export const getDayByDate = async (date) => {
  try {
    const response = await axios.get(`${API_URL}/${date}`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.status === 404) {
      return null; // Day not found, return null instead of throwing error
    }
    console.error(`Error fetching day for ${date}:`, error);
    throw error;
  }
};

export const createOrUpdateDay = async (date, type, hours, description) => {
  try {
    const response = await axios.post(API_URL, { date, type, hours, description });
    return response.data;
  } catch (error) {
    console.error('Error creating or updating day:', error);
    throw error;
  }
};

// ===== FUNCIONES PARA TURNOS PREDEFINIDOS =====

/**
 * Obtiene todos los turnos predefinidos
 */
export const getAllShifts = async () => {
  try {
    const response = await axios.get(SHIFTS_API_URL);
    return response.data;
  } catch (error) {
    console.error('Error fetching all shifts:', error);
    throw error;
  }
};

/**
 * Obtiene un turno específico por ID
 */
export const getShiftById = async (shiftId) => {
  try {
    const response = await axios.get(`${SHIFTS_API_URL}/${shiftId}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching shift ${shiftId}:`, error);
    throw error;
  }
};

/**
 * Aplica un turno predefinido a una fecha específica
 */
export const applyShiftToDate = async (date, shiftId, description = '') => {
  try {
    const response = await axios.post(`${SHIFTS_API_URL}/apply`, {
      date,
      shiftId,
      description
    });
    return response.data;
  } catch (error) {
    console.error('Error applying shift to date:', error);
    throw error;
  }
};

/**
 * Aplica un turno a múltiples fechas
 */
export const applyShiftToMultipleDates = async (dates, shiftId, description = '') => {
  try {
    const response = await axios.post(`${SHIFTS_API_URL}/apply-multiple`, {
      dates,
      shiftId,
      description
    });
    return response.data;
  } catch (error) {
    console.error('Error applying shift to multiple dates:', error);
    throw error;
  }
};

/**
 * Obtiene estadísticas de turnos aplicados
 */
export const getShiftStatistics = async () => {
  try {
    const response = await axios.get(`${SHIFTS_API_URL}/statistics`);
    return response.data;
  } catch (error) {
    console.error('Error fetching shift statistics:', error);
    throw error;
  }
};

/**
 * Sugiere turnos apropiados para una fecha
 */
export const suggestShiftsForDate = async (date) => {
  try {
    const response = await axios.get(`${SHIFTS_API_URL}/suggest/${date}`);
    return response.data;
  } catch (error) {
    console.error(`Error suggesting shifts for date ${date}:`, error);
    throw error;
  }
};

/**
 * Aplica un patrón de días a un rango de fechas usando un ID de patrón.
 */
export const applyPattern = async (startDate, endDate, patternId) => {
  try {
    const response = await axios.post(`${API_URL}/apply-pattern`, { startDate, endDate, patternId });
    return response.data;
  } catch (error) {
    console.error('Error applying pattern:', error);
    throw error;
  }
};

/**
 * Aplica un patrón de días a un rango de fechas usando un array de patrón (legacy).
 */
export const applyPatternArray = async (startDate, endDate, pattern) => {
  try {
    const response = await axios.post(`${API_URL}/apply-pattern`, { startDate, endDate, pattern });
    return response.data;
  } catch (error) {
    console.error('Error applying pattern array:', error);
    throw error;
  }
};

// ===== FUNCIONES PARA PATRONES =====

export const getAllPatterns = async () => {
  try {
    const response = await axios.get(PATTERNS_API_URL);
    return response.data;
  } catch (error) {
    console.error('Error fetching all patterns:', error);
    throw error;
  }
};

export const getPatternById = async (id) => {
  try {
    const response = await axios.get(`${PATTERNS_API_URL}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching pattern ${id}:`, error);
    throw error;
  }
};

export const createPattern = async (name, basePattern, overrides = []) => {
  try {
    const response = await axios.post(PATTERNS_API_URL, { name, basePattern, overrides });
    return response.data;
  } catch (error) {
    console.error('Error creating pattern:', error);
    throw error;
  }
};

export const updatePattern = async (id, name, basePattern, overrides = []) => {
  try {
    const response = await axios.put(`${PATTERNS_API_URL}/${id}`, { name, basePattern, overrides });
    return response.data;
  } catch (error) {
    console.error(`Error updating pattern ${id}:`, error);
    throw error;
  }
};

export const copyPattern = async (id, newName, newYear) => {
  try {
    const response = await axios.post(`${PATTERNS_API_URL}/${id}/copy`, { newName, newYear });
    return response.data;
  } catch (error) {
    console.error(`Error copying pattern ${id}:`, error);
    throw error;
  }
};

export const deletePattern = async (id) => {
  try {
    await axios.delete(`${PATTERNS_API_URL}/${id}`);
    return { message: 'Pattern deleted successfully' };
  } catch (error) {
    console.error(`Error deleting pattern ${id}:`, error);
    throw error;
  }
};
