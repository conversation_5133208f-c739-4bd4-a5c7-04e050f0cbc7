{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\CalendarPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Modal, Form, Alert, Badge, Tabs, Tab } from 'react-bootstrap';\nimport DualCalendarView from '../components/DualCalendarView';\nimport RealCalendarManager from '../components/RealCalendarManager';\nimport CalendarAnalysis from '../components/CalendarAnalysis';\nimport { getDayByDate, createOrUpdateDay, getAllShifts, applyShiftToDate, suggestShiftsForDate, applyPattern, getAllPatterns, clearPlannedCalendar } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction CalendarPage() {\n  _s();\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('calendar');\n\n  // Estados para modales\n  const [showRealDayModal, setShowRealDayModal] = useState(false);\n  const [showPatternModal, setShowPatternModal] = useState(false);\n\n  // Estados para aplicación de patrones\n  const [selectionStartDate, setSelectionStartDate] = useState(null);\n  const [selectionEndDate, setSelectionEndDate] = useState(null);\n  const [allPatterns, setAllPatterns] = useState([]);\n  const [selectedPatternId, setSelectedPatternId] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estado para forzar actualización del calendario\n  const [calendarKey, setCalendarKey] = useState(0);\n  useEffect(() => {\n    loadAllPatterns();\n  }, []);\n  const loadAllPatterns = async () => {\n    try {\n      const patterns = await getAllPatterns();\n      setAllPatterns(patterns);\n    } catch (error) {\n      console.error('Error loading patterns:', error);\n      setError('Error cargando patrones.');\n    }\n  };\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (error) {\n      console.error('Error loading shifts:', error);\n      setError('Error cargando turnos predefinidos');\n    }\n  };\n  const loadSuggestedShifts = async date => {\n    try {\n      const formattedDate = date.toISOString().split('T')[0];\n      const suggestions = await suggestShiftsForDate(formattedDate);\n      setSuggestedShifts(suggestions.suggestions || []);\n    } catch (error) {\n      console.error('Error loading suggested shifts:', error);\n      setSuggestedShifts([]);\n    }\n  };\n  const fetchDayDetails = async date => {\n    try {\n      const formattedDate = date.toISOString().split('T')[0];\n      const response = await getDayByDate(formattedDate);\n      if (response) {\n        setDayType(response.type || '');\n        setHours(response.hours || 0);\n        setDescription(response.description || '');\n        // Si el día tiene un turno asignado, mostrar esa información\n        if (response.shift) {\n          setSelectedShift(response.shift.id);\n          setUseShift(true);\n        } else {\n          setSelectedShift('');\n          setUseShift(false);\n        }\n      } else {\n        resetForm();\n      }\n    } catch (error) {\n      console.error('Error fetching day details:', error);\n      resetForm();\n    }\n  };\n  const resetForm = () => {\n    setDayType('');\n    setHours(0);\n    setDescription('');\n    setSelectedShift('');\n    setUseShift(false);\n    setError('');\n  };\n  const handleDateClick = value => {\n    if (!selectionStartDate) {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    } else if (!selectionEndDate) {\n      if (value < selectionStartDate) {\n        setSelectionEndDate(selectionStartDate);\n        setSelectionStartDate(value);\n      } else {\n        setSelectionEndDate(value);\n      }\n    } else {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    }\n    setSelectedDay(value);\n    setShowModal(true);\n    setError('');\n  };\n  const handleShiftChange = shiftId => {\n    setSelectedShift(shiftId);\n    if (shiftId && availableShifts.length > 0) {\n      const shift = availableShifts.find(s => s.id === shiftId);\n      if (shift) {\n        setDayType('worked');\n        setHours(shift.totalHours);\n        setDescription(`${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`);\n      }\n    }\n  };\n  const handleSave = async () => {\n    if (!selectedDay) return;\n    const formattedDate = selectedDay.toISOString().split('T')[0];\n    setLoading(true);\n    setError('');\n    try {\n      if (useShift && selectedShift) {\n        // Usar turno predefinido\n        await applyShiftToDate(formattedDate, selectedShift, description);\n      } else {\n        // Usar entrada manual\n        await createOrUpdateDay(formattedDate, dayType, hours, description);\n      }\n      setShowModal(false);\n      resetForm();\n    } catch (error) {\n      console.error('Error saving day:', error);\n      setError('Error guardando los datos. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleApplyPattern = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      const formattedStartDate = selectionStartDate.toISOString().split('T')[0];\n      const formattedEndDate = selectionEndDate.toISOString().split('T')[0];\n      await applyPattern(formattedStartDate, formattedEndDate, selectedPatternId);\n      setSelectionStartDate(null);\n      setSelectionEndDate(null);\n      setSelectedPatternId('');\n      // Optionally, refresh calendar data here if needed\n      alert('Patrón aplicado exitosamente!');\n    } catch (error) {\n      console.error('Error applying pattern:', error);\n      setError('Error aplicando el patrón. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"justify-content-md-center\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: \"auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Calendario de D\\xEDas Trabajados\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Calendar, {\n          onChange: setDate,\n          value: date,\n          onClickDay: handleDateClick,\n          tileClassName: ({\n            date,\n            view\n          }) => {\n            if (view === 'month') {\n              if (selectionStartDate && selectionEndDate) {\n                if (date >= selectionStartDate && date <= selectionEndDate) {\n                  return 'selected-range';\n                }\n              } else if (selectionStartDate && date.toDateString() === selectionStartDate.toDateString()) {\n                return 'selected-range-start';\n              }\n            }\n            return null;\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"justify-content-md-center mt-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: \"auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Aplicar Patr\\xF3n a Rango Seleccionado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"mb-3\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Fechas Seleccionadas:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              value: selectionStartDate && selectionEndDate ? `${selectionStartDate.toDateString()} - ${selectionEndDate.toDateString()}` : selectionStartDate ? selectionStartDate.toDateString() : 'Ninguna',\n              readOnly: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Seleccionar Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: selectedPatternId,\n              onChange: e => setSelectedPatternId(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Seleccionar patr\\xF3n...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), allPatterns.map(pattern => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: pattern.id,\n                children: pattern.name\n              }, pattern.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleApplyPattern,\n            disabled: loading || !selectedPatternId || !selectionStartDate || !selectionEndDate,\n            children: loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => {\n              setSelectionStartDate(null);\n              setSelectionEndDate(null);\n              setSelectedPatternId('');\n              setError('');\n            },\n            className: \"ms-2\",\n            children: \"Limpiar Selecci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [\"Detalles del D\\xEDa: \", selectedDay && selectedDay.toDateString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"mb-3\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n              type: \"radio\",\n              id: \"manual-entry\",\n              name: \"entry-method\",\n              label: \"Entrada manual\",\n              checked: !useShift,\n              onChange: () => setUseShift(false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n              type: \"radio\",\n              id: \"shift-entry\",\n              name: \"entry-method\",\n              label: \"Usar turno predefinido\",\n              checked: useShift,\n              onChange: () => setUseShift(true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), useShift && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [suggestedShifts.length > 0 && /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Turnos sugeridos para esta fecha:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex flex-wrap gap-2 mb-2\",\n                children: suggestedShifts.map(shift => /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: selectedShift === shift.id ? \"primary\" : \"secondary\",\n                  style: {\n                    cursor: 'pointer'\n                  },\n                  onClick: () => handleShiftChange(shift.id),\n                  children: [shift.name, \" (\", shift.startTime, \" - \", shift.endTime, \")\"]\n                }, shift.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Seleccionar Turno\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: selectedShift,\n                onChange: e => handleShiftChange(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Seleccionar turno...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this), availableShifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: shift.id,\n                  children: [shift.name, \" - \", shift.startTime, \" a \", shift.endTime, \" (\", shift.totalHours, \"h)\"]\n                }, shift.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), !useShift && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Tipo de D\\xEDa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: dayType,\n                onChange: e => setDayType(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Seleccionar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"worked\",\n                  children: \"Trabajado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"holiday\",\n                  children: \"Vacaciones\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"permit\",\n                  children: \"Permiso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"negative\",\n                  children: \"C\\xF3mputo Negativo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Horas (C\\xF3mputo Positivo/Negativo)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"number\",\n                value: hours,\n                onChange: e => setHours(parseFloat(e.target.value)),\n                placeholder: \"Introduce horas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Descripci\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              value: description,\n              onChange: e => setDescription(e.target.value),\n              placeholder: \"A\\xF1ade una descripci\\xF3n (opcional)\",\n              disabled: useShift && selectedShift && !description.includes('Personalizado:')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), useShift && selectedShift && /*#__PURE__*/_jsxDEV(Form.Text, {\n              className: \"text-muted\",\n              children: \"La descripci\\xF3n se genera autom\\xE1ticamente. Puedes editarla si es necesario.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => {\n            setShowModal(false);\n            resetForm();\n          },\n          disabled: loading,\n          children: \"Cerrar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSave,\n          disabled: loading || !useShift && !dayType || useShift && !selectedShift,\n          children: loading ? 'Guardando...' : 'Guardar Cambios'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 183,\n    columnNumber: 5\n  }, this);\n}\n_s(CalendarPage, \"uITU9TjMMwqnQXFPHuKhrf0iCQM=\");\n_c = CalendarPage;\nexport default CalendarPage;\nvar _c;\n$RefreshReg$(_c, \"CalendarPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "<PERSON><PERSON>", "Modal", "Form", "<PERSON><PERSON>", "Badge", "Tabs", "Tab", "DualCalendarView", "RealCalendarManager", "CalendarAnalysis", "getDayByDate", "createOrUpdateDay", "getAllShifts", "applyShiftToDate", "suggestShiftsForDate", "applyPattern", "getAllPatterns", "clearPlannedCalendar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CalendarPage", "_s", "selectedDate", "setSelectedDate", "Date", "activeTab", "setActiveTab", "showRealDayModal", "setShowRealDayModal", "showPatternModal", "setShowPatternModal", "selectionStartDate", "setSelectionStartDate", "selectionEndDate", "setSelectionEndDate", "allPatterns", "setAllPatterns", "selectedPatternId", "setSelectedPatternId", "loading", "setLoading", "error", "setError", "calendarKey", "setCalendarKey", "loadAllPatterns", "patterns", "console", "loadAvailableShifts", "shifts", "setAvailableShifts", "loadSuggestedShifts", "date", "formattedDate", "toISOString", "split", "suggestions", "setSuggestedShifts", "fetchDayDetails", "response", "setDayType", "type", "setHours", "hours", "setDescription", "description", "shift", "setSelectedShift", "id", "setUseShift", "resetForm", "handleDateClick", "value", "setSelectedDay", "setShowModal", "handleShiftChange", "shiftId", "availableShifts", "length", "find", "s", "totalHours", "name", "startTime", "endTime", "breakMinutes", "handleSave", "selected<PERSON>ay", "useShift", "selectedShift", "dayType", "handleApplyPattern", "formattedStartDate", "formattedEndDate", "alert", "children", "className", "md", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Calendar", "onChange", "setDate", "onClickDay", "tileClassName", "view", "toDateString", "variant", "Group", "Label", "Control", "readOnly", "Select", "e", "target", "map", "pattern", "onClick", "disabled", "show", "showModal", "onHide", "Header", "closeButton", "Title", "Body", "Check", "label", "checked", "suggestedShifts", "bg", "style", "cursor", "parseFloat", "placeholder", "as", "rows", "includes", "Text", "Footer", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/CalendarPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON>er, Row, Col, Button, Modal, Form, Alert, Badge, Tabs, Tab } from 'react-bootstrap';\nimport DualCalendarView from '../components/DualCalendarView';\nimport RealCalendarManager from '../components/RealCalendarManager';\nimport CalendarAnalysis from '../components/CalendarAnalysis';\nimport {\n  getDayByDate,\n  createOrUpdateDay,\n  getAllShifts,\n  applyShiftToDate,\n  suggestShiftsForDate,\n  applyPattern,\n  getAllPatterns,\n  clearPlannedCalendar\n} from '../services/api';\n\nfunction CalendarPage() {\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('calendar');\n\n  // Estados para modales\n  const [showRealDayModal, setShowRealDayModal] = useState(false);\n  const [showPatternModal, setShowPatternModal] = useState(false);\n\n  // Estados para aplicación de patrones\n  const [selectionStartDate, setSelectionStartDate] = useState(null);\n  const [selectionEndDate, setSelectionEndDate] = useState(null);\n  const [allPatterns, setAllPatterns] = useState([]);\n  const [selectedPatternId, setSelectedPatternId] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estado para forzar actualización del calendario\n  const [calendarKey, setCalendarKey] = useState(0);\n\n  useEffect(() => {\n    loadAllPatterns();\n  }, []);\n\n  const loadAllPatterns = async () => {\n    try {\n      const patterns = await getAllPatterns();\n      setAllPatterns(patterns);\n    } catch (error) {\n      console.error('Error loading patterns:', error);\n      setError('Error cargando patrones.');\n    }\n  };\n\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (error) {\n      console.error('Error loading shifts:', error);\n      setError('Error cargando turnos predefinidos');\n    }\n  };\n\n  const loadSuggestedShifts = async (date) => {\n    try {\n      const formattedDate = date.toISOString().split('T')[0];\n      const suggestions = await suggestShiftsForDate(formattedDate);\n      setSuggestedShifts(suggestions.suggestions || []);\n    } catch (error) {\n      console.error('Error loading suggested shifts:', error);\n      setSuggestedShifts([]);\n    }\n  };\n\n  const fetchDayDetails = async (date) => {\n    try {\n      const formattedDate = date.toISOString().split('T')[0];\n      const response = await getDayByDate(formattedDate);\n      if (response) {\n        setDayType(response.type || '');\n        setHours(response.hours || 0);\n        setDescription(response.description || '');\n        // Si el día tiene un turno asignado, mostrar esa información\n        if (response.shift) {\n          setSelectedShift(response.shift.id);\n          setUseShift(true);\n        } else {\n          setSelectedShift('');\n          setUseShift(false);\n        }\n      } else {\n        resetForm();\n      }\n    } catch (error) {\n      console.error('Error fetching day details:', error);\n      resetForm();\n    }\n  };\n\n  const resetForm = () => {\n    setDayType('');\n    setHours(0);\n    setDescription('');\n    setSelectedShift('');\n    setUseShift(false);\n    setError('');\n  };\n\n  const handleDateClick = (value) => {\n    if (!selectionStartDate) {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    } else if (!selectionEndDate) {\n      if (value < selectionStartDate) {\n        setSelectionEndDate(selectionStartDate);\n        setSelectionStartDate(value);\n      } else {\n        setSelectionEndDate(value);\n      }\n    } else {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    }\n    setSelectedDay(value);\n    setShowModal(true);\n    setError('');\n  };\n\n  const handleShiftChange = (shiftId) => {\n    setSelectedShift(shiftId);\n    if (shiftId && availableShifts.length > 0) {\n      const shift = availableShifts.find(s => s.id === shiftId);\n      if (shift) {\n        setDayType('worked');\n        setHours(shift.totalHours);\n        setDescription(`${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`);\n      }\n    }\n  };\n\n  const handleSave = async () => {\n    if (!selectedDay) return;\n\n    const formattedDate = selectedDay.toISOString().split('T')[0];\n    setLoading(true);\n    setError('');\n\n    try {\n      if (useShift && selectedShift) {\n        // Usar turno predefinido\n        await applyShiftToDate(formattedDate, selectedShift, description);\n      } else {\n        // Usar entrada manual\n        await createOrUpdateDay(formattedDate, dayType, hours, description);\n      }\n      setShowModal(false);\n      resetForm();\n    } catch (error) {\n      console.error('Error saving day:', error);\n      setError('Error guardando los datos. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleApplyPattern = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      const formattedStartDate = selectionStartDate.toISOString().split('T')[0];\n      const formattedEndDate = selectionEndDate.toISOString().split('T')[0];\n      await applyPattern(formattedStartDate, formattedEndDate, selectedPatternId);\n      setSelectionStartDate(null);\n      setSelectionEndDate(null);\n      setSelectedPatternId('');\n      // Optionally, refresh calendar data here if needed\n      alert('Patrón aplicado exitosamente!');\n    } catch (error) {\n      console.error('Error applying pattern:', error);\n      setError('Error aplicando el patrón. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Container>\n      <Row className=\"justify-content-md-center\">\n        <Col md=\"auto\">\n          <h2>Calendario de Días Trabajados</h2>\n          <Calendar\n            onChange={setDate}\n            value={date}\n            onClickDay={handleDateClick}\n            tileClassName={({ date, view }) => {\n              if (view === 'month') {\n                if (selectionStartDate && selectionEndDate) {\n                  if (date >= selectionStartDate && date <= selectionEndDate) {\n                    return 'selected-range';\n                  }\n                } else if (selectionStartDate && date.toDateString() === selectionStartDate.toDateString()) {\n                  return 'selected-range-start';\n                }\n              }\n              return null;\n            }}\n          />\n\n        </Col>\n      </Row>\n\n      <Row className=\"justify-content-md-center mt-3\">\n        <Col md=\"auto\">\n          <h3>Aplicar Patrón a Rango Seleccionado</h3>\n          {error && (\n            <Alert variant=\"danger\" className=\"mb-3\">\n              {error}\n            </Alert>\n          )}\n          <Form>\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Fechas Seleccionadas:</Form.Label>\n              <Form.Control\n                type=\"text\"\n                value={\n                  selectionStartDate && selectionEndDate\n                    ? `${selectionStartDate.toDateString()} - ${selectionEndDate.toDateString()}`\n                    : selectionStartDate\n                      ? selectionStartDate.toDateString()\n                      : 'Ninguna'\n                }\n                readOnly\n              />\n            </Form.Group>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Seleccionar Patrón</Form.Label>\n              <Form.Select\n                value={selectedPatternId}\n                onChange={(e) => setSelectedPatternId(e.target.value)}\n              >\n                <option value=\"\">Seleccionar patrón...</option>\n                {allPatterns.map((pattern) => (\n                  <option key={pattern.id} value={pattern.id}>\n                    {pattern.name}\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n\n            <Button\n              variant=\"primary\"\n              onClick={handleApplyPattern}\n              disabled={loading || !selectedPatternId || !selectionStartDate || !selectionEndDate}\n            >\n              {loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'}\n            </Button>\n            <Button\n              variant=\"secondary\"\n              onClick={() => {\n                setSelectionStartDate(null);\n                setSelectionEndDate(null);\n                setSelectedPatternId('');\n                setError('');\n              }}\n              className=\"ms-2\"\n            >\n              Limpiar Selección\n            </Button>\n          </Form>\n        </Col>\n      </Row>\n\n      <Modal show={showModal} onHide={() => setShowModal(false)}>\n        <Modal.Header closeButton>\n          <Modal.Title>Detalles del Día: {selectedDay && selectedDay.toDateString()}</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {error && (\n            <Alert variant=\"danger\" className=\"mb-3\">\n              {error}\n            </Alert>\n          )}\n\n          <Form>\n            {/* Selector de método de entrada */}\n            <Form.Group className=\"mb-3\">\n              <Form.Check\n                type=\"radio\"\n                id=\"manual-entry\"\n                name=\"entry-method\"\n                label=\"Entrada manual\"\n                checked={!useShift}\n                onChange={() => setUseShift(false)}\n              />\n              <Form.Check\n                type=\"radio\"\n                id=\"shift-entry\"\n                name=\"entry-method\"\n                label=\"Usar turno predefinido\"\n                checked={useShift}\n                onChange={() => setUseShift(true)}\n              />\n            </Form.Group>\n\n            {/* Sección de turnos predefinidos */}\n            {useShift && (\n              <>\n                {suggestedShifts.length > 0 && (\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Turnos sugeridos para esta fecha:</Form.Label>\n                    <div className=\"d-flex flex-wrap gap-2 mb-2\">\n                      {suggestedShifts.map((shift) => (\n                        <Badge\n                          key={shift.id}\n                          bg={selectedShift === shift.id ? \"primary\" : \"secondary\"}\n                          style={{ cursor: 'pointer' }}\n                          onClick={() => handleShiftChange(shift.id)}\n                        >\n                          {shift.name} ({shift.startTime} - {shift.endTime})\n                        </Badge>\n                      ))}\n                    </div>\n                  </Form.Group>\n                )}\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Seleccionar Turno</Form.Label>\n                  <Form.Select\n                    value={selectedShift}\n                    onChange={(e) => handleShiftChange(e.target.value)}\n                  >\n                    <option value=\"\">Seleccionar turno...</option>\n                    {availableShifts.map((shift) => (\n                      <option key={shift.id} value={shift.id}>\n                        {shift.name} - {shift.startTime} a {shift.endTime} ({shift.totalHours}h)\n                      </option>\n                    ))}\n                  </Form.Select>\n                </Form.Group>\n              </>\n            )}\n\n            {/* Sección de entrada manual */}\n            {!useShift && (\n              <>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Tipo de Día</Form.Label>\n                  <Form.Select value={dayType} onChange={(e) => setDayType(e.target.value)}>\n                    <option value=\"\">Seleccionar</option>\n                    <option value=\"worked\">Trabajado</option>\n                    <option value=\"holiday\">Vacaciones</option>\n                    <option value=\"permit\">Permiso</option>\n                    <option value=\"negative\">Cómputo Negativo</option>\n                  </Form.Select>\n                </Form.Group>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Horas (Cómputo Positivo/Negativo)</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    value={hours}\n                    onChange={(e) => setHours(parseFloat(e.target.value))}\n                    placeholder=\"Introduce horas\"\n                  />\n                </Form.Group>\n              </>\n            )}\n\n            {/* Descripción (común para ambos métodos) */}\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Descripción</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={3}\n                value={description}\n                onChange={(e) => setDescription(e.target.value)}\n                placeholder=\"Añade una descripción (opcional)\"\n                disabled={useShift && selectedShift && !description.includes('Personalizado:')}\n              />\n              {useShift && selectedShift && (\n                <Form.Text className=\"text-muted\">\n                  La descripción se genera automáticamente. Puedes editarla si es necesario.\n                </Form.Text>\n              )}\n            </Form.Group>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button\n            variant=\"secondary\"\n            onClick={() => {\n              setShowModal(false);\n              resetForm();\n            }}\n            disabled={loading}\n          >\n            Cerrar\n          </Button>\n          <Button\n            variant=\"primary\"\n            onClick={handleSave}\n            disabled={loading || (!useShift && !dayType) || (useShift && !selectedShift)}\n          >\n            {loading ? 'Guardando...' : 'Guardar Cambios'}\n          </Button>\n        </Modal.Footer>\n      </Modal>\n\n\n    </Container>\n  );\n}\n\nexport default CalendarPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,QAAQ,iBAAiB;AACnG,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,SACEC,YAAY,EACZC,iBAAiB,EACjBC,YAAY,EACZC,gBAAgB,EAChBC,oBAAoB,EACpBC,YAAY,EACZC,cAAc,EACdC,oBAAoB,QACf,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,IAAI+B,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,UAAU,CAAC;;EAEtD;EACA,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACsC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgD,KAAK,EAAEC,QAAQ,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACdmD,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMhC,cAAc,CAAC,CAAC;MACvCsB,cAAc,CAACU,QAAQ,CAAC;IAC1B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,0BAA0B,CAAC;IACtC;EACF,CAAC;EAED,MAAMM,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMvC,YAAY,CAAC,CAAC;MACnCwC,kBAAkB,CAACD,MAAM,CAAC;IAC5B,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,oCAAoC,CAAC;IAChD;EACF,CAAC;EAED,MAAMS,mBAAmB,GAAG,MAAOC,IAAI,IAAK;IAC1C,IAAI;MACF,MAAMC,aAAa,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACtD,MAAMC,WAAW,GAAG,MAAM5C,oBAAoB,CAACyC,aAAa,CAAC;MAC7DI,kBAAkB,CAACD,WAAW,CAACA,WAAW,IAAI,EAAE,CAAC;IACnD,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDgB,kBAAkB,CAAC,EAAE,CAAC;IACxB;EACF,CAAC;EAED,MAAMC,eAAe,GAAG,MAAON,IAAI,IAAK;IACtC,IAAI;MACF,MAAMC,aAAa,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACtD,MAAMI,QAAQ,GAAG,MAAMnD,YAAY,CAAC6C,aAAa,CAAC;MAClD,IAAIM,QAAQ,EAAE;QACZC,UAAU,CAACD,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;QAC/BC,QAAQ,CAACH,QAAQ,CAACI,KAAK,IAAI,CAAC,CAAC;QAC7BC,cAAc,CAACL,QAAQ,CAACM,WAAW,IAAI,EAAE,CAAC;QAC1C;QACA,IAAIN,QAAQ,CAACO,KAAK,EAAE;UAClBC,gBAAgB,CAACR,QAAQ,CAACO,KAAK,CAACE,EAAE,CAAC;UACnCC,WAAW,CAAC,IAAI,CAAC;QACnB,CAAC,MAAM;UACLF,gBAAgB,CAAC,EAAE,CAAC;UACpBE,WAAW,CAAC,KAAK,CAAC;QACpB;MACF,CAAC,MAAM;QACLC,SAAS,CAAC,CAAC;MACb;IACF,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD6B,SAAS,CAAC,CAAC;IACb;EACF,CAAC;EAED,MAAMA,SAAS,GAAGA,CAAA,KAAM;IACtBV,UAAU,CAAC,EAAE,CAAC;IACdE,QAAQ,CAAC,CAAC,CAAC;IACXE,cAAc,CAAC,EAAE,CAAC;IAClBG,gBAAgB,CAAC,EAAE,CAAC;IACpBE,WAAW,CAAC,KAAK,CAAC;IAClB3B,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAM6B,eAAe,GAAIC,KAAK,IAAK;IACjC,IAAI,CAACzC,kBAAkB,EAAE;MACvBC,qBAAqB,CAACwC,KAAK,CAAC;MAC5BtC,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM,IAAI,CAACD,gBAAgB,EAAE;MAC5B,IAAIuC,KAAK,GAAGzC,kBAAkB,EAAE;QAC9BG,mBAAmB,CAACH,kBAAkB,CAAC;QACvCC,qBAAqB,CAACwC,KAAK,CAAC;MAC9B,CAAC,MAAM;QACLtC,mBAAmB,CAACsC,KAAK,CAAC;MAC5B;IACF,CAAC,MAAM;MACLxC,qBAAqB,CAACwC,KAAK,CAAC;MAC5BtC,mBAAmB,CAAC,IAAI,CAAC;IAC3B;IACAuC,cAAc,CAACD,KAAK,CAAC;IACrBE,YAAY,CAAC,IAAI,CAAC;IAClBhC,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMiC,iBAAiB,GAAIC,OAAO,IAAK;IACrCT,gBAAgB,CAACS,OAAO,CAAC;IACzB,IAAIA,OAAO,IAAIC,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;MACzC,MAAMZ,KAAK,GAAGW,eAAe,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,EAAE,KAAKQ,OAAO,CAAC;MACzD,IAAIV,KAAK,EAAE;QACTN,UAAU,CAAC,QAAQ,CAAC;QACpBE,QAAQ,CAACI,KAAK,CAACe,UAAU,CAAC;QAC1BjB,cAAc,CAAC,GAAGE,KAAK,CAACgB,IAAI,KAAKhB,KAAK,CAACiB,SAAS,MAAMjB,KAAK,CAACkB,OAAO,KAAKlB,KAAK,CAACmB,YAAY,eAAe,CAAC;MAC5G;IACF;EACF,CAAC;EAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACC,WAAW,EAAE;IAElB,MAAMlC,aAAa,GAAGkC,WAAW,CAACjC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7Df,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,IAAI8C,QAAQ,IAAIC,aAAa,EAAE;QAC7B;QACA,MAAM9E,gBAAgB,CAAC0C,aAAa,EAAEoC,aAAa,EAAExB,WAAW,CAAC;MACnE,CAAC,MAAM;QACL;QACA,MAAMxD,iBAAiB,CAAC4C,aAAa,EAAEqC,OAAO,EAAE3B,KAAK,EAAEE,WAAW,CAAC;MACrE;MACAS,YAAY,CAAC,KAAK,CAAC;MACnBJ,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCC,QAAQ,CAAC,2DAA2D,CAAC;IACvE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrCnD,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,MAAMkD,kBAAkB,GAAG7D,kBAAkB,CAACuB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACzE,MAAMsC,gBAAgB,GAAG5D,gBAAgB,CAACqB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACrE,MAAM1C,YAAY,CAAC+E,kBAAkB,EAAEC,gBAAgB,EAAExD,iBAAiB,CAAC;MAC3EL,qBAAqB,CAAC,IAAI,CAAC;MAC3BE,mBAAmB,CAAC,IAAI,CAAC;MACzBI,oBAAoB,CAAC,EAAE,CAAC;MACxB;MACAwD,KAAK,CAAC,+BAA+B,CAAC;IACxC,CAAC,CAAC,OAAOrD,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,2DAA2D,CAAC;IACvE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEvB,OAAA,CAACtB,SAAS;IAAAoG,QAAA,gBACR9E,OAAA,CAACrB,GAAG;MAACoG,SAAS,EAAC,2BAA2B;MAAAD,QAAA,eACxC9E,OAAA,CAACpB,GAAG;QAACoG,EAAE,EAAC,MAAM;QAAAF,QAAA,gBACZ9E,OAAA;UAAA8E,QAAA,EAAI;QAA6B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCpF,OAAA,CAACqF,QAAQ;UACPC,QAAQ,EAAEC,OAAQ;UAClBhC,KAAK,EAAEpB,IAAK;UACZqD,UAAU,EAAElC,eAAgB;UAC5BmC,aAAa,EAAEA,CAAC;YAAEtD,IAAI;YAAEuD;UAAK,CAAC,KAAK;YACjC,IAAIA,IAAI,KAAK,OAAO,EAAE;cACpB,IAAI5E,kBAAkB,IAAIE,gBAAgB,EAAE;gBAC1C,IAAImB,IAAI,IAAIrB,kBAAkB,IAAIqB,IAAI,IAAInB,gBAAgB,EAAE;kBAC1D,OAAO,gBAAgB;gBACzB;cACF,CAAC,MAAM,IAAIF,kBAAkB,IAAIqB,IAAI,CAACwD,YAAY,CAAC,CAAC,KAAK7E,kBAAkB,CAAC6E,YAAY,CAAC,CAAC,EAAE;gBAC1F,OAAO,sBAAsB;cAC/B;YACF;YACA,OAAO,IAAI;UACb;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpF,OAAA,CAACrB,GAAG;MAACoG,SAAS,EAAC,gCAAgC;MAAAD,QAAA,eAC7C9E,OAAA,CAACpB,GAAG;QAACoG,EAAE,EAAC,MAAM;QAAAF,QAAA,gBACZ9E,OAAA;UAAA8E,QAAA,EAAI;QAAmC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC3C5D,KAAK,iBACJxB,OAAA,CAAChB,KAAK;UAAC4G,OAAO,EAAC,QAAQ;UAACb,SAAS,EAAC,MAAM;UAAAD,QAAA,EACrCtD;QAAK;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eACDpF,OAAA,CAACjB,IAAI;UAAA+F,QAAA,gBACH9E,OAAA,CAACjB,IAAI,CAAC8G,KAAK;YAACd,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1B9E,OAAA,CAACjB,IAAI,CAAC+G,KAAK;cAAAhB,QAAA,EAAC;YAAqB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9CpF,OAAA,CAACjB,IAAI,CAACgH,OAAO;cACXnD,IAAI,EAAC,MAAM;cACXW,KAAK,EACHzC,kBAAkB,IAAIE,gBAAgB,GAClC,GAAGF,kBAAkB,CAAC6E,YAAY,CAAC,CAAC,MAAM3E,gBAAgB,CAAC2E,YAAY,CAAC,CAAC,EAAE,GAC3E7E,kBAAkB,GAChBA,kBAAkB,CAAC6E,YAAY,CAAC,CAAC,GACjC,SACP;cACDK,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEbpF,OAAA,CAACjB,IAAI,CAAC8G,KAAK;YAACd,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1B9E,OAAA,CAACjB,IAAI,CAAC+G,KAAK;cAAAhB,QAAA,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3CpF,OAAA,CAACjB,IAAI,CAACkH,MAAM;cACV1C,KAAK,EAAEnC,iBAAkB;cACzBkE,QAAQ,EAAGY,CAAC,IAAK7E,oBAAoB,CAAC6E,CAAC,CAACC,MAAM,CAAC5C,KAAK,CAAE;cAAAuB,QAAA,gBAEtD9E,OAAA;gBAAQuD,KAAK,EAAC,EAAE;gBAAAuB,QAAA,EAAC;cAAqB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9ClE,WAAW,CAACkF,GAAG,CAAEC,OAAO,iBACvBrG,OAAA;gBAAyBuD,KAAK,EAAE8C,OAAO,CAAClD,EAAG;gBAAA2B,QAAA,EACxCuB,OAAO,CAACpC;cAAI,GADFoC,OAAO,CAAClD,EAAE;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEbpF,OAAA,CAACnB,MAAM;YACL+G,OAAO,EAAC,SAAS;YACjBU,OAAO,EAAE5B,kBAAmB;YAC5B6B,QAAQ,EAAEjF,OAAO,IAAI,CAACF,iBAAiB,IAAI,CAACN,kBAAkB,IAAI,CAACE,gBAAiB;YAAA8D,QAAA,EAEnFxD,OAAO,GAAG,cAAc,GAAG;UAAwB;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACTpF,OAAA,CAACnB,MAAM;YACL+G,OAAO,EAAC,WAAW;YACnBU,OAAO,EAAEA,CAAA,KAAM;cACbvF,qBAAqB,CAAC,IAAI,CAAC;cAC3BE,mBAAmB,CAAC,IAAI,CAAC;cACzBI,oBAAoB,CAAC,EAAE,CAAC;cACxBI,QAAQ,CAAC,EAAE,CAAC;YACd,CAAE;YACFsD,SAAS,EAAC,MAAM;YAAAD,QAAA,EACjB;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpF,OAAA,CAAClB,KAAK;MAAC0H,IAAI,EAAEC,SAAU;MAACC,MAAM,EAAEA,CAAA,KAAMjD,YAAY,CAAC,KAAK,CAAE;MAAAqB,QAAA,gBACxD9E,OAAA,CAAClB,KAAK,CAAC6H,MAAM;QAACC,WAAW;QAAA9B,QAAA,eACvB9E,OAAA,CAAClB,KAAK,CAAC+H,KAAK;UAAA/B,QAAA,GAAC,uBAAkB,EAACR,WAAW,IAAIA,WAAW,CAACqB,YAAY,CAAC,CAAC;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,eACfpF,OAAA,CAAClB,KAAK,CAACgI,IAAI;QAAAhC,QAAA,GACRtD,KAAK,iBACJxB,OAAA,CAAChB,KAAK;UAAC4G,OAAO,EAAC,QAAQ;UAACb,SAAS,EAAC,MAAM;UAAAD,QAAA,EACrCtD;QAAK;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eAEDpF,OAAA,CAACjB,IAAI;UAAA+F,QAAA,gBAEH9E,OAAA,CAACjB,IAAI,CAAC8G,KAAK;YAACd,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1B9E,OAAA,CAACjB,IAAI,CAACgI,KAAK;cACTnE,IAAI,EAAC,OAAO;cACZO,EAAE,EAAC,cAAc;cACjBc,IAAI,EAAC,cAAc;cACnB+C,KAAK,EAAC,gBAAgB;cACtBC,OAAO,EAAE,CAAC1C,QAAS;cACnBe,QAAQ,EAAEA,CAAA,KAAMlC,WAAW,CAAC,KAAK;YAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACFpF,OAAA,CAACjB,IAAI,CAACgI,KAAK;cACTnE,IAAI,EAAC,OAAO;cACZO,EAAE,EAAC,aAAa;cAChBc,IAAI,EAAC,cAAc;cACnB+C,KAAK,EAAC,wBAAwB;cAC9BC,OAAO,EAAE1C,QAAS;cAClBe,QAAQ,EAAEA,CAAA,KAAMlC,WAAW,CAAC,IAAI;YAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,EAGZb,QAAQ,iBACPvE,OAAA,CAAAE,SAAA;YAAA4E,QAAA,GACGoC,eAAe,CAACrD,MAAM,GAAG,CAAC,iBACzB7D,OAAA,CAACjB,IAAI,CAAC8G,KAAK;cAACd,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1B9E,OAAA,CAACjB,IAAI,CAAC+G,KAAK;gBAAAhB,QAAA,EAAC;cAAiC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1DpF,OAAA;gBAAK+E,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,EACzCoC,eAAe,CAACd,GAAG,CAAEnD,KAAK,iBACzBjD,OAAA,CAACf,KAAK;kBAEJkI,EAAE,EAAE3C,aAAa,KAAKvB,KAAK,CAACE,EAAE,GAAG,SAAS,GAAG,WAAY;kBACzDiE,KAAK,EAAE;oBAAEC,MAAM,EAAE;kBAAU,CAAE;kBAC7Bf,OAAO,EAAEA,CAAA,KAAM5C,iBAAiB,CAACT,KAAK,CAACE,EAAE,CAAE;kBAAA2B,QAAA,GAE1C7B,KAAK,CAACgB,IAAI,EAAC,IAAE,EAAChB,KAAK,CAACiB,SAAS,EAAC,KAAG,EAACjB,KAAK,CAACkB,OAAO,EAAC,GACnD;gBAAA,GANOlB,KAAK,CAACE,EAAE;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMR,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAEDpF,OAAA,CAACjB,IAAI,CAAC8G,KAAK;cAACd,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1B9E,OAAA,CAACjB,IAAI,CAAC+G,KAAK;gBAAAhB,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1CpF,OAAA,CAACjB,IAAI,CAACkH,MAAM;gBACV1C,KAAK,EAAEiB,aAAc;gBACrBc,QAAQ,EAAGY,CAAC,IAAKxC,iBAAiB,CAACwC,CAAC,CAACC,MAAM,CAAC5C,KAAK,CAAE;gBAAAuB,QAAA,gBAEnD9E,OAAA;kBAAQuD,KAAK,EAAC,EAAE;kBAAAuB,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC7CxB,eAAe,CAACwC,GAAG,CAAEnD,KAAK,iBACzBjD,OAAA;kBAAuBuD,KAAK,EAAEN,KAAK,CAACE,EAAG;kBAAA2B,QAAA,GACpC7B,KAAK,CAACgB,IAAI,EAAC,KAAG,EAAChB,KAAK,CAACiB,SAAS,EAAC,KAAG,EAACjB,KAAK,CAACkB,OAAO,EAAC,IAAE,EAAClB,KAAK,CAACe,UAAU,EAAC,IACxE;gBAAA,GAFaf,KAAK,CAACE,EAAE;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,eACb,CACH,EAGA,CAACb,QAAQ,iBACRvE,OAAA,CAAAE,SAAA;YAAA4E,QAAA,gBACE9E,OAAA,CAACjB,IAAI,CAAC8G,KAAK;cAACd,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1B9E,OAAA,CAACjB,IAAI,CAAC+G,KAAK;gBAAAhB,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpCpF,OAAA,CAACjB,IAAI,CAACkH,MAAM;gBAAC1C,KAAK,EAAEkB,OAAQ;gBAACa,QAAQ,EAAGY,CAAC,IAAKvD,UAAU,CAACuD,CAAC,CAACC,MAAM,CAAC5C,KAAK,CAAE;gBAAAuB,QAAA,gBACvE9E,OAAA;kBAAQuD,KAAK,EAAC,EAAE;kBAAAuB,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCpF,OAAA;kBAAQuD,KAAK,EAAC,QAAQ;kBAAAuB,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCpF,OAAA;kBAAQuD,KAAK,EAAC,SAAS;kBAAAuB,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3CpF,OAAA;kBAAQuD,KAAK,EAAC,QAAQ;kBAAAuB,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCpF,OAAA;kBAAQuD,KAAK,EAAC,UAAU;kBAAAuB,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACbpF,OAAA,CAACjB,IAAI,CAAC8G,KAAK;cAACd,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1B9E,OAAA,CAACjB,IAAI,CAAC+G,KAAK;gBAAAhB,QAAA,EAAC;cAAiC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1DpF,OAAA,CAACjB,IAAI,CAACgH,OAAO;gBACXnD,IAAI,EAAC,QAAQ;gBACbW,KAAK,EAAET,KAAM;gBACbwC,QAAQ,EAAGY,CAAC,IAAKrD,QAAQ,CAACyE,UAAU,CAACpB,CAAC,CAACC,MAAM,CAAC5C,KAAK,CAAC,CAAE;gBACtDgE,WAAW,EAAC;cAAiB;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA,eACb,CACH,eAGDpF,OAAA,CAACjB,IAAI,CAAC8G,KAAK;YAACd,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1B9E,OAAA,CAACjB,IAAI,CAAC+G,KAAK;cAAAhB,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpCpF,OAAA,CAACjB,IAAI,CAACgH,OAAO;cACXyB,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRlE,KAAK,EAAEP,WAAY;cACnBsC,QAAQ,EAAGY,CAAC,IAAKnD,cAAc,CAACmD,CAAC,CAACC,MAAM,CAAC5C,KAAK,CAAE;cAChDgE,WAAW,EAAC,wCAAkC;cAC9ChB,QAAQ,EAAEhC,QAAQ,IAAIC,aAAa,IAAI,CAACxB,WAAW,CAAC0E,QAAQ,CAAC,gBAAgB;YAAE;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,EACDb,QAAQ,IAAIC,aAAa,iBACxBxE,OAAA,CAACjB,IAAI,CAAC4I,IAAI;cAAC5C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAElC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CACZ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbpF,OAAA,CAAClB,KAAK,CAAC8I,MAAM;QAAA9C,QAAA,gBACX9E,OAAA,CAACnB,MAAM;UACL+G,OAAO,EAAC,WAAW;UACnBU,OAAO,EAAEA,CAAA,KAAM;YACb7C,YAAY,CAAC,KAAK,CAAC;YACnBJ,SAAS,CAAC,CAAC;UACb,CAAE;UACFkD,QAAQ,EAAEjF,OAAQ;UAAAwD,QAAA,EACnB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpF,OAAA,CAACnB,MAAM;UACL+G,OAAO,EAAC,SAAS;UACjBU,OAAO,EAAEjC,UAAW;UACpBkC,QAAQ,EAAEjF,OAAO,IAAK,CAACiD,QAAQ,IAAI,CAACE,OAAQ,IAAKF,QAAQ,IAAI,CAACC,aAAe;UAAAM,QAAA,EAE5ExD,OAAO,GAAG,cAAc,GAAG;QAAiB;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGC,CAAC;AAEhB;AAAChF,EAAA,CAvYQD,YAAY;AAAA0H,EAAA,GAAZ1H,YAAY;AAyYrB,eAAeA,YAAY;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}