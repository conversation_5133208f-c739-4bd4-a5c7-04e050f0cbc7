const axios = require('axios');

const testPattern = {
    name: "<PERSON>r<PERSON> de Prueba",
    description: "Patrón de prueba para verificar funcionalidad",
    basePattern: [
        { type: "rest", hours: 0, description: "Domingo - Descanso" },
        { type: "worked", hours: 8, description: "Lunes - Trabajo", shiftId: "morning" },
        { type: "worked", hours: 8, description: "Martes - Trabajo", shiftId: "morning" },
        { type: "worked", hours: 8, description: "Miércoles - Trabajo", shiftId: "morning" },
        { type: "worked", hours: 8, description: "Jueves - Trabajo", shiftId: "morning" },
        { type: "worked", hours: 8, description: "Viernes - Trabajo", shiftId: "morning" },
        { type: "rest", hours: 0, description: "Sábado - Descanso" }
    ],
    overrides: []
};

async function testCreatePattern() {
    try {
        console.log('Creando patrón de prueba...');
        const response = await axios.post('http://localhost:5000/api/patterns', testPattern);
        console.log('Patrón creado exitosamente:', response.data);
        
        console.log('Obteniendo todos los patrones...');
        const allPatterns = await axios.get('http://localhost:5000/api/patterns');
        console.log('Patrones disponibles:', allPatterns.data);
        
    } catch (error) {
        console.error('Error:', error.response ? error.response.data : error.message);
    }
}

testCreatePattern();
