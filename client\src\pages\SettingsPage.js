import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Button, Form, Alert, ListGroup, Modal } from 'react-bootstrap';
import { getAllPatterns, createPattern, updatePattern, deletePattern, getAllShifts, copyPattern } from '../services/api';

function SettingsPage() {
  const [patterns, setPatterns] = useState([]);
  const [currentPattern, setCurrentPattern] = useState(null);
  const [showPatternForm, setShowPatternForm] = useState(false);
  const [availableShifts, setAvailableShifts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showCopyModal, setShowCopyModal] = useState(false);
  const [patternToCopy, setPatternToCopy] = useState(null);
  const [newPatternName, setNewPatternName] = useState('');
  const [newPatternYear, setNewPatternYear] = useState(new Date().getFullYear() + 1);

  useEffect(() => {
    loadPatterns();
    loadAvailableShifts();
  }, []);

  const loadPatterns = async () => {
    setLoading(true);
    try {
      const data = await getAllPatterns();
      setPatterns(data);
    } catch (err) {
      setError('Error cargando patrones.');
      console.error('Error loading patterns:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadAvailableShifts = async () => {
    try {
      const shifts = await getAllShifts();
      setAvailableShifts(shifts);
    } catch (err) {
      console.error('Error loading shifts:', err);
    }
  };

  const handleAddPattern = () => {
    setCurrentPattern({
      name: '',
      basePattern: Array.from({ length: 7 }, () => ({ type: '', hours: 0, description: '', shiftId: null })),
      overrides: [],
    });
    setShowPatternForm(true);
  };

  const handleEditPattern = (pattern) => {
    setCurrentPattern({ ...pattern });
    setShowPatternForm(true);
  };

  const handleDeletePattern = async (id) => {
    if (window.confirm('¿Estás seguro de que quieres eliminar este patrón?')) {
      setLoading(true);
      try {
        await deletePattern(id);
        await loadPatterns();
      } catch (err) {
        setError('Error eliminando patrón.');
        console.error('Error deleting pattern:', err);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSavePattern = async () => {
    if (!currentPattern || !currentPattern.name || !currentPattern.basePattern) {
      setError('El nombre del patrón y el patrón base son requeridos.');
      return;
    }

    setLoading(true);
    setError('');
    try {
      if (currentPattern.id) {
        await updatePattern(currentPattern.id, currentPattern.name, currentPattern.basePattern, currentPattern.overrides);
      } else {
        await createPattern(currentPattern.name, currentPattern.basePattern, currentPattern.overrides);
      }
      setShowPatternForm(false);
      setCurrentPattern(null);
      await loadPatterns();
    } catch (err) {
      setError('Error guardando patrón.');
      console.error('Error saving pattern:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setShowPatternForm(false);
    setCurrentPattern(null);
    setError('');
  };

  const handleShowCopyModal = (pattern) => {
    setPatternToCopy(pattern);
    setNewPatternName(`${pattern.name} - ${new Date().getFullYear() + 1}`);
    setNewPatternYear(new Date().getFullYear() + 1);
    setShowCopyModal(true);
  };

  const handleCopyPattern = async () => {
    if (!patternToCopy || !newPatternName || !newPatternYear) {
      setError('Nombre y año para la copia son requeridos.');
      return;
    }

    setLoading(true);
    setError('');
    try {
      await copyPattern(patternToCopy.id, newPatternName, newPatternYear);
      setShowCopyModal(false);
      setPatternToCopy(null);
      setNewPatternName('');
      setNewPatternYear(new Date().getFullYear() + 1);
      await loadPatterns();
      alert('Patrón copiado exitosamente!');
    } catch (err) {
      setError('Error copiando patrón.');
      console.error('Error copying pattern:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleBasePatternDayChange = (index, field, value) => {
    const newBasePattern = [...currentPattern.basePattern];
    newBasePattern[index][field] = value;

    if (field === 'shiftId' && value) {
      const shift = availableShifts.find(s => s.id === value);
      if (shift) {
        newBasePattern[index].type = 'worked';
        newBasePattern[index].hours = shift.totalHours;
        newBasePattern[index].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;
      }
    } else if (field === 'shiftId' && !value) {
      newBasePattern[index].description = '';
    }
    setCurrentPattern({ ...currentPattern, basePattern: newBasePattern });
  };

  const handleOverrideChange = (overrideIndex, field, value) => {
    const newOverrides = [...currentPattern.overrides];
    newOverrides[overrideIndex][field] = value;
    setCurrentPattern({ ...currentPattern, overrides: newOverrides });
  };

  const handleOverridePatternDayChange = (overrideIndex, dayIndex, field, value) => {
    const newOverrides = [...currentPattern.overrides];
    const newOverridePattern = [...newOverrides[overrideIndex].overridePattern];
    newOverridePattern[dayIndex][field] = value;

    if (field === 'shiftId' && value) {
      const shift = availableShifts.find(s => s.id === value);
      if (shift) {
        newOverridePattern[dayIndex].type = 'worked';
        newOverridePattern[dayIndex].hours = shift.totalHours;
        newOverridePattern[dayIndex].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;
      }
    } else if (field === 'shiftId' && !value) {
      newOverridePattern[dayIndex].description = '';
    }

    newOverrides[overrideIndex].overridePattern = newOverridePattern;
    setCurrentPattern({ ...currentPattern, overrides: newOverrides });
  };

  const handleAddOverride = () => {
    setCurrentPattern({
      ...currentPattern,
      overrides: [...currentPattern.overrides, { startDate: '', endDate: '', overridePattern: [{ type: '', hours: 0, description: '', shiftId: null }] }]
    });
  };

  const handleRemoveOverride = (overrideIndex) => {
    const newOverrides = currentPattern.overrides.filter((_, i) => i !== overrideIndex);
    setCurrentPattern({ ...currentPattern, overrides: newOverrides });
  };

  const handleAddOverridePatternDay = (overrideIndex) => {
    const newOverrides = [...currentPattern.overrides];
    newOverrides[overrideIndex].overridePattern.push({ type: '', hours: 0, description: '', shiftId: null });
    setCurrentPattern({ ...currentPattern, overrides: newOverrides });
  };

  const handleRemoveOverridePatternDay = (overrideIndex, dayIndex) => {
    const newOverrides = [...currentPattern.overrides];
    newOverrides[overrideIndex].overridePattern = newOverrides[overrideIndex].overridePattern.filter((_, i) => i !== dayIndex);
    setCurrentPattern({ ...currentPattern, overrides: newOverrides });
  };

  return (
    <Container>
      <Row className="my-4">
        <Col>
          <h2>Configuración de Patrones de Calendario</h2>
          {error && <Alert variant="danger">{error}</Alert>}
          <Button onClick={handleAddPattern} className="mb-3">Crear Nuevo Patrón</Button>

          {loading ? (
            <p>Cargando patrones...</p>
          ) : patterns.length === 0 ? (
            <p>No hay patrones definidos.</p>
          ) : (
            <ListGroup>
              {patterns.map(pattern => (
                <ListGroup.Item key={pattern.id} className="d-flex justify-content-between align-items-center">
                  {pattern.name}
                  <div>
                    <Button variant="info" size="sm" className="me-2" onClick={() => handleEditPattern(pattern)}>Editar</Button>
                    <Button variant="secondary" size="sm" className="me-2" onClick={() => handleShowCopyModal(pattern)}>Copiar</Button>
                    <Button variant="danger" size="sm" onClick={() => handleDeletePattern(pattern.id)}>Eliminar</Button>
                  </div>
                </ListGroup.Item>
              ))}
            </ListGroup>
          )}
        </Col>
      </Row>

      <Modal show={showPatternForm} onHide={handleCancelEdit} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>{currentPattern && currentPattern.id ? 'Editar Patrón' : 'Crear Nuevo Patrón'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {currentPattern && (
            <Form>
              <Form.Group className="mb-3">
                <Form.Label>Nombre del Patrón</Form.Label>
                <Form.Control
                  type="text"
                  value={currentPattern.name}
                  onChange={(e) => setCurrentPattern({ ...currentPattern, name: e.target.value })}
                />
              </Form.Group>

              <h5>Patrón Base (Semanal):</h5>
              {currentPattern.basePattern.map((day, index) => (
                <div key={index} className="border p-3 mb-2">
                  <h6>Día {['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'][index]}</h6>
                  <Form.Group className="mb-2">
                    <Form.Label>Tipo de Día</Form.Label>
                    <Form.Select
                      value={day.type}
                      onChange={(e) => handleBasePatternDayChange(index, 'type', e.target.value)}
                    >
                      <option value="">Seleccionar</option>
                      <option value="worked">Trabajado</option>
                      <option value="holiday">Vacaciones</option>
                      <option value="permit">Permiso</option>
                      <option value="negative">Cómputo Negativo</option>
                    </Form.Select>
                  </Form.Group>
                  <Form.Group className="mb-2">
                    <Form.Label>Horas</Form.Label>
                    <Form.Control
                      type="number"
                      value={day.hours}
                      onChange={(e) => handleBasePatternDayChange(index, 'hours', parseFloat(e.target.value))}
                    />
                  </Form.Group>
                  <Form.Group className="mb-2">
                    <Form.Label>Turno Predefinido</Form.Label>
                    <Form.Select
                      value={day.shiftId || ''}
                      onChange={(e) => handleBasePatternDayChange(index, 'shiftId', e.target.value)}
                    >
                      <option value="">Ninguno</option>
                      {availableShifts.map((shift) => (
                        <option key={shift.id} value={shift.id}>
                          {shift.name} - {shift.startTime} a {shift.endTime} ({shift.totalHours}h)
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                  <Form.Group className="mb-2">
                    <Form.Label>Descripción</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={1}
                      value={day.description}
                      onChange={(e) => handleBasePatternDayChange(index, 'description', e.target.value)}
                    />
                  </Form.Group>
                </div>
              ))}

              <h5 className="mt-4">Anulaciones (Overrides):</h5>
              {currentPattern.overrides.map((override, overrideIndex) => (
                <div key={overrideIndex} className="border p-3 mb-2 bg-light">
                  <h6>Anulación {overrideIndex + 1}</h6>
                  <Row className="mb-2">
                    <Col>
                      <Form.Group controlId={`overrideStartDate-${overrideIndex}`}>
                        <Form.Label>Fecha de Inicio</Form.Label>
                        <Form.Control
                          type="date"
                          value={override.startDate}
                          onChange={(e) => handleOverrideChange(overrideIndex, 'startDate', e.target.value)}
                        />
                      </Form.Group>
                    </Col>
                    <Col>
                      <Form.Group controlId={`overrideEndDate-${overrideIndex}`}>
                        <Form.Label>Fecha de Fin</Form.Label>
                        <Form.Control
                          type="date"
                          value={override.endDate}
                          onChange={(e) => handleOverrideChange(overrideIndex, 'endDate', e.target.value)}
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                  <h6>Días de la Anulación:</h6>
                  {override.overridePattern.map((day, dayIndex) => (
                    <div key={dayIndex} className="border p-3 mb-2 ms-3">
                      <h6>Día {dayIndex + 1}</h6>
                      <Form.Group className="mb-2">
                        <Form.Label>Tipo de Día</Form.Label>
                        <Form.Select
                          value={day.type}
                          onChange={(e) => handleOverridePatternDayChange(overrideIndex, dayIndex, 'type', e.target.value)}
                        >
                          <option value="">Seleccionar</option>
                          <option value="worked">Trabajado</option>
                          <option value="holiday">Vacaciones</option>
                          <option value="permit">Permiso</option>
                          <option value="negative">Cómputo Negativo</option>
                        </Form.Select>
                      </Form.Group>
                      <Form.Group className="mb-2">
                        <Form.Label>Horas</Form.Label>
                        <Form.Control
                          type="number"
                          value={day.hours}
                          onChange={(e) => handleOverridePatternDayChange(overrideIndex, dayIndex, 'hours', parseFloat(e.target.value))}
                        />
                      </Form.Group>
                      <Form.Group className="mb-2">
                        <Form.Label>Turno Predefinido</Form.Label>
                        <Form.Select
                          value={day.shiftId || ''}
                          onChange={(e) => handleOverridePatternDayChange(overrideIndex, dayIndex, 'shiftId', e.target.value)}
                        >
                          <option value="">Ninguno</option>
                          {availableShifts.map((shift) => (
                            <option key={shift.id} value={shift.id}>
                              {shift.name} - {shift.startTime} a {shift.endTime} ({shift.totalHours}h)
                            </option>
                          ))}
                        </Form.Select>
                      </Form.Group>
                      <Form.Group className="mb-2">
                        <Form.Label>Descripción</Form.Label>
                        <Form.Control
                          as="textarea"
                          rows={1}
                          value={day.description}
                          onChange={(e) => handleOverridePatternDayChange(overrideIndex, dayIndex, 'description', e.target.value)}
                        />
                      </Form.Group>
                      <Button variant="danger" size="sm" onClick={() => handleRemoveOverridePatternDay(overrideIndex, dayIndex)}>
                        Eliminar Día de Anulación
                      </Button>
                    </div>
                  ))}
                  <Button variant="secondary" onClick={() => handleAddOverridePatternDay(overrideIndex)} className="mt-3">Añadir Día a Anulación</Button>
                  <Button variant="danger" onClick={() => handleRemoveOverride(overrideIndex)} className="mt-3 ms-2">Eliminar Anulación</Button>
                </div>
              ))}
              <Button variant="info" onClick={handleAddOverride} className="mt-3">Añadir Anulación</Button>
            </Form>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCancelEdit}>Cancelar</Button>
          <Button variant="primary" onClick={handleSavePattern} disabled={loading}>Guardar Patrón</Button>
        </Modal.Footer>
      </Modal>

      {/* Modal para Copiar Patrón */}
      <Modal show={showCopyModal} onHide={() => setShowCopyModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Copiar Patrón</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          {patternToCopy && (
            <Form>
              <Form.Group className="mb-3">
                <Form.Label>Nombre del Patrón Original</Form.Label>
                <Form.Control type="text" value={patternToCopy.name} disabled />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Nuevo Nombre del Patrón</Form.Label>
                <Form.Control
                  type="text"
                  value={newPatternName}
                  onChange={(e) => setNewPatternName(e.target.value)}
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Año para el Nuevo Patrón</Form.Label>
                <Form.Control
                  type="number"
                  value={newPatternYear}
                  onChange={(e) => setNewPatternYear(parseInt(e.target.value, 10))}
                />
              </Form.Group>
            </Form>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowCopyModal(false)}>Cancelar</Button>
          <Button variant="primary" onClick={handleCopyPattern} disabled={loading}>Copiar Patrón</Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
}

export default SettingsPage;
