const axios = require('axios');

const testPatterns = async () => {
    try {
        console.log('Testing patterns API...');
        
        // Test GET all patterns
        console.log('1. Getting all patterns...');
        const response = await axios.get('http://localhost:5000/api/patterns');
        console.log('Patterns:', response.data);
        
        // Test creating a pattern
        console.log('2. Creating a test pattern...');
        const newPattern = {
            name: 'Test Pattern',
            basePattern: [
                { type: 'holiday', hours: 0, description: '<PERSON>' },
                { type: 'worked', hours: 8, description: '<PERSON><PERSON>' },
                { type: 'worked', hours: 8, description: 'Mart<PERSON>' },
                { type: 'worked', hours: 8, description: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
                { type: 'worked', hours: 8, description: 'Jueves' },
                { type: 'worked', hours: 8, description: 'Vier<PERSON>' },
                { type: 'holiday', hours: 0, description: 'Sábado' }
            ],
            overrides: []
        };
        
        const createResponse = await axios.post('http://localhost:5000/api/patterns', newPattern);
        console.log('Created pattern:', createResponse.data);
        
    } catch (error) {
        console.error('Error testing patterns:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
};

testPatterns();
