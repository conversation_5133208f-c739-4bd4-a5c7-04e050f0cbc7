import React, { useState, useEffect } from 'react';
import { Row, Col, Button, Alert, Table, Badge, Modal, Form } from 'react-bootstrap';
import { getAllRealDays, createOrUpdateRealDay, deleteRealDay, getAllShifts } from '../services/api';

function RealCalendarView({ onDateSelect, selectedDate, onCalendarUpdate }) {
  const [realDays, setRealDays] = useState([]);
  const [shifts, setShifts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingDay, setEditingDay] = useState(null);

  // Form state
  const [formData, setFormData] = useState({
    date: '',
    shiftId: '',
    customHours: '',
    entryTime: '',
    exitTime: '',
    description: ''
  });

  useEffect(() => {
    loadRealDays();
    loadShifts();
  }, []);

  const loadRealDays = async () => {
    try {
      setLoading(true);
      const days = await getAllRealDays();
      setRealDays(days);
      setError('');
    } catch (err) {
      console.error('Error loading real days:', err);
      setError('Error cargando días reales: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const loadShifts = async () => {
    try {
      const shiftsData = await getAllShifts();
      setShifts(shiftsData);
    } catch (err) {
      console.error('Error loading shifts:', err);
    }
  };

  const handleAddDay = () => {
    setEditingDay(null);
    setFormData({
      date: new Date().toISOString().split('T')[0],
      shiftId: '',
      customHours: '',
      entryTime: '',
      exitTime: '',
      description: ''
    });
    setShowModal(true);
  };

  const handleEditDay = (day) => {
    setEditingDay(day);
    setFormData({
      date: day.date,
      shiftId: day.shiftId || '',
      customHours: day.hours || '',
      entryTime: day.entryTime || '',
      exitTime: day.exitTime || '',
      description: day.description || ''
    });
    setShowModal(true);
  };

  const handleDeleteDay = async (dayDate) => {
    if (window.confirm('¿Estás seguro de que quieres eliminar este día trabajado?')) {
      try {
        setLoading(true);
        await deleteRealDay(dayDate);
        await loadRealDays();
        if (onCalendarUpdate) onCalendarUpdate();
      } catch (err) {
        setError('Error eliminando día: ' + err.message);
      } finally {
        setLoading(false);
      }
    }
  };

  const getTotalHours = () => {
    return realDays.reduce((total, day) => total + (day.hours || 0), 0);
  };

  const getCurrentMonthHours = () => {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    return realDays
      .filter(day => {
        const dayDate = new Date(day.date);
        return dayDate.getMonth() === currentMonth && dayDate.getFullYear() === currentYear;
      })
      .reduce((total, day) => total + (day.hours || 0), 0);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);

      // Calcular horas automáticamente si se proporcionan horas de entrada y salida
      let hours = parseFloat(formData.customHours) || 0;
      if (formData.entryTime && formData.exitTime && !formData.customHours) {
        const entry = new Date(`2000-01-01T${formData.entryTime}`);
        const exit = new Date(`2000-01-01T${formData.exitTime}`);
        hours = (exit - entry) / (1000 * 60 * 60); // Convertir a horas
      }

      const dayData = {
        date: formData.date,
        shiftId: formData.shiftId || null,
        hours: hours,
        entryTime: formData.entryTime || null,
        exitTime: formData.exitTime || null,
        description: formData.description || null
      };

      if (editingDay) {
        dayData.id = editingDay.id;
      }

      await createOrUpdateRealDay(dayData);
      await loadRealDays();
      if (onCalendarUpdate) onCalendarUpdate();
      setShowModal(false);
      setError('');
    } catch (err) {
      setError('Error guardando día: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const getShiftName = (shiftId) => {
    const shift = shifts.find(s => s.id === shiftId);
    return shift ? shift.name : 'Personalizado';
  };

  return (
    <div>
      <Row className="mb-3">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <h4>Calendario de Trabajo Real</h4>
            <div>
              <Button
                variant="success"
                onClick={handleAddDay}
                disabled={loading}
              >
                ➕ Registrar Día
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {error && (
        <Row className="mb-3">
          <Col>
            <Alert variant="danger" onClose={() => setError('')} dismissible>
              {error}
            </Alert>
          </Col>
        </Row>
      )}

      <Row className="mb-3">
        <Col md={8}>
          <div className="calendar-container">
            <h5>Días Trabajados Registrados</h5>
            {loading ? (
              <p>Cargando días reales...</p>
            ) : realDays.length > 0 ? (
              <Table striped bordered hover responsive>
                <thead>
                  <tr>
                    <th>Fecha</th>
                    <th>Turno</th>
                    <th>Horas</th>
                    <th>Entrada</th>
                    <th>Salida</th>
                    <th>Descripción</th>
                    <th>Acciones</th>
                  </tr>
                </thead>
                <tbody>
                  {realDays
                    .sort((a, b) => new Date(b.date) - new Date(a.date))
                    .map(day => (
                      <tr key={day.id}>
                        <td>
                          {new Date(day.date).toLocaleDateString('es-ES', {
                            weekday: 'short',
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric'
                          })}
                        </td>
                        <td>
                          <Badge bg="secondary">
                            {getShiftName(day.shiftId)}
                          </Badge>
                        </td>
                        <td><strong>{day.hours}h</strong></td>
                        <td>{day.entryTime || '-'}</td>
                        <td>{day.exitTime || '-'}</td>
                        <td>{day.description || '-'}</td>
                        <td>
                          <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={() => handleEditDay(day)}
                            className="me-1"
                          >
                            ✏️
                          </Button>
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => handleDeleteDay(day.date)}
                          >
                            🗑️
                          </Button>
                        </td>
                      </tr>
                    ))}
                </tbody>
              </Table>
            ) : (
              <Alert variant="info">
                No hay días trabajados registrados. ¡Haz clic en "Registrar Día" para empezar!
              </Alert>
            )}
          </div>
        </Col>
        <Col md={4}>
          <div className="calendar-stats">
            <h5>Estadísticas Reales</h5>
            <div className="stat-item">
              <strong>Total de días registrados:</strong> {realDays.length}
            </div>
            <div className="stat-item">
              <strong>Total de horas:</strong> {getTotalHours().toFixed(1)}h
            </div>
            <div className="stat-item">
              <strong>Horas este mes:</strong> {getCurrentMonthHours().toFixed(1)}h
            </div>
            <div className="stat-item">
              <strong>Promedio por día:</strong> {realDays.length > 0 ? (getTotalHours() / realDays.length).toFixed(1) : 0}h
            </div>
          </div>
        </Col>
      </Row>

      {/* Modal para registrar/editar días */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {editingDay ? 'Editar Día Trabajado' : 'Registrar Nuevo Día'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={(e) => e.preventDefault()}>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Fecha *</Form.Label>
                  <Form.Control
                    type="date"
                    value={formData.date}
                    onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Turno</Form.Label>
                  <Form.Select
                    value={formData.shiftId}
                    onChange={(e) => setFormData({ ...formData, shiftId: e.target.value })}
                  >
                    <option value="">Personalizado</option>
                    {shifts.map(shift => (
                      <option key={shift.id} value={shift.id}>
                        {shift.name} ({shift.hours}h)
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Horas Trabajadas</Form.Label>
                  <Form.Control
                    type="number"
                    step="0.5"
                    min="0"
                    max="24"
                    value={formData.customHours}
                    onChange={(e) => setFormData({ ...formData, customHours: e.target.value })}
                    placeholder="Ej: 8"
                  />
                  <Form.Text className="text-muted">
                    Se calculará automáticamente si defines entrada y salida
                  </Form.Text>
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Hora de Entrada</Form.Label>
                  <Form.Control
                    type="time"
                    value={formData.entryTime}
                    onChange={(e) => setFormData({ ...formData, entryTime: e.target.value })}
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Hora de Salida</Form.Label>
                  <Form.Control
                    type="time"
                    value={formData.exitTime}
                    onChange={(e) => setFormData({ ...formData, exitTime: e.target.value })}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Descripción</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Notas adicionales sobre este día trabajado..."
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Cancelar
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            disabled={loading || !formData.date}
          >
            {loading ? 'Guardando...' : (editingDay ? 'Actualizar' : 'Registrar')}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
}

export default RealCalendarView;
