const calendarService = require('../services/calendarService');
const patternService = require('../services/patternService');

const getAllDays = (req, res) => {
    try {
        const days = calendarService.getAllDays();
        res.json(days);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getDayByDate = (req, res) => {
    try {
        const { date } = req.params;
        const day = calendarService.getDayByDate(date);
        if (day) {
            res.json(day);
        } else {
            res.status(404).json({ message: 'Day not found' });
        }
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const createOrUpdateDay = (req, res) => {
    try {
        const { date, type, hours, description, shiftId } = req.body;
        if (!date || !type) {
            return res.status(400).json({ message: 'Date and type are required' });
        }
        const updatedDay = calendarService.createOrUpdateDay(date, type, hours, description, shiftId);
        res.status(200).json(updatedDay);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const applyPatternToDates = (req, res) => {
    try {
        const { startDate, endDate, pattern, patternId } = req.body;

        if (!startDate || !endDate) {
            return res.status(400).json({ message: 'Start date and end date are required.' });
        }

        // Support both pattern array and patternId
        if (patternId) {
            // Use pattern by ID
            const result = patternService.applyPatternToDates(startDate, endDate, patternId);
            res.status(200).json(result);
        } else if (pattern && Array.isArray(pattern)) {
            // Use pattern array directly (legacy support)
            // TODO: Implement legacy pattern array support if needed
            return res.status(400).json({ message: 'Pattern array support not implemented. Use patternId instead.' });
        } else {
            return res.status(400).json({ message: 'Either a valid pattern array or patternId is required.' });
        }
    } catch (error) {
        console.error('Error in applyPatternToDates:', error);
        res.status(500).json({ message: error.message });
    }
};

module.exports = {
    getAllDays,
    getDayByDate,
    createOrUpdateDay,
    applyPatternToDates
};
