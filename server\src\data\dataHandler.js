const fs = require('fs');
const path = require('path');

const DATA_FILE = path.join(__dirname, '../../data.json');

const readData = () => {
    if (!fs.existsSync(DATA_FILE)) {
        return { days: [], patternCalendar: {}, holidays: [], permits: [], patterns: [] };
    }
    const data = fs.readFileSync(DATA_FILE, 'utf8');
    return JSON.parse(data);
};

const writeData = (data) => {
    fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2), 'utf8');
};

module.exports = {
    readData,
    writeData
};
