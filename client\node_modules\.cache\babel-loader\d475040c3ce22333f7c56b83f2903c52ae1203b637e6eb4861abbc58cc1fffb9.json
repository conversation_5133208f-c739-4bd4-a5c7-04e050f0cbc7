{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\components\\\\NavbarComponent.js\";\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction NavbarComponent() {\n  return /*#__PURE__*/_jsxDEV(Navbar, {\n    bg: \"dark\",\n    variant: \"dark\",\n    expand: \"lg\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(Navbar.Brand, {\n        as: Link,\n        to: \"/\",\n        children: \"Control Horario\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Navbar.Toggle, {\n        \"aria-controls\": \"basic-navbar-nav\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Navbar.Collapse, {\n        id: \"basic-navbar-nav\",\n        children: /*#__PURE__*/_jsxDEV(Nav, {\n          className: \"me-auto\",\n          children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/\",\n            children: \"Calendario\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/reports\",\n            children: \"Informes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/settings\",\n            children: \"Configuraci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = NavbarComponent;\nexport default NavbarComponent;\nvar _c;\n$RefreshReg$(_c, \"NavbarComponent\");", "map": {"version": 3, "names": ["Link", "jsxDEV", "_jsxDEV", "NavbarComponent", "<PERSON><PERSON><PERSON>", "bg", "variant", "expand", "children", "Container", "Brand", "as", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Toggle", "Collapse", "id", "Nav", "className", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/components/NavbarComponent.js"], "sourcesContent": ["import { Link } from 'react-router-dom';\n\nfunction NavbarComponent() {\n  return (\n    <Navbar bg=\"dark\" variant=\"dark\" expand=\"lg\">\n      <Container>\n        <Navbar.Brand as={Link} to=\"/\">Control Horario</Navbar.Brand>\n        <Navbar.Toggle aria-controls=\"basic-navbar-nav\" />\n        <Navbar.Collapse id=\"basic-navbar-nav\">\n          <Nav className=\"me-auto\">\n            <Nav.Link as={Link} to=\"/\">Calendario</Nav.Link>\n            <Nav.Link as={Link} to=\"/reports\">Informes</Nav.Link>\n            <Nav.Link as={Link} to=\"/settings\">Configuración</Nav.Link>\n          </Nav>\n        </Navbar.Collapse>\n      </Container>\n    </Navbar>\n  );\n}\n\nexport default NavbarComponent;"], "mappings": ";AAAA,SAASA,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,eAAeA,CAAA,EAAG;EACzB,oBACED,OAAA,CAACE,MAAM;IAACC,EAAE,EAAC,MAAM;IAACC,OAAO,EAAC,MAAM;IAACC,MAAM,EAAC,IAAI;IAAAC,QAAA,eAC1CN,OAAA,CAACO,SAAS;MAAAD,QAAA,gBACRN,OAAA,CAACE,MAAM,CAACM,KAAK;QAACC,EAAE,EAAEX,IAAK;QAACY,EAAE,EAAC,GAAG;QAAAJ,QAAA,EAAC;MAAe;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAC7Dd,OAAA,CAACE,MAAM,CAACa,MAAM;QAAC,iBAAc;MAAkB;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClDd,OAAA,CAACE,MAAM,CAACc,QAAQ;QAACC,EAAE,EAAC,kBAAkB;QAAAX,QAAA,eACpCN,OAAA,CAACkB,GAAG;UAACC,SAAS,EAAC,SAAS;UAAAb,QAAA,gBACtBN,OAAA,CAACkB,GAAG,CAACpB,IAAI;YAACW,EAAE,EAAEX,IAAK;YAACY,EAAE,EAAC,GAAG;YAAAJ,QAAA,EAAC;UAAU;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAChDd,OAAA,CAACkB,GAAG,CAACpB,IAAI;YAACW,EAAE,EAAEX,IAAK;YAACY,EAAE,EAAC,UAAU;YAAAJ,QAAA,EAAC;UAAQ;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACrDd,OAAA,CAACkB,GAAG,CAACpB,IAAI;YAACW,EAAE,EAAEX,IAAK;YAACY,EAAE,EAAC,WAAW;YAAAJ,QAAA,EAAC;UAAa;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEb;AAACM,EAAA,GAhBQnB,eAAe;AAkBxB,eAAeA,eAAe;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}