const { readData, writeData } = require('../data/dataHandler');
const shiftService = require('./shiftService');

// ===== CALENDARIO REAL =====
const getAllRealDays = () => {
    const data = readData();
    return data.realCalendar || [];
};

const getRealDayByDate = (date) => {
    const data = readData();
    return data.realCalendar?.find(day => day.date === date);
};

const createOrUpdateRealDay = (date, type, hours = 0, description = '', shiftId = null) => {
    const data = readData();
    if (!data.realCalendar) data.realCalendar = [];

    let day = data.realCalendar.find(d => d.date === date);

    if (day) {
        day.type = type;
        day.hours = hours;
        day.description = description;
        day.shiftId = shiftId;
    } else {
        day = { date, type, hours, description, shiftId };
        data.realCalendar.push(day);
    }
    writeData(data);
    return day;
};

// ===== CALENDARIO PLANIFICADO =====
const getAllPlannedDays = () => {
    const data = readData();
    return data.plannedCalendar || [];
};

const getPlannedDayByDate = (date) => {
    const data = readData();
    return data.plannedCalendar?.find(day => day.date === date);
};

const createOrUpdatePlannedDay = (date, type, hours = 0, description = '', shiftId = null) => {
    const data = readData();
    if (!data.plannedCalendar) data.plannedCalendar = [];

    let day = data.plannedCalendar.find(d => d.date === date);

    if (day) {
        day.type = type;
        day.hours = hours;
        day.description = description;
        day.shiftId = shiftId;
    } else {
        day = { date, type, hours, description, shiftId };
        data.plannedCalendar.push(day);
    }
    writeData(data);
    return day;
};

const clearPlannedCalendar = () => {
    const data = readData();
    data.plannedCalendar = [];
    writeData(data);
    return true;
};

// ===== COMPATIBILIDAD CON VERSIÓN ANTERIOR =====
const getAllDays = () => getAllRealDays();
const getDayByDate = (date) => getRealDayByDate(date);
const createOrUpdateDay = (date, type, hours, description, shiftId) =>
    createOrUpdateRealDay(date, type, hours, description, shiftId);

module.exports = {
    // Calendario Real
    getAllRealDays,
    getRealDayByDate,
    createOrUpdateRealDay,

    // Calendario Planificado
    getAllPlannedDays,
    getPlannedDayByDate,
    createOrUpdatePlannedDay,
    clearPlannedCalendar,

    // Compatibilidad
    getAllDays,
    getDayByDate,
    createOrUpdateDay
};
