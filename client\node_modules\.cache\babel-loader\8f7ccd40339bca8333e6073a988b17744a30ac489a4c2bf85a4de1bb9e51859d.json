{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\CalendarPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Calendar from 'react-calendar';\nimport 'react-calendar/dist/Calendar.css';\nimport { Container, Row, Col, Button, Modal, Form, Alert, Badge } from 'react-bootstrap';\nimport { getDayByDate, createOrUpdateDay, getAllShifts, applyShiftToDate, suggestShiftsForDate, applyPattern, getAllPatterns } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction CalendarPage() {\n  _s();\n  const [date, setDate] = useState(new Date());\n  const [showModal, setShowModal] = useState(false);\n  const [selectedDay, setSelectedDay] = useState(null);\n  const [dayType, setDayType] = useState('');\n  const [hours, setHours] = useState(0);\n  const [description, setDescription] = useState('');\n\n  // Estados para turnos predefinidos\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [selectedShift, setSelectedShift] = useState('');\n  const [suggestedShifts, setSuggestedShifts] = useState([]);\n  const [useShift, setUseShift] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estados para el calendario patrón\n  const [showPatternModal, setShowPatternModal] = useState(false);\n  const [patternStartDate, setPatternStartDate] = useState(new Date());\n  const [patternEndDate, setPatternEndDate] = useState(new Date());\n  const [allPatterns, setAllPatterns] = useState([]); // New state for all patterns\n  const [selectedPatternId, setSelectedPatternId] = useState(''); // New state for selected pattern\n\n  useEffect(() => {\n    // Cargar turnos disponibles al montar el componente\n    loadAvailableShifts();\n    loadAllPatterns(); // Load all patterns\n  }, []);\n  useEffect(() => {\n    if (selectedDay) {\n      fetchDayDetails(selectedDay);\n      loadSuggestedShifts(selectedDay);\n    }\n  }, [selectedDay]);\n  const loadAllPatterns = async () => {\n    try {\n      const patterns = await getAllPatterns();\n      setAllPatterns(patterns);\n    } catch (error) {\n      console.error('Error loading patterns:', error);\n      setError('Error cargando patrones.');\n    }\n  };\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (error) {\n      console.error('Error loading shifts:', error);\n      setError('Error cargando turnos predefinidos');\n    }\n  };\n  const loadSuggestedShifts = async date => {\n    try {\n      const formattedDate = date.toISOString().split('T')[0];\n      const suggestions = await suggestShiftsForDate(formattedDate);\n      setSuggestedShifts(suggestions.suggestions || []);\n    } catch (error) {\n      console.error('Error loading suggested shifts:', error);\n      setSuggestedShifts([]);\n    }\n  };\n  const fetchDayDetails = async date => {\n    try {\n      const formattedDate = date.toISOString().split('T')[0];\n      const response = await getDayByDate(formattedDate);\n      if (response) {\n        setDayType(response.type || '');\n        setHours(response.hours || 0);\n        setDescription(response.description || '');\n        // Si el día tiene un turno asignado, mostrar esa información\n        if (response.shift) {\n          setSelectedShift(response.shift.id);\n          setUseShift(true);\n        } else {\n          setSelectedShift('');\n          setUseShift(false);\n        }\n      } else {\n        resetForm();\n      }\n    } catch (error) {\n      console.error('Error fetching day details:', error);\n      resetForm();\n    }\n  };\n  const resetForm = () => {\n    setDayType('');\n    setHours(0);\n    setDescription('');\n    setSelectedShift('');\n    setUseShift(false);\n    setError('');\n  };\n  const handleDateClick = value => {\n    setSelectedDay(value);\n    setShowModal(true);\n    setError('');\n  };\n  const handleShiftChange = shiftId => {\n    setSelectedShift(shiftId);\n    if (shiftId && availableShifts.length > 0) {\n      const shift = availableShifts.find(s => s.id === shiftId);\n      if (shift) {\n        setDayType('worked');\n        setHours(shift.totalHours);\n        setDescription(`${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`);\n      }\n    }\n  };\n  const handleSave = async () => {\n    if (!selectedDay) return;\n    const formattedDate = selectedDay.toISOString().split('T')[0];\n    setLoading(true);\n    setError('');\n    try {\n      if (useShift && selectedShift) {\n        // Usar turno predefinido\n        await applyShiftToDate(formattedDate, selectedShift, description);\n      } else {\n        // Usar entrada manual\n        await createOrUpdateDay(formattedDate, dayType, hours, description);\n      }\n      setShowModal(false);\n      resetForm();\n    } catch (error) {\n      console.error('Error saving day:', error);\n      setError('Error guardando los datos. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleApplyPattern = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      const formattedStartDate = patternStartDate.toISOString().split('T')[0];\n      const formattedEndDate = patternEndDate.toISOString().split('T')[0];\n      await applyPattern(formattedStartDate, formattedEndDate, selectedPatternId);\n      setShowPatternModal(false);\n      setSelectedPatternId(''); // Reset selected pattern\n      // Optionally, refresh calendar data here if needed\n      alert('Patrón aplicado exitosamente!');\n    } catch (error) {\n      console.error('Error applying pattern:', error);\n      setError('Error aplicando el patrón. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"justify-content-md-center\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: \"auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Calendario de D\\xEDas Trabajados\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Calendar, {\n          onChange: setDate,\n          value: date,\n          onClickDay: handleDateClick\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"info\",\n          className: \"mt-3\",\n          onClick: () => setShowPatternModal(true),\n          children: \"Aplicar Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [\"Detalles del D\\xEDa: \", selectedDay && selectedDay.toDateString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"mb-3\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n              type: \"radio\",\n              id: \"manual-entry\",\n              name: \"entry-method\",\n              label: \"Entrada manual\",\n              checked: !useShift,\n              onChange: () => setUseShift(false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n              type: \"radio\",\n              id: \"shift-entry\",\n              name: \"entry-method\",\n              label: \"Usar turno predefinido\",\n              checked: useShift,\n              onChange: () => setUseShift(true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), useShift && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [suggestedShifts.length > 0 && /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Turnos sugeridos para esta fecha:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex flex-wrap gap-2 mb-2\",\n                children: suggestedShifts.map(shift => /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: selectedShift === shift.id ? \"primary\" : \"secondary\",\n                  style: {\n                    cursor: 'pointer'\n                  },\n                  onClick: () => handleShiftChange(shift.id),\n                  children: [shift.name, \" (\", shift.startTime, \" - \", shift.endTime, \")\"]\n                }, shift.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Seleccionar Turno\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: selectedShift,\n                onChange: e => handleShiftChange(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Seleccionar turno...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this), availableShifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: shift.id,\n                  children: [shift.name, \" - \", shift.startTime, \" a \", shift.endTime, \" (\", shift.totalHours, \"h)\"]\n                }, shift.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), !useShift && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Tipo de D\\xEDa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: dayType,\n                onChange: e => setDayType(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Seleccionar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"worked\",\n                  children: \"Trabajado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"holiday\",\n                  children: \"Vacaciones\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"permit\",\n                  children: \"Permiso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"negative\",\n                  children: \"C\\xF3mputo Negativo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Horas (C\\xF3mputo Positivo/Negativo)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"number\",\n                value: hours,\n                onChange: e => setHours(parseFloat(e.target.value)),\n                placeholder: \"Introduce horas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Descripci\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              value: description,\n              onChange: e => setDescription(e.target.value),\n              placeholder: \"A\\xF1ade una descripci\\xF3n (opcional)\",\n              disabled: useShift && selectedShift && !description.includes('Personalizado:')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), useShift && selectedShift && /*#__PURE__*/_jsxDEV(Form.Text, {\n              className: \"text-muted\",\n              children: \"La descripci\\xF3n se genera autom\\xE1ticamente. Puedes editarla si es necesario.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => {\n            setShowModal(false);\n            resetForm();\n          },\n          disabled: loading,\n          children: \"Cerrar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSave,\n          disabled: loading || !useShift && !dayType || useShift && !selectedShift,\n          children: loading ? 'Guardando...' : 'Guardar Cambios'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPatternModal,\n      onHide: () => setShowPatternModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Aplicar Patr\\xF3n de D\\xEDas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"mb-3\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                controlId: \"patternStartDate\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de Inicio\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: patternStartDate.toISOString().split('T')[0],\n                  onChange: e => setPatternStartDate(new Date(e.target.value))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                controlId: \"patternEndDate\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de Fin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: patternEndDate.toISOString().split('T')[0],\n                  onChange: e => setPatternEndDate(new Date(e.target.value))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Seleccionar Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: selectedPatternId,\n              onChange: e => setSelectedPatternId(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Seleccionar patr\\xF3n...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this), allPatterns.map(pattern => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: pattern.id,\n                children: pattern.name\n              }, pattern.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => {\n            setShowPatternModal(false);\n            setError('');\n            setSelectedPatternId(''); // Reset selected pattern\n          },\n          children: \"Cerrar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleApplyPattern,\n          disabled: loading || !selectedPatternId,\n          children: loading ? 'Aplicando...' : 'Aplicar Patrón'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this);\n}\n_s(CalendarPage, \"l+Im34mPMi4/okxnJlNG4ahm9iM=\");\n_c = CalendarPage;\nexport default CalendarPage;\nvar _c;\n$RefreshReg$(_c, \"CalendarPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Calendar", "Container", "Row", "Col", "<PERSON><PERSON>", "Modal", "Form", "<PERSON><PERSON>", "Badge", "getDayByDate", "createOrUpdateDay", "getAllShifts", "applyShiftToDate", "suggestShiftsForDate", "applyPattern", "getAllPatterns", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CalendarPage", "_s", "date", "setDate", "Date", "showModal", "setShowModal", "selected<PERSON>ay", "setSelectedDay", "dayType", "setDayType", "hours", "setHours", "description", "setDescription", "availableShifts", "setAvailableShifts", "selectedShift", "setSelectedShift", "suggestedShifts", "setSuggestedShifts", "useShift", "setUseShift", "loading", "setLoading", "error", "setError", "showPatternModal", "setShowPatternModal", "patternStartDate", "setPatternStartDate", "patternEndDate", "setPatternEndDate", "allPatterns", "setAllPatterns", "selectedPatternId", "setSelectedPatternId", "loadAvailableShifts", "loadAllPatterns", "fetchDayDetails", "loadSuggestedShifts", "patterns", "console", "shifts", "formattedDate", "toISOString", "split", "suggestions", "response", "type", "shift", "id", "resetForm", "handleDateClick", "value", "handleShiftChange", "shiftId", "length", "find", "s", "totalHours", "name", "startTime", "endTime", "breakMinutes", "handleSave", "handleApplyPattern", "formattedStartDate", "formattedEndDate", "alert", "children", "className", "md", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "onClickDay", "variant", "onClick", "show", "onHide", "Header", "closeButton", "Title", "toDateString", "Body", "Group", "Check", "label", "checked", "Label", "map", "bg", "style", "cursor", "Select", "e", "target", "Control", "parseFloat", "placeholder", "as", "rows", "disabled", "includes", "Text", "Footer", "size", "controlId", "pattern", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/CalendarPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Calendar from 'react-calendar';\nimport 'react-calendar/dist/Calendar.css';\nimport { Container, Row, Col, Button, Modal, Form, Alert, Badge } from 'react-bootstrap';\nimport {\n  getDayByDate,\n  createOrUpdateDay,\n  getAllShifts,\n  applyShiftToDate,\n  suggestShiftsForDate,\n  applyPattern,\n  getAllPatterns\n} from '../services/api';\n\nfunction CalendarPage() {\n  const [date, setDate] = useState(new Date());\n  const [showModal, setShowModal] = useState(false);\n  const [selectedDay, setSelectedDay] = useState(null);\n  const [dayType, setDayType] = useState('');\n  const [hours, setHours] = useState(0);\n  const [description, setDescription] = useState('');\n\n  // Estados para turnos predefinidos\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [selectedShift, setSelectedShift] = useState('');\n  const [suggestedShifts, setSuggestedShifts] = useState([]);\n  const [useShift, setUseShift] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estados para el calendario patrón\n  const [showPatternModal, setShowPatternModal] = useState(false);\n  const [patternStartDate, setPatternStartDate] = useState(new Date());\n  const [patternEndDate, setPatternEndDate] = useState(new Date());\n  const [allPatterns, setAllPatterns] = useState([]); // New state for all patterns\n  const [selectedPatternId, setSelectedPatternId] = useState(''); // New state for selected pattern\n\n  useEffect(() => {\n    // Cargar turnos disponibles al montar el componente\n    loadAvailableShifts();\n    loadAllPatterns(); // Load all patterns\n  }, []);\n\n  useEffect(() => {\n    if (selectedDay) {\n      fetchDayDetails(selectedDay);\n      loadSuggestedShifts(selectedDay);\n    }\n  }, [selectedDay]);\n\n  const loadAllPatterns = async () => {\n    try {\n      const patterns = await getAllPatterns();\n      setAllPatterns(patterns);\n    } catch (error) {\n      console.error('Error loading patterns:', error);\n      setError('Error cargando patrones.');\n    }\n  };\n\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (error) {\n      console.error('Error loading shifts:', error);\n      setError('Error cargando turnos predefinidos');\n    }\n  };\n\n  const loadSuggestedShifts = async (date) => {\n    try {\n      const formattedDate = date.toISOString().split('T')[0];\n      const suggestions = await suggestShiftsForDate(formattedDate);\n      setSuggestedShifts(suggestions.suggestions || []);\n    } catch (error) {\n      console.error('Error loading suggested shifts:', error);\n      setSuggestedShifts([]);\n    }\n  };\n\n  const fetchDayDetails = async (date) => {\n    try {\n      const formattedDate = date.toISOString().split('T')[0];\n      const response = await getDayByDate(formattedDate);\n      if (response) {\n        setDayType(response.type || '');\n        setHours(response.hours || 0);\n        setDescription(response.description || '');\n        // Si el día tiene un turno asignado, mostrar esa información\n        if (response.shift) {\n          setSelectedShift(response.shift.id);\n          setUseShift(true);\n        } else {\n          setSelectedShift('');\n          setUseShift(false);\n        }\n      } else {\n        resetForm();\n      }\n    } catch (error) {\n      console.error('Error fetching day details:', error);\n      resetForm();\n    }\n  };\n\n  const resetForm = () => {\n    setDayType('');\n    setHours(0);\n    setDescription('');\n    setSelectedShift('');\n    setUseShift(false);\n    setError('');\n  };\n\n  const handleDateClick = (value) => {\n    setSelectedDay(value);\n    setShowModal(true);\n    setError('');\n  };\n\n  const handleShiftChange = (shiftId) => {\n    setSelectedShift(shiftId);\n    if (shiftId && availableShifts.length > 0) {\n      const shift = availableShifts.find(s => s.id === shiftId);\n      if (shift) {\n        setDayType('worked');\n        setHours(shift.totalHours);\n        setDescription(`${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`);\n      }\n    }\n  };\n\n  const handleSave = async () => {\n    if (!selectedDay) return;\n\n    const formattedDate = selectedDay.toISOString().split('T')[0];\n    setLoading(true);\n    setError('');\n\n    try {\n      if (useShift && selectedShift) {\n        // Usar turno predefinido\n        await applyShiftToDate(formattedDate, selectedShift, description);\n      } else {\n        // Usar entrada manual\n        await createOrUpdateDay(formattedDate, dayType, hours, description);\n      }\n      setShowModal(false);\n      resetForm();\n    } catch (error) {\n      console.error('Error saving day:', error);\n      setError('Error guardando los datos. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleApplyPattern = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      const formattedStartDate = patternStartDate.toISOString().split('T')[0];\n      const formattedEndDate = patternEndDate.toISOString().split('T')[0];\n      await applyPattern(formattedStartDate, formattedEndDate, selectedPatternId);\n      setShowPatternModal(false);\n      setSelectedPatternId(''); // Reset selected pattern\n      // Optionally, refresh calendar data here if needed\n      alert('Patrón aplicado exitosamente!');\n    } catch (error) {\n      console.error('Error applying pattern:', error);\n      setError('Error aplicando el patrón. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Container>\n      <Row className=\"justify-content-md-center\">\n        <Col md=\"auto\">\n          <h2>Calendario de Días Trabajados</h2>\n          <Calendar\n            onChange={setDate}\n            value={date}\n            onClickDay={handleDateClick}\n          />\n          <Button variant=\"info\" className=\"mt-3\" onClick={() => setShowPatternModal(true)}>\n            Aplicar Patrón\n          </Button>\n        </Col>\n      </Row>\n\n      <Modal show={showModal} onHide={() => setShowModal(false)}>\n        <Modal.Header closeButton>\n          <Modal.Title>Detalles del Día: {selectedDay && selectedDay.toDateString()}</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {error && (\n            <Alert variant=\"danger\" className=\"mb-3\">\n              {error}\n            </Alert>\n          )}\n\n          <Form>\n            {/* Selector de método de entrada */}\n            <Form.Group className=\"mb-3\">\n              <Form.Check\n                type=\"radio\"\n                id=\"manual-entry\"\n                name=\"entry-method\"\n                label=\"Entrada manual\"\n                checked={!useShift}\n                onChange={() => setUseShift(false)}\n              />\n              <Form.Check\n                type=\"radio\"\n                id=\"shift-entry\"\n                name=\"entry-method\"\n                label=\"Usar turno predefinido\"\n                checked={useShift}\n                onChange={() => setUseShift(true)}\n              />\n            </Form.Group>\n\n            {/* Sección de turnos predefinidos */}\n            {useShift && (\n              <>\n                {suggestedShifts.length > 0 && (\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Turnos sugeridos para esta fecha:</Form.Label>\n                    <div className=\"d-flex flex-wrap gap-2 mb-2\">\n                      {suggestedShifts.map((shift) => (\n                        <Badge\n                          key={shift.id}\n                          bg={selectedShift === shift.id ? \"primary\" : \"secondary\"}\n                          style={{ cursor: 'pointer' }}\n                          onClick={() => handleShiftChange(shift.id)}\n                        >\n                          {shift.name} ({shift.startTime} - {shift.endTime})\n                        </Badge>\n                      ))}\n                    </div>\n                  </Form.Group>\n                )}\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Seleccionar Turno</Form.Label>\n                  <Form.Select\n                    value={selectedShift}\n                    onChange={(e) => handleShiftChange(e.target.value)}\n                  >\n                    <option value=\"\">Seleccionar turno...</option>\n                    {availableShifts.map((shift) => (\n                      <option key={shift.id} value={shift.id}>\n                        {shift.name} - {shift.startTime} a {shift.endTime} ({shift.totalHours}h)\n                      </option>\n                    ))}\n                  </Form.Select>\n                </Form.Group>\n              </>\n            )}\n\n            {/* Sección de entrada manual */}\n            {!useShift && (\n              <>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Tipo de Día</Form.Label>\n                  <Form.Select value={dayType} onChange={(e) => setDayType(e.target.value)}>\n                    <option value=\"\">Seleccionar</option>\n                    <option value=\"worked\">Trabajado</option>\n                    <option value=\"holiday\">Vacaciones</option>\n                    <option value=\"permit\">Permiso</option>\n                    <option value=\"negative\">Cómputo Negativo</option>\n                  </Form.Select>\n                </Form.Group>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Horas (Cómputo Positivo/Negativo)</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    value={hours}\n                    onChange={(e) => setHours(parseFloat(e.target.value))}\n                    placeholder=\"Introduce horas\"\n                  />\n                </Form.Group>\n              </>\n            )}\n\n            {/* Descripción (común para ambos métodos) */}\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Descripción</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={3}\n                value={description}\n                onChange={(e) => setDescription(e.target.value)}\n                placeholder=\"Añade una descripción (opcional)\"\n                disabled={useShift && selectedShift && !description.includes('Personalizado:')}\n              />\n              {useShift && selectedShift && (\n                <Form.Text className=\"text-muted\">\n                  La descripción se genera automáticamente. Puedes editarla si es necesario.\n                </Form.Text>\n              )}\n            </Form.Group>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button\n            variant=\"secondary\"\n            onClick={() => {\n              setShowModal(false);\n              resetForm();\n            }}\n            disabled={loading}\n          >\n            Cerrar\n          </Button>\n          <Button\n            variant=\"primary\"\n            onClick={handleSave}\n            disabled={loading || (!useShift && !dayType) || (useShift && !selectedShift)}\n          >\n            {loading ? 'Guardando...' : 'Guardar Cambios'}\n          </Button>\n        </Modal.Footer>\n      </Modal>\n\n      {/* Modal para Aplicar Patrón */}\n      <Modal show={showPatternModal} onHide={() => setShowPatternModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>Aplicar Patrón de Días</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {error && (\n            <Alert variant=\"danger\" className=\"mb-3\">\n              {error}\n            </Alert>\n          )}\n          <Form>\n            <Row className=\"mb-3\">\n              <Col>\n                <Form.Group controlId=\"patternStartDate\">\n                  <Form.Label>Fecha de Inicio</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={patternStartDate.toISOString().split('T')[0]}\n                    onChange={(e) => setPatternStartDate(new Date(e.target.value))}\n                  />\n                </Form.Group>\n              </Col>\n              <Col>\n                <Form.Group controlId=\"patternEndDate\">\n                  <Form.Label>Fecha de Fin</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={patternEndDate.toISOString().split('T')[0]}\n                    onChange={(e) => setPatternEndDate(new Date(e.target.value))}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Seleccionar Patrón</Form.Label>\n              <Form.Select\n                value={selectedPatternId}\n                onChange={(e) => setSelectedPatternId(e.target.value)}\n              >\n                <option value=\"\">Seleccionar patrón...</option>\n                {allPatterns.map((pattern) => (\n                  <option key={pattern.id} value={pattern.id}>\n                    {pattern.name}\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button\n            variant=\"secondary\"\n            onClick={() => {\n              setShowPatternModal(false);\n              setError('');\n              setSelectedPatternId(''); // Reset selected pattern\n            }}\n          >\n            Cerrar\n          </Button>\n          <Button\n            variant=\"primary\"\n            onClick={handleApplyPattern}\n            disabled={loading || !selectedPatternId}\n          >\n            {loading ? 'Aplicando...' : 'Aplicar Patrón'}\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n}\n\nexport default CalendarPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAO,kCAAkC;AACzC,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AACxF,SACEC,YAAY,EACZC,iBAAiB,EACjBC,YAAY,EACZC,gBAAgB,EAChBC,oBAAoB,EACpBC,YAAY,EACZC,cAAc,QACT,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGzB,QAAQ,CAAC,IAAI0B,IAAI,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiC,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAM,CAACiD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACmD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,IAAI0B,IAAI,CAAC,CAAC,CAAC;EACpE,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,IAAI0B,IAAI,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACpD,MAAM,CAACyD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEhEC,SAAS,CAAC,MAAM;IACd;IACA0D,mBAAmB,CAAC,CAAC;IACrBC,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN3D,SAAS,CAAC,MAAM;IACd,IAAI4B,WAAW,EAAE;MACfgC,eAAe,CAAChC,WAAW,CAAC;MAC5BiC,mBAAmB,CAACjC,WAAW,CAAC;IAClC;EACF,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjB,MAAM+B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAM9C,cAAc,CAAC,CAAC;MACvCuC,cAAc,CAACO,QAAQ,CAAC;IAC1B,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,0BAA0B,CAAC;IACtC;EACF,CAAC;EAED,MAAMW,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMM,MAAM,GAAG,MAAMpD,YAAY,CAAC,CAAC;MACnCyB,kBAAkB,CAAC2B,MAAM,CAAC;IAC5B,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,oCAAoC,CAAC;IAChD;EACF,CAAC;EAED,MAAMc,mBAAmB,GAAG,MAAOtC,IAAI,IAAK;IAC1C,IAAI;MACF,MAAM0C,aAAa,GAAG1C,IAAI,CAAC2C,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACtD,MAAMC,WAAW,GAAG,MAAMtD,oBAAoB,CAACmD,aAAa,CAAC;MAC7DxB,kBAAkB,CAAC2B,WAAW,CAACA,WAAW,IAAI,EAAE,CAAC;IACnD,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDL,kBAAkB,CAAC,EAAE,CAAC;IACxB;EACF,CAAC;EAED,MAAMmB,eAAe,GAAG,MAAOrC,IAAI,IAAK;IACtC,IAAI;MACF,MAAM0C,aAAa,GAAG1C,IAAI,CAAC2C,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACtD,MAAME,QAAQ,GAAG,MAAM3D,YAAY,CAACuD,aAAa,CAAC;MAClD,IAAII,QAAQ,EAAE;QACZtC,UAAU,CAACsC,QAAQ,CAACC,IAAI,IAAI,EAAE,CAAC;QAC/BrC,QAAQ,CAACoC,QAAQ,CAACrC,KAAK,IAAI,CAAC,CAAC;QAC7BG,cAAc,CAACkC,QAAQ,CAACnC,WAAW,IAAI,EAAE,CAAC;QAC1C;QACA,IAAImC,QAAQ,CAACE,KAAK,EAAE;UAClBhC,gBAAgB,CAAC8B,QAAQ,CAACE,KAAK,CAACC,EAAE,CAAC;UACnC7B,WAAW,CAAC,IAAI,CAAC;QACnB,CAAC,MAAM;UACLJ,gBAAgB,CAAC,EAAE,CAAC;UACpBI,WAAW,CAAC,KAAK,CAAC;QACpB;MACF,CAAC,MAAM;QACL8B,SAAS,CAAC,CAAC;MACb;IACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD2B,SAAS,CAAC,CAAC;IACb;EACF,CAAC;EAED,MAAMA,SAAS,GAAGA,CAAA,KAAM;IACtB1C,UAAU,CAAC,EAAE,CAAC;IACdE,QAAQ,CAAC,CAAC,CAAC;IACXE,cAAc,CAAC,EAAE,CAAC;IAClBI,gBAAgB,CAAC,EAAE,CAAC;IACpBI,WAAW,CAAC,KAAK,CAAC;IAClBI,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAM2B,eAAe,GAAIC,KAAK,IAAK;IACjC9C,cAAc,CAAC8C,KAAK,CAAC;IACrBhD,YAAY,CAAC,IAAI,CAAC;IAClBoB,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAM6B,iBAAiB,GAAIC,OAAO,IAAK;IACrCtC,gBAAgB,CAACsC,OAAO,CAAC;IACzB,IAAIA,OAAO,IAAIzC,eAAe,CAAC0C,MAAM,GAAG,CAAC,EAAE;MACzC,MAAMP,KAAK,GAAGnC,eAAe,CAAC2C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACR,EAAE,KAAKK,OAAO,CAAC;MACzD,IAAIN,KAAK,EAAE;QACTxC,UAAU,CAAC,QAAQ,CAAC;QACpBE,QAAQ,CAACsC,KAAK,CAACU,UAAU,CAAC;QAC1B9C,cAAc,CAAC,GAAGoC,KAAK,CAACW,IAAI,KAAKX,KAAK,CAACY,SAAS,MAAMZ,KAAK,CAACa,OAAO,KAAKb,KAAK,CAACc,YAAY,eAAe,CAAC;MAC5G;IACF;EACF,CAAC;EAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAAC1D,WAAW,EAAE;IAElB,MAAMqC,aAAa,GAAGrC,WAAW,CAACsC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7DtB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,IAAIL,QAAQ,IAAIJ,aAAa,EAAE;QAC7B;QACA,MAAMzB,gBAAgB,CAACoD,aAAa,EAAE3B,aAAa,EAAEJ,WAAW,CAAC;MACnE,CAAC,MAAM;QACL;QACA,MAAMvB,iBAAiB,CAACsD,aAAa,EAAEnC,OAAO,EAAEE,KAAK,EAAEE,WAAW,CAAC;MACrE;MACAP,YAAY,CAAC,KAAK,CAAC;MACnB8C,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCC,QAAQ,CAAC,2DAA2D,CAAC;IACvE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0C,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC1C,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,MAAMyC,kBAAkB,GAAGtC,gBAAgB,CAACgB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACvE,MAAMsB,gBAAgB,GAAGrC,cAAc,CAACc,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACnE,MAAMpD,YAAY,CAACyE,kBAAkB,EAAEC,gBAAgB,EAAEjC,iBAAiB,CAAC;MAC3EP,mBAAmB,CAAC,KAAK,CAAC;MAC1BQ,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1B;MACAiC,KAAK,CAAC,+BAA+B,CAAC;IACxC,CAAC,CAAC,OAAO5C,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,2DAA2D,CAAC;IACvE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACE3B,OAAA,CAAChB,SAAS;IAAAyF,QAAA,gBACRzE,OAAA,CAACf,GAAG;MAACyF,SAAS,EAAC,2BAA2B;MAAAD,QAAA,eACxCzE,OAAA,CAACd,GAAG;QAACyF,EAAE,EAAC,MAAM;QAAAF,QAAA,gBACZzE,OAAA;UAAAyE,QAAA,EAAI;QAA6B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtC/E,OAAA,CAACjB,QAAQ;UACPiG,QAAQ,EAAE1E,OAAQ;UAClBmD,KAAK,EAAEpD,IAAK;UACZ4E,UAAU,EAAEzB;QAAgB;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACF/E,OAAA,CAACb,MAAM;UAAC+F,OAAO,EAAC,MAAM;UAACR,SAAS,EAAC,MAAM;UAACS,OAAO,EAAEA,CAAA,KAAMpD,mBAAmB,CAAC,IAAI,CAAE;UAAA0C,QAAA,EAAC;QAElF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN/E,OAAA,CAACZ,KAAK;MAACgG,IAAI,EAAE5E,SAAU;MAAC6E,MAAM,EAAEA,CAAA,KAAM5E,YAAY,CAAC,KAAK,CAAE;MAAAgE,QAAA,gBACxDzE,OAAA,CAACZ,KAAK,CAACkG,MAAM;QAACC,WAAW;QAAAd,QAAA,eACvBzE,OAAA,CAACZ,KAAK,CAACoG,KAAK;UAAAf,QAAA,GAAC,uBAAkB,EAAC/D,WAAW,IAAIA,WAAW,CAAC+E,YAAY,CAAC,CAAC;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,eACf/E,OAAA,CAACZ,KAAK,CAACsG,IAAI;QAAAjB,QAAA,GACR7C,KAAK,iBACJ5B,OAAA,CAACV,KAAK;UAAC4F,OAAO,EAAC,QAAQ;UAACR,SAAS,EAAC,MAAM;UAAAD,QAAA,EACrC7C;QAAK;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eAED/E,OAAA,CAACX,IAAI;UAAAoF,QAAA,gBAEHzE,OAAA,CAACX,IAAI,CAACsG,KAAK;YAACjB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BzE,OAAA,CAACX,IAAI,CAACuG,KAAK;cACTxC,IAAI,EAAC,OAAO;cACZE,EAAE,EAAC,cAAc;cACjBU,IAAI,EAAC,cAAc;cACnB6B,KAAK,EAAC,gBAAgB;cACtBC,OAAO,EAAE,CAACtE,QAAS;cACnBwD,QAAQ,EAAEA,CAAA,KAAMvD,WAAW,CAAC,KAAK;YAAE;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACF/E,OAAA,CAACX,IAAI,CAACuG,KAAK;cACTxC,IAAI,EAAC,OAAO;cACZE,EAAE,EAAC,aAAa;cAChBU,IAAI,EAAC,cAAc;cACnB6B,KAAK,EAAC,wBAAwB;cAC9BC,OAAO,EAAEtE,QAAS;cAClBwD,QAAQ,EAAEA,CAAA,KAAMvD,WAAW,CAAC,IAAI;YAAE;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,EAGZvD,QAAQ,iBACPxB,OAAA,CAAAE,SAAA;YAAAuE,QAAA,GACGnD,eAAe,CAACsC,MAAM,GAAG,CAAC,iBACzB5D,OAAA,CAACX,IAAI,CAACsG,KAAK;cAACjB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BzE,OAAA,CAACX,IAAI,CAAC0G,KAAK;gBAAAtB,QAAA,EAAC;cAAiC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1D/E,OAAA;gBAAK0E,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,EACzCnD,eAAe,CAAC0E,GAAG,CAAE3C,KAAK,iBACzBrD,OAAA,CAACT,KAAK;kBAEJ0G,EAAE,EAAE7E,aAAa,KAAKiC,KAAK,CAACC,EAAE,GAAG,SAAS,GAAG,WAAY;kBACzD4C,KAAK,EAAE;oBAAEC,MAAM,EAAE;kBAAU,CAAE;kBAC7BhB,OAAO,EAAEA,CAAA,KAAMzB,iBAAiB,CAACL,KAAK,CAACC,EAAE,CAAE;kBAAAmB,QAAA,GAE1CpB,KAAK,CAACW,IAAI,EAAC,IAAE,EAACX,KAAK,CAACY,SAAS,EAAC,KAAG,EAACZ,KAAK,CAACa,OAAO,EAAC,GACnD;gBAAA,GANOb,KAAK,CAACC,EAAE;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMR,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAED/E,OAAA,CAACX,IAAI,CAACsG,KAAK;cAACjB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BzE,OAAA,CAACX,IAAI,CAAC0G,KAAK;gBAAAtB,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1C/E,OAAA,CAACX,IAAI,CAAC+G,MAAM;gBACV3C,KAAK,EAAErC,aAAc;gBACrB4D,QAAQ,EAAGqB,CAAC,IAAK3C,iBAAiB,CAAC2C,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAE;gBAAAgB,QAAA,gBAEnDzE,OAAA;kBAAQyD,KAAK,EAAC,EAAE;kBAAAgB,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC7C7D,eAAe,CAAC8E,GAAG,CAAE3C,KAAK,iBACzBrD,OAAA;kBAAuByD,KAAK,EAAEJ,KAAK,CAACC,EAAG;kBAAAmB,QAAA,GACpCpB,KAAK,CAACW,IAAI,EAAC,KAAG,EAACX,KAAK,CAACY,SAAS,EAAC,KAAG,EAACZ,KAAK,CAACa,OAAO,EAAC,IAAE,EAACb,KAAK,CAACU,UAAU,EAAC,IACxE;gBAAA,GAFaV,KAAK,CAACC,EAAE;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,eACb,CACH,EAGA,CAACvD,QAAQ,iBACRxB,OAAA,CAAAE,SAAA;YAAAuE,QAAA,gBACEzE,OAAA,CAACX,IAAI,CAACsG,KAAK;cAACjB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BzE,OAAA,CAACX,IAAI,CAAC0G,KAAK;gBAAAtB,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpC/E,OAAA,CAACX,IAAI,CAAC+G,MAAM;gBAAC3C,KAAK,EAAE7C,OAAQ;gBAACoE,QAAQ,EAAGqB,CAAC,IAAKxF,UAAU,CAACwF,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAE;gBAAAgB,QAAA,gBACvEzE,OAAA;kBAAQyD,KAAK,EAAC,EAAE;kBAAAgB,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrC/E,OAAA;kBAAQyD,KAAK,EAAC,QAAQ;kBAAAgB,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzC/E,OAAA;kBAAQyD,KAAK,EAAC,SAAS;kBAAAgB,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3C/E,OAAA;kBAAQyD,KAAK,EAAC,QAAQ;kBAAAgB,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvC/E,OAAA;kBAAQyD,KAAK,EAAC,UAAU;kBAAAgB,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACb/E,OAAA,CAACX,IAAI,CAACsG,KAAK;cAACjB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BzE,OAAA,CAACX,IAAI,CAAC0G,KAAK;gBAAAtB,QAAA,EAAC;cAAiC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1D/E,OAAA,CAACX,IAAI,CAACkH,OAAO;gBACXnD,IAAI,EAAC,QAAQ;gBACbK,KAAK,EAAE3C,KAAM;gBACbkE,QAAQ,EAAGqB,CAAC,IAAKtF,QAAQ,CAACyF,UAAU,CAACH,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAC,CAAE;gBACtDgD,WAAW,EAAC;cAAiB;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA,eACb,CACH,eAGD/E,OAAA,CAACX,IAAI,CAACsG,KAAK;YAACjB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BzE,OAAA,CAACX,IAAI,CAAC0G,KAAK;cAAAtB,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpC/E,OAAA,CAACX,IAAI,CAACkH,OAAO;cACXG,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRlD,KAAK,EAAEzC,WAAY;cACnBgE,QAAQ,EAAGqB,CAAC,IAAKpF,cAAc,CAACoF,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAE;cAChDgD,WAAW,EAAC,wCAAkC;cAC9CG,QAAQ,EAAEpF,QAAQ,IAAIJ,aAAa,IAAI,CAACJ,WAAW,CAAC6F,QAAQ,CAAC,gBAAgB;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,EACDvD,QAAQ,IAAIJ,aAAa,iBACxBpB,OAAA,CAACX,IAAI,CAACyH,IAAI;cAACpC,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAElC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CACZ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACb/E,OAAA,CAACZ,KAAK,CAAC2H,MAAM;QAAAtC,QAAA,gBACXzE,OAAA,CAACb,MAAM;UACL+F,OAAO,EAAC,WAAW;UACnBC,OAAO,EAAEA,CAAA,KAAM;YACb1E,YAAY,CAAC,KAAK,CAAC;YACnB8C,SAAS,CAAC,CAAC;UACb,CAAE;UACFqD,QAAQ,EAAElF,OAAQ;UAAA+C,QAAA,EACnB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/E,OAAA,CAACb,MAAM;UACL+F,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAEf,UAAW;UACpBwC,QAAQ,EAAElF,OAAO,IAAK,CAACF,QAAQ,IAAI,CAACZ,OAAQ,IAAKY,QAAQ,IAAI,CAACJ,aAAe;UAAAqD,QAAA,EAE5E/C,OAAO,GAAG,cAAc,GAAG;QAAiB;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGR/E,OAAA,CAACZ,KAAK;MAACgG,IAAI,EAAEtD,gBAAiB;MAACuD,MAAM,EAAEA,CAAA,KAAMtD,mBAAmB,CAAC,KAAK,CAAE;MAACiF,IAAI,EAAC,IAAI;MAAAvC,QAAA,gBAChFzE,OAAA,CAACZ,KAAK,CAACkG,MAAM;QAACC,WAAW;QAAAd,QAAA,eACvBzE,OAAA,CAACZ,KAAK,CAACoG,KAAK;UAAAf,QAAA,EAAC;QAAsB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACf/E,OAAA,CAACZ,KAAK,CAACsG,IAAI;QAAAjB,QAAA,GACR7C,KAAK,iBACJ5B,OAAA,CAACV,KAAK;UAAC4F,OAAO,EAAC,QAAQ;UAACR,SAAS,EAAC,MAAM;UAAAD,QAAA,EACrC7C;QAAK;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eACD/E,OAAA,CAACX,IAAI;UAAAoF,QAAA,gBACHzE,OAAA,CAACf,GAAG;YAACyF,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBzE,OAAA,CAACd,GAAG;cAAAuF,QAAA,eACFzE,OAAA,CAACX,IAAI,CAACsG,KAAK;gBAACsB,SAAS,EAAC,kBAAkB;gBAAAxC,QAAA,gBACtCzE,OAAA,CAACX,IAAI,CAAC0G,KAAK;kBAAAtB,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxC/E,OAAA,CAACX,IAAI,CAACkH,OAAO;kBACXnD,IAAI,EAAC,MAAM;kBACXK,KAAK,EAAEzB,gBAAgB,CAACgB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;kBACpD+B,QAAQ,EAAGqB,CAAC,IAAKpE,mBAAmB,CAAC,IAAI1B,IAAI,CAAC8F,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAC;gBAAE;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN/E,OAAA,CAACd,GAAG;cAAAuF,QAAA,eACFzE,OAAA,CAACX,IAAI,CAACsG,KAAK;gBAACsB,SAAS,EAAC,gBAAgB;gBAAAxC,QAAA,gBACpCzE,OAAA,CAACX,IAAI,CAAC0G,KAAK;kBAAAtB,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrC/E,OAAA,CAACX,IAAI,CAACkH,OAAO;kBACXnD,IAAI,EAAC,MAAM;kBACXK,KAAK,EAAEvB,cAAc,CAACc,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;kBAClD+B,QAAQ,EAAGqB,CAAC,IAAKlE,iBAAiB,CAAC,IAAI5B,IAAI,CAAC8F,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAC;gBAAE;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/E,OAAA,CAACX,IAAI,CAACsG,KAAK;YAACjB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BzE,OAAA,CAACX,IAAI,CAAC0G,KAAK;cAAAtB,QAAA,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3C/E,OAAA,CAACX,IAAI,CAAC+G,MAAM;cACV3C,KAAK,EAAEnB,iBAAkB;cACzB0C,QAAQ,EAAGqB,CAAC,IAAK9D,oBAAoB,CAAC8D,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAE;cAAAgB,QAAA,gBAEtDzE,OAAA;gBAAQyD,KAAK,EAAC,EAAE;gBAAAgB,QAAA,EAAC;cAAqB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9C3C,WAAW,CAAC4D,GAAG,CAAEkB,OAAO,iBACvBlH,OAAA;gBAAyByD,KAAK,EAAEyD,OAAO,CAAC5D,EAAG;gBAAAmB,QAAA,EACxCyC,OAAO,CAAClD;cAAI,GADFkD,OAAO,CAAC5D,EAAE;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACb/E,OAAA,CAACZ,KAAK,CAAC2H,MAAM;QAAAtC,QAAA,gBACXzE,OAAA,CAACb,MAAM;UACL+F,OAAO,EAAC,WAAW;UACnBC,OAAO,EAAEA,CAAA,KAAM;YACbpD,mBAAmB,CAAC,KAAK,CAAC;YAC1BF,QAAQ,CAAC,EAAE,CAAC;YACZU,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;UAC5B,CAAE;UAAAkC,QAAA,EACH;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/E,OAAA,CAACb,MAAM;UACL+F,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAEd,kBAAmB;UAC5BuC,QAAQ,EAAElF,OAAO,IAAI,CAACY,iBAAkB;UAAAmC,QAAA,EAEvC/C,OAAO,GAAG,cAAc,GAAG;QAAgB;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB;AAAC3E,EAAA,CAnYQD,YAAY;AAAAgH,EAAA,GAAZhH,YAAY;AAqYrB,eAAeA,YAAY;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}