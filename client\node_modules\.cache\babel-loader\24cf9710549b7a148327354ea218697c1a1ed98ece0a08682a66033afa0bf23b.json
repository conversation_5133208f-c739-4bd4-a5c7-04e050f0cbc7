{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\components\\\\RealCalendarView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Button, Alert, Table, Badge, Modal, Form } from 'react-bootstrap';\nimport { getAllRealDays, createOrUpdateRealDay, deleteRealDay, getAllShifts } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction RealCalendarView({\n  onDateSelect,\n  selectedDate,\n  onCalendarUpdate\n}) {\n  _s();\n  const [realDays, setRealDays] = useState([]);\n  const [shifts, setShifts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showModal, setShowModal] = useState(false);\n  const [editingDay, setEditingDay] = useState(null);\n\n  // Form state\n  const [formData, setFormData] = useState({\n    date: '',\n    shiftId: '',\n    customHours: '',\n    entryTime: '',\n    exitTime: '',\n    description: ''\n  });\n  useEffect(() => {\n    loadRealDays();\n    loadShifts();\n  }, []);\n  const loadRealDays = async () => {\n    try {\n      setLoading(true);\n      const days = await getAllRealDays();\n      setRealDays(days);\n      setError('');\n    } catch (err) {\n      console.error('Error loading real days:', err);\n      setError('Error cargando días reales: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadShifts = async () => {\n    try {\n      const shiftsData = await getAllShifts();\n      setShifts(shiftsData);\n    } catch (err) {\n      console.error('Error loading shifts:', err);\n    }\n  };\n  const handleAddDay = () => {\n    setEditingDay(null);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      shiftId: '',\n      customHours: '',\n      entryTime: '',\n      exitTime: '',\n      description: ''\n    });\n    setShowModal(true);\n  };\n  const handleEditDay = day => {\n    setEditingDay(day);\n    setFormData({\n      date: day.date,\n      shiftId: day.shiftId || '',\n      customHours: day.hours || '',\n      entryTime: day.entryTime || '',\n      exitTime: day.exitTime || '',\n      description: day.description || ''\n    });\n    setShowModal(true);\n  };\n  const handleDeleteDay = async dayDate => {\n    if (window.confirm('¿Estás seguro de que quieres eliminar este día trabajado?')) {\n      try {\n        setLoading(true);\n        await deleteRealDay(dayDate);\n        await loadRealDays();\n        if (onCalendarUpdate) onCalendarUpdate();\n      } catch (err) {\n        setError('Error eliminando día: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  const getTotalHours = () => {\n    return realDays.reduce((total, day) => total + (day.hours || 0), 0);\n  };\n  const getCurrentMonthHours = () => {\n    const currentMonth = new Date().getMonth();\n    const currentYear = new Date().getFullYear();\n    return realDays.filter(day => {\n      const dayDate = new Date(day.date);\n      return dayDate.getMonth() === currentMonth && dayDate.getFullYear() === currentYear;\n    }).reduce((total, day) => total + (day.hours || 0), 0);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n\n      // Calcular horas automáticamente si se proporcionan horas de entrada y salida\n      let hours = parseFloat(formData.customHours) || 0;\n      if (formData.entryTime && formData.exitTime && !formData.customHours) {\n        const entry = new Date(`2000-01-01T${formData.entryTime}`);\n        const exit = new Date(`2000-01-01T${formData.exitTime}`);\n        hours = (exit - entry) / (1000 * 60 * 60); // Convertir a horas\n      }\n      const dayData = {\n        date: formData.date,\n        shiftId: formData.shiftId || null,\n        hours: hours,\n        entryTime: formData.entryTime || null,\n        exitTime: formData.exitTime || null,\n        description: formData.description || null\n      };\n      if (editingDay) {\n        dayData.id = editingDay.id;\n      }\n      await createOrUpdateRealDay(dayData);\n      await loadRealDays();\n      if (onCalendarUpdate) onCalendarUpdate();\n      setShowModal(false);\n      setError('');\n    } catch (err) {\n      setError('Error guardando día: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getShiftName = shiftId => {\n    const shift = shifts.find(s => s.id === shiftId);\n    return shift ? shift.name : 'Personalizado';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Calendario de Trabajo Real\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"success\",\n              onClick: handleAddDay,\n              disabled: loading,\n              children: \"\\u2795 Registrar D\\xEDa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          onClose: () => setError(''),\n          dismissible: true,\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"calendar-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"D\\xEDas Trabajados Registrados\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), loading ? /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Cargando d\\xEDas reales...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this) : realDays.length > 0 ? /*#__PURE__*/_jsxDEV(Table, {\n            striped: true,\n            bordered: true,\n            hover: true,\n            responsive: true,\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Fecha\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Turno\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Horas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Entrada\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Salida\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Descripci\\xF3n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Acciones\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: realDays.sort((a, b) => new Date(b.date) - new Date(a.date)).map(day => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: new Date(day.date).toLocaleDateString('es-ES', {\n                    weekday: 'short',\n                    day: '2-digit',\n                    month: '2-digit',\n                    year: 'numeric'\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(Badge, {\n                    bg: \"secondary\",\n                    children: getShiftName(day.shiftId)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [day.hours, \"h\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: day.entryTime || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: day.exitTime || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: day.description || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-primary\",\n                    size: \"sm\",\n                    onClick: () => handleEditDay(day),\n                    className: \"me-1\",\n                    children: \"\\u270F\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-danger\",\n                    size: \"sm\",\n                    onClick: () => handleDeleteDay(day.id),\n                    children: \"\\uD83D\\uDDD1\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 25\n                }, this)]\n              }, day.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"info\",\n            children: \"No hay d\\xEDas trabajados registrados. \\xA1Haz clic en \\\"Registrar D\\xEDa\\\" para empezar!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"calendar-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Estad\\xEDsticas Reales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total de d\\xEDas registrados:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this), \" \", realDays.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total de horas:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), \" \", getTotalHours().toFixed(1), \"h\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Horas este mes:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), \" \", getCurrentMonthHours().toFixed(1), \"h\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Promedio por d\\xEDa:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this), \" \", realDays.length > 0 ? (getTotalHours() / realDays.length).toFixed(1) : 0, \"h\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: editingDay ? 'Editar Día Trabajado' : 'Registrar Nuevo Día'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          onSubmit: e => e.preventDefault(),\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: formData.date,\n                  onChange: e => setFormData({\n                    ...formData,\n                    date: e.target.value\n                  }),\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Turno\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: formData.shiftId,\n                  onChange: e => setFormData({\n                    ...formData,\n                    shiftId: e.target.value\n                  }),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Personalizado\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 21\n                  }, this), shifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: shift.id,\n                    children: [shift.name, \" (\", shift.hours, \"h)\"]\n                  }, shift.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Horas Trabajadas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  step: \"0.5\",\n                  min: \"0\",\n                  max: \"24\",\n                  value: formData.customHours,\n                  onChange: e => setFormData({\n                    ...formData,\n                    customHours: e.target.value\n                  }),\n                  placeholder: \"Ej: 8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                  className: \"text-muted\",\n                  children: \"Se calcular\\xE1 autom\\xE1ticamente si defines entrada y salida\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Hora de Entrada\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"time\",\n                  value: formData.entryTime,\n                  onChange: e => setFormData({\n                    ...formData,\n                    entryTime: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Hora de Salida\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"time\",\n                  value: formData.exitTime,\n                  onChange: e => setFormData({\n                    ...formData,\n                    exitTime: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Descripci\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              value: formData.description,\n              onChange: e => setFormData({\n                ...formData,\n                description: e.target.value\n              }),\n              placeholder: \"Notas adicionales sobre este d\\xEDa trabajado...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowModal(false),\n          children: \"Cancelar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSubmit,\n          disabled: loading || !formData.date,\n          children: loading ? 'Guardando...' : editingDay ? 'Actualizar' : 'Registrar'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 5\n  }, this);\n}\n_s(RealCalendarView, \"W55oOhFk1CVeDbpp3EG/qT87LPw=\");\n_c = RealCalendarView;\nexport default RealCalendarView;\nvar _c;\n$RefreshReg$(_c, \"RealCalendarView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "<PERSON><PERSON>", "<PERSON><PERSON>", "Table", "Badge", "Modal", "Form", "getAllRealDays", "createOrUpdateRealDay", "deleteRealDay", "getAllShifts", "jsxDEV", "_jsxDEV", "RealCalendarView", "onDateSelect", "selectedDate", "onCalendarUpdate", "_s", "realDays", "setRealDays", "shifts", "setShifts", "loading", "setLoading", "error", "setError", "showModal", "setShowModal", "editingDay", "setEditingDay", "formData", "setFormData", "date", "shiftId", "customHours", "entryTime", "exitTime", "description", "loadRealDays", "loadShifts", "days", "err", "console", "message", "shiftsData", "handleAddDay", "Date", "toISOString", "split", "handleEditDay", "day", "hours", "handleDeleteDay", "dayDate", "window", "confirm", "getTotalHours", "reduce", "total", "getCurrentMonthHours", "currentMonth", "getMonth", "currentYear", "getFullYear", "filter", "handleSubmit", "e", "preventDefault", "parseFloat", "entry", "exit", "dayData", "id", "getShiftName", "shift", "find", "s", "name", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "disabled", "onClose", "dismissible", "md", "length", "striped", "bordered", "hover", "responsive", "sort", "a", "b", "map", "toLocaleDateString", "weekday", "month", "year", "bg", "size", "toFixed", "show", "onHide", "Header", "closeButton", "Title", "Body", "onSubmit", "Group", "Label", "Control", "type", "value", "onChange", "target", "required", "Select", "step", "min", "max", "placeholder", "Text", "as", "rows", "Footer", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/components/RealCalendarView.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Button, Alert, Table, Badge, Modal, Form } from 'react-bootstrap';\nimport { getAllRealDays, createOrUpdateRealDay, deleteRealDay, getAllShifts } from '../services/api';\n\nfunction RealCalendarView({ onDateSelect, selectedDate, onCalendarUpdate }) {\n  const [realDays, setRealDays] = useState([]);\n  const [shifts, setShifts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showModal, setShowModal] = useState(false);\n  const [editingDay, setEditingDay] = useState(null);\n\n  // Form state\n  const [formData, setFormData] = useState({\n    date: '',\n    shiftId: '',\n    customHours: '',\n    entryTime: '',\n    exitTime: '',\n    description: ''\n  });\n\n  useEffect(() => {\n    loadRealDays();\n    loadShifts();\n  }, []);\n\n  const loadRealDays = async () => {\n    try {\n      setLoading(true);\n      const days = await getAllRealDays();\n      setRealDays(days);\n      setError('');\n    } catch (err) {\n      console.error('Error loading real days:', err);\n      setError('Error cargando días reales: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadShifts = async () => {\n    try {\n      const shiftsData = await getAllShifts();\n      setShifts(shiftsData);\n    } catch (err) {\n      console.error('Error loading shifts:', err);\n    }\n  };\n\n  const handleAddDay = () => {\n    setEditingDay(null);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      shiftId: '',\n      customHours: '',\n      entryTime: '',\n      exitTime: '',\n      description: ''\n    });\n    setShowModal(true);\n  };\n\n  const handleEditDay = (day) => {\n    setEditingDay(day);\n    setFormData({\n      date: day.date,\n      shiftId: day.shiftId || '',\n      customHours: day.hours || '',\n      entryTime: day.entryTime || '',\n      exitTime: day.exitTime || '',\n      description: day.description || ''\n    });\n    setShowModal(true);\n  };\n\n  const handleDeleteDay = async (dayDate) => {\n    if (window.confirm('¿Estás seguro de que quieres eliminar este día trabajado?')) {\n      try {\n        setLoading(true);\n        await deleteRealDay(dayDate);\n        await loadRealDays();\n        if (onCalendarUpdate) onCalendarUpdate();\n      } catch (err) {\n        setError('Error eliminando día: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  const getTotalHours = () => {\n    return realDays.reduce((total, day) => total + (day.hours || 0), 0);\n  };\n\n  const getCurrentMonthHours = () => {\n    const currentMonth = new Date().getMonth();\n    const currentYear = new Date().getFullYear();\n\n    return realDays\n      .filter(day => {\n        const dayDate = new Date(day.date);\n        return dayDate.getMonth() === currentMonth && dayDate.getFullYear() === currentYear;\n      })\n      .reduce((total, day) => total + (day.hours || 0), 0);\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    try {\n      setLoading(true);\n\n      // Calcular horas automáticamente si se proporcionan horas de entrada y salida\n      let hours = parseFloat(formData.customHours) || 0;\n      if (formData.entryTime && formData.exitTime && !formData.customHours) {\n        const entry = new Date(`2000-01-01T${formData.entryTime}`);\n        const exit = new Date(`2000-01-01T${formData.exitTime}`);\n        hours = (exit - entry) / (1000 * 60 * 60); // Convertir a horas\n      }\n\n      const dayData = {\n        date: formData.date,\n        shiftId: formData.shiftId || null,\n        hours: hours,\n        entryTime: formData.entryTime || null,\n        exitTime: formData.exitTime || null,\n        description: formData.description || null\n      };\n\n      if (editingDay) {\n        dayData.id = editingDay.id;\n      }\n\n      await createOrUpdateRealDay(dayData);\n      await loadRealDays();\n      if (onCalendarUpdate) onCalendarUpdate();\n      setShowModal(false);\n      setError('');\n    } catch (err) {\n      setError('Error guardando día: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getShiftName = (shiftId) => {\n    const shift = shifts.find(s => s.id === shiftId);\n    return shift ? shift.name : 'Personalizado';\n  };\n\n  return (\n    <div>\n      <Row className=\"mb-3\">\n        <Col>\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <h4>Calendario de Trabajo Real</h4>\n            <div>\n              <Button\n                variant=\"success\"\n                onClick={handleAddDay}\n                disabled={loading}\n              >\n                ➕ Registrar Día\n              </Button>\n            </div>\n          </div>\n        </Col>\n      </Row>\n\n      {error && (\n        <Row className=\"mb-3\">\n          <Col>\n            <Alert variant=\"danger\" onClose={() => setError('')} dismissible>\n              {error}\n            </Alert>\n          </Col>\n        </Row>\n      )}\n\n      <Row className=\"mb-3\">\n        <Col md={8}>\n          <div className=\"calendar-container\">\n            <h5>Días Trabajados Registrados</h5>\n            {loading ? (\n              <p>Cargando días reales...</p>\n            ) : realDays.length > 0 ? (\n              <Table striped bordered hover responsive>\n                <thead>\n                  <tr>\n                    <th>Fecha</th>\n                    <th>Turno</th>\n                    <th>Horas</th>\n                    <th>Entrada</th>\n                    <th>Salida</th>\n                    <th>Descripción</th>\n                    <th>Acciones</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {realDays\n                    .sort((a, b) => new Date(b.date) - new Date(a.date))\n                    .map(day => (\n                      <tr key={day.id}>\n                        <td>\n                          {new Date(day.date).toLocaleDateString('es-ES', {\n                            weekday: 'short',\n                            day: '2-digit',\n                            month: '2-digit',\n                            year: 'numeric'\n                          })}\n                        </td>\n                        <td>\n                          <Badge bg=\"secondary\">\n                            {getShiftName(day.shiftId)}\n                          </Badge>\n                        </td>\n                        <td><strong>{day.hours}h</strong></td>\n                        <td>{day.entryTime || '-'}</td>\n                        <td>{day.exitTime || '-'}</td>\n                        <td>{day.description || '-'}</td>\n                        <td>\n                          <Button\n                            variant=\"outline-primary\"\n                            size=\"sm\"\n                            onClick={() => handleEditDay(day)}\n                            className=\"me-1\"\n                          >\n                            ✏️\n                          </Button>\n                          <Button\n                            variant=\"outline-danger\"\n                            size=\"sm\"\n                            onClick={() => handleDeleteDay(day.id)}\n                          >\n                            🗑️\n                          </Button>\n                        </td>\n                      </tr>\n                    ))}\n                </tbody>\n              </Table>\n            ) : (\n              <Alert variant=\"info\">\n                No hay días trabajados registrados. ¡Haz clic en \"Registrar Día\" para empezar!\n              </Alert>\n            )}\n          </div>\n        </Col>\n        <Col md={4}>\n          <div className=\"calendar-stats\">\n            <h5>Estadísticas Reales</h5>\n            <div className=\"stat-item\">\n              <strong>Total de días registrados:</strong> {realDays.length}\n            </div>\n            <div className=\"stat-item\">\n              <strong>Total de horas:</strong> {getTotalHours().toFixed(1)}h\n            </div>\n            <div className=\"stat-item\">\n              <strong>Horas este mes:</strong> {getCurrentMonthHours().toFixed(1)}h\n            </div>\n            <div className=\"stat-item\">\n              <strong>Promedio por día:</strong> {realDays.length > 0 ? (getTotalHours() / realDays.length).toFixed(1) : 0}h\n            </div>\n          </div>\n        </Col>\n      </Row>\n\n      {/* Modal para registrar/editar días */}\n      <Modal show={showModal} onHide={() => setShowModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            {editingDay ? 'Editar Día Trabajado' : 'Registrar Nuevo Día'}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <Form onSubmit={(e) => e.preventDefault()}>\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Fecha *</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={formData.date}\n                    onChange={(e) => setFormData({ ...formData, date: e.target.value })}\n                    required\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Turno</Form.Label>\n                  <Form.Select\n                    value={formData.shiftId}\n                    onChange={(e) => setFormData({ ...formData, shiftId: e.target.value })}\n                  >\n                    <option value=\"\">Personalizado</option>\n                    {shifts.map(shift => (\n                      <option key={shift.id} value={shift.id}>\n                        {shift.name} ({shift.hours}h)\n                      </option>\n                    ))}\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Row>\n              <Col md={4}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Horas Trabajadas</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    step=\"0.5\"\n                    min=\"0\"\n                    max=\"24\"\n                    value={formData.customHours}\n                    onChange={(e) => setFormData({ ...formData, customHours: e.target.value })}\n                    placeholder=\"Ej: 8\"\n                  />\n                  <Form.Text className=\"text-muted\">\n                    Se calculará automáticamente si defines entrada y salida\n                  </Form.Text>\n                </Form.Group>\n              </Col>\n              <Col md={4}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Hora de Entrada</Form.Label>\n                  <Form.Control\n                    type=\"time\"\n                    value={formData.entryTime}\n                    onChange={(e) => setFormData({ ...formData, entryTime: e.target.value })}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={4}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Hora de Salida</Form.Label>\n                  <Form.Control\n                    type=\"time\"\n                    value={formData.exitTime}\n                    onChange={(e) => setFormData({ ...formData, exitTime: e.target.value })}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Descripción</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={3}\n                value={formData.description}\n                onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                placeholder=\"Notas adicionales sobre este día trabajado...\"\n              />\n            </Form.Group>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowModal(false)}>\n            Cancelar\n          </Button>\n          <Button\n            variant=\"primary\"\n            onClick={handleSubmit}\n            disabled={loading || !formData.date}\n          >\n            {loading ? 'Guardando...' : (editingDay ? 'Actualizar' : 'Registrar')}\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </div>\n  );\n}\n\nexport default RealCalendarView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AACpF,SAASC,cAAc,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,YAAY,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErG,SAASC,gBAAgBA,CAAC;EAAEC,YAAY;EAAEC,YAAY;EAAEC;AAAiB,CAAC,EAAE;EAAAC,EAAA;EAC1E,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuB,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC;IACvCmC,IAAI,EAAE,EAAE;IACRC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE;EACf,CAAC,CAAC;EAEFvC,SAAS,CAAC,MAAM;IACdwC,YAAY,CAAC,CAAC;IACdC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiB,IAAI,GAAG,MAAMjC,cAAc,CAAC,CAAC;MACnCY,WAAW,CAACqB,IAAI,CAAC;MACjBf,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOgB,GAAG,EAAE;MACZC,OAAO,CAAClB,KAAK,CAAC,0BAA0B,EAAEiB,GAAG,CAAC;MAC9ChB,QAAQ,CAAC,8BAA8B,GAAGgB,GAAG,CAACE,OAAO,CAAC;IACxD,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMK,UAAU,GAAG,MAAMlC,YAAY,CAAC,CAAC;MACvCW,SAAS,CAACuB,UAAU,CAAC;IACvB,CAAC,CAAC,OAAOH,GAAG,EAAE;MACZC,OAAO,CAAClB,KAAK,CAAC,uBAAuB,EAAEiB,GAAG,CAAC;IAC7C;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzBhB,aAAa,CAAC,IAAI,CAAC;IACnBE,WAAW,CAAC;MACVC,IAAI,EAAE,IAAIc,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5Cf,OAAO,EAAE,EAAE;MACXC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE;IACf,CAAC,CAAC;IACFV,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMsB,aAAa,GAAIC,GAAG,IAAK;IAC7BrB,aAAa,CAACqB,GAAG,CAAC;IAClBnB,WAAW,CAAC;MACVC,IAAI,EAAEkB,GAAG,CAAClB,IAAI;MACdC,OAAO,EAAEiB,GAAG,CAACjB,OAAO,IAAI,EAAE;MAC1BC,WAAW,EAAEgB,GAAG,CAACC,KAAK,IAAI,EAAE;MAC5BhB,SAAS,EAAEe,GAAG,CAACf,SAAS,IAAI,EAAE;MAC9BC,QAAQ,EAAEc,GAAG,CAACd,QAAQ,IAAI,EAAE;MAC5BC,WAAW,EAAEa,GAAG,CAACb,WAAW,IAAI;IAClC,CAAC,CAAC;IACFV,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMyB,eAAe,GAAG,MAAOC,OAAO,IAAK;IACzC,IAAIC,MAAM,CAACC,OAAO,CAAC,2DAA2D,CAAC,EAAE;MAC/E,IAAI;QACFhC,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMd,aAAa,CAAC4C,OAAO,CAAC;QAC5B,MAAMf,YAAY,CAAC,CAAC;QACpB,IAAItB,gBAAgB,EAAEA,gBAAgB,CAAC,CAAC;MAC1C,CAAC,CAAC,OAAOyB,GAAG,EAAE;QACZhB,QAAQ,CAAC,wBAAwB,GAAGgB,GAAG,CAACE,OAAO,CAAC;MAClD,CAAC,SAAS;QACRpB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;EAED,MAAMiC,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAOtC,QAAQ,CAACuC,MAAM,CAAC,CAACC,KAAK,EAAER,GAAG,KAAKQ,KAAK,IAAIR,GAAG,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACrE,CAAC;EAED,MAAMQ,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,YAAY,GAAG,IAAId,IAAI,CAAC,CAAC,CAACe,QAAQ,CAAC,CAAC;IAC1C,MAAMC,WAAW,GAAG,IAAIhB,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;IAE5C,OAAO7C,QAAQ,CACZ8C,MAAM,CAACd,GAAG,IAAI;MACb,MAAMG,OAAO,GAAG,IAAIP,IAAI,CAACI,GAAG,CAAClB,IAAI,CAAC;MAClC,OAAOqB,OAAO,CAACQ,QAAQ,CAAC,CAAC,KAAKD,YAAY,IAAIP,OAAO,CAACU,WAAW,CAAC,CAAC,KAAKD,WAAW;IACrF,CAAC,CAAC,CACDL,MAAM,CAAC,CAACC,KAAK,EAAER,GAAG,KAAKQ,KAAK,IAAIR,GAAG,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACxD,CAAC;EAED,MAAMc,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI;MACF5C,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI4B,KAAK,GAAGiB,UAAU,CAACtC,QAAQ,CAACI,WAAW,CAAC,IAAI,CAAC;MACjD,IAAIJ,QAAQ,CAACK,SAAS,IAAIL,QAAQ,CAACM,QAAQ,IAAI,CAACN,QAAQ,CAACI,WAAW,EAAE;QACpE,MAAMmC,KAAK,GAAG,IAAIvB,IAAI,CAAC,cAAchB,QAAQ,CAACK,SAAS,EAAE,CAAC;QAC1D,MAAMmC,IAAI,GAAG,IAAIxB,IAAI,CAAC,cAAchB,QAAQ,CAACM,QAAQ,EAAE,CAAC;QACxDe,KAAK,GAAG,CAACmB,IAAI,GAAGD,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;MAC7C;MAEA,MAAME,OAAO,GAAG;QACdvC,IAAI,EAAEF,QAAQ,CAACE,IAAI;QACnBC,OAAO,EAAEH,QAAQ,CAACG,OAAO,IAAI,IAAI;QACjCkB,KAAK,EAAEA,KAAK;QACZhB,SAAS,EAAEL,QAAQ,CAACK,SAAS,IAAI,IAAI;QACrCC,QAAQ,EAAEN,QAAQ,CAACM,QAAQ,IAAI,IAAI;QACnCC,WAAW,EAAEP,QAAQ,CAACO,WAAW,IAAI;MACvC,CAAC;MAED,IAAIT,UAAU,EAAE;QACd2C,OAAO,CAACC,EAAE,GAAG5C,UAAU,CAAC4C,EAAE;MAC5B;MAEA,MAAMhE,qBAAqB,CAAC+D,OAAO,CAAC;MACpC,MAAMjC,YAAY,CAAC,CAAC;MACpB,IAAItB,gBAAgB,EAAEA,gBAAgB,CAAC,CAAC;MACxCW,YAAY,CAAC,KAAK,CAAC;MACnBF,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOgB,GAAG,EAAE;MACZhB,QAAQ,CAAC,uBAAuB,GAAGgB,GAAG,CAACE,OAAO,CAAC;IACjD,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkD,YAAY,GAAIxC,OAAO,IAAK;IAChC,MAAMyC,KAAK,GAAGtD,MAAM,CAACuD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,EAAE,KAAKvC,OAAO,CAAC;IAChD,OAAOyC,KAAK,GAAGA,KAAK,CAACG,IAAI,GAAG,eAAe;EAC7C,CAAC;EAED,oBACEjE,OAAA;IAAAkE,QAAA,gBACElE,OAAA,CAACb,GAAG;MAACgF,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBlE,OAAA,CAACZ,GAAG;QAAA8E,QAAA,eACFlE,OAAA;UAAKmE,SAAS,EAAC,mDAAmD;UAAAD,QAAA,gBAChElE,OAAA;YAAAkE,QAAA,EAAI;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnCvE,OAAA;YAAAkE,QAAA,eACElE,OAAA,CAACX,MAAM;cACLmF,OAAO,EAAC,SAAS;cACjBC,OAAO,EAAExC,YAAa;cACtByC,QAAQ,EAAEhE,OAAQ;cAAAwD,QAAA,EACnB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL3D,KAAK,iBACJZ,OAAA,CAACb,GAAG;MAACgF,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBlE,OAAA,CAACZ,GAAG;QAAA8E,QAAA,eACFlE,OAAA,CAACV,KAAK;UAACkF,OAAO,EAAC,QAAQ;UAACG,OAAO,EAAEA,CAAA,KAAM9D,QAAQ,CAAC,EAAE,CAAE;UAAC+D,WAAW;UAAAV,QAAA,EAC7DtD;QAAK;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDvE,OAAA,CAACb,GAAG;MAACgF,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBlE,OAAA,CAACZ,GAAG;QAACyF,EAAE,EAAE,CAAE;QAAAX,QAAA,eACTlE,OAAA;UAAKmE,SAAS,EAAC,oBAAoB;UAAAD,QAAA,gBACjClE,OAAA;YAAAkE,QAAA,EAAI;UAA2B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACnC7D,OAAO,gBACNV,OAAA;YAAAkE,QAAA,EAAG;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,GAC5BjE,QAAQ,CAACwE,MAAM,GAAG,CAAC,gBACrB9E,OAAA,CAACT,KAAK;YAACwF,OAAO;YAACC,QAAQ;YAACC,KAAK;YAACC,UAAU;YAAAhB,QAAA,gBACtClE,OAAA;cAAAkE,QAAA,eACElE,OAAA;gBAAAkE,QAAA,gBACElE,OAAA;kBAAAkE,QAAA,EAAI;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACdvE,OAAA;kBAAAkE,QAAA,EAAI;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACdvE,OAAA;kBAAAkE,QAAA,EAAI;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACdvE,OAAA;kBAAAkE,QAAA,EAAI;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChBvE,OAAA;kBAAAkE,QAAA,EAAI;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACfvE,OAAA;kBAAAkE,QAAA,EAAI;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpBvE,OAAA;kBAAAkE,QAAA,EAAI;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRvE,OAAA;cAAAkE,QAAA,EACG5D,QAAQ,CACN6E,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAInD,IAAI,CAACmD,CAAC,CAACjE,IAAI,CAAC,GAAG,IAAIc,IAAI,CAACkD,CAAC,CAAChE,IAAI,CAAC,CAAC,CACnDkE,GAAG,CAAChD,GAAG,iBACNtC,OAAA;gBAAAkE,QAAA,gBACElE,OAAA;kBAAAkE,QAAA,EACG,IAAIhC,IAAI,CAACI,GAAG,CAAClB,IAAI,CAAC,CAACmE,kBAAkB,CAAC,OAAO,EAAE;oBAC9CC,OAAO,EAAE,OAAO;oBAChBlD,GAAG,EAAE,SAAS;oBACdmD,KAAK,EAAE,SAAS;oBAChBC,IAAI,EAAE;kBACR,CAAC;gBAAC;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACLvE,OAAA;kBAAAkE,QAAA,eACElE,OAAA,CAACR,KAAK;oBAACmG,EAAE,EAAC,WAAW;oBAAAzB,QAAA,EAClBL,YAAY,CAACvB,GAAG,CAACjB,OAAO;kBAAC;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACLvE,OAAA;kBAAAkE,QAAA,eAAIlE,OAAA;oBAAAkE,QAAA,GAAS5B,GAAG,CAACC,KAAK,EAAC,GAAC;kBAAA;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtCvE,OAAA;kBAAAkE,QAAA,EAAK5B,GAAG,CAACf,SAAS,IAAI;gBAAG;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC/BvE,OAAA;kBAAAkE,QAAA,EAAK5B,GAAG,CAACd,QAAQ,IAAI;gBAAG;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9BvE,OAAA;kBAAAkE,QAAA,EAAK5B,GAAG,CAACb,WAAW,IAAI;gBAAG;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjCvE,OAAA;kBAAAkE,QAAA,gBACElE,OAAA,CAACX,MAAM;oBACLmF,OAAO,EAAC,iBAAiB;oBACzBoB,IAAI,EAAC,IAAI;oBACTnB,OAAO,EAAEA,CAAA,KAAMpC,aAAa,CAACC,GAAG,CAAE;oBAClC6B,SAAS,EAAC,MAAM;oBAAAD,QAAA,EACjB;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTvE,OAAA,CAACX,MAAM;oBACLmF,OAAO,EAAC,gBAAgB;oBACxBoB,IAAI,EAAC,IAAI;oBACTnB,OAAO,EAAEA,CAAA,KAAMjC,eAAe,CAACF,GAAG,CAACsB,EAAE,CAAE;oBAAAM,QAAA,EACxC;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GAlCEjC,GAAG,CAACsB,EAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmCX,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAERvE,OAAA,CAACV,KAAK;YAACkF,OAAO,EAAC,MAAM;YAAAN,QAAA,EAAC;UAEtB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNvE,OAAA,CAACZ,GAAG;QAACyF,EAAE,EAAE,CAAE;QAAAX,QAAA,eACTlE,OAAA;UAAKmE,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC7BlE,OAAA;YAAAkE,QAAA,EAAI;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BvE,OAAA;YAAKmE,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxBlE,OAAA;cAAAkE,QAAA,EAAQ;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjE,QAAQ,CAACwE,MAAM;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNvE,OAAA;YAAKmE,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxBlE,OAAA;cAAAkE,QAAA,EAAQ;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3B,aAAa,CAAC,CAAC,CAACiD,OAAO,CAAC,CAAC,CAAC,EAAC,GAC/D;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNvE,OAAA;YAAKmE,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxBlE,OAAA;cAAAkE,QAAA,EAAQ;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxB,oBAAoB,CAAC,CAAC,CAAC8C,OAAO,CAAC,CAAC,CAAC,EAAC,GACtE;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNvE,OAAA;YAAKmE,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxBlE,OAAA;cAAAkE,QAAA,EAAQ;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjE,QAAQ,CAACwE,MAAM,GAAG,CAAC,GAAG,CAAClC,aAAa,CAAC,CAAC,GAAGtC,QAAQ,CAACwE,MAAM,EAAEe,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,GAC/G;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvE,OAAA,CAACP,KAAK;MAACqG,IAAI,EAAEhF,SAAU;MAACiF,MAAM,EAAEA,CAAA,KAAMhF,YAAY,CAAC,KAAK,CAAE;MAAC6E,IAAI,EAAC,IAAI;MAAA1B,QAAA,gBAClElE,OAAA,CAACP,KAAK,CAACuG,MAAM;QAACC,WAAW;QAAA/B,QAAA,eACvBlE,OAAA,CAACP,KAAK,CAACyG,KAAK;UAAAhC,QAAA,EACTlD,UAAU,GAAG,sBAAsB,GAAG;QAAqB;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfvE,OAAA,CAACP,KAAK,CAAC0G,IAAI;QAAAjC,QAAA,eACTlE,OAAA,CAACN,IAAI;UAAC0G,QAAQ,EAAG9C,CAAC,IAAKA,CAAC,CAACC,cAAc,CAAC,CAAE;UAAAW,QAAA,gBACxClE,OAAA,CAACb,GAAG;YAAA+E,QAAA,gBACFlE,OAAA,CAACZ,GAAG;cAACyF,EAAE,EAAE,CAAE;cAAAX,QAAA,eACTlE,OAAA,CAACN,IAAI,CAAC2G,KAAK;gBAAClC,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BlE,OAAA,CAACN,IAAI,CAAC4G,KAAK;kBAAApC,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCvE,OAAA,CAACN,IAAI,CAAC6G,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEvF,QAAQ,CAACE,IAAK;kBACrBsF,QAAQ,EAAGpD,CAAC,IAAKnC,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEE,IAAI,EAAEkC,CAAC,CAACqD,MAAM,CAACF;kBAAM,CAAC,CAAE;kBACpEG,QAAQ;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNvE,OAAA,CAACZ,GAAG;cAACyF,EAAE,EAAE,CAAE;cAAAX,QAAA,eACTlE,OAAA,CAACN,IAAI,CAAC2G,KAAK;gBAAClC,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BlE,OAAA,CAACN,IAAI,CAAC4G,KAAK;kBAAApC,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9BvE,OAAA,CAACN,IAAI,CAACmH,MAAM;kBACVJ,KAAK,EAAEvF,QAAQ,CAACG,OAAQ;kBACxBqF,QAAQ,EAAGpD,CAAC,IAAKnC,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEG,OAAO,EAAEiC,CAAC,CAACqD,MAAM,CAACF;kBAAM,CAAC,CAAE;kBAAAvC,QAAA,gBAEvElE,OAAA;oBAAQyG,KAAK,EAAC,EAAE;oBAAAvC,QAAA,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACtC/D,MAAM,CAAC8E,GAAG,CAACxB,KAAK,iBACf9D,OAAA;oBAAuByG,KAAK,EAAE3C,KAAK,CAACF,EAAG;oBAAAM,QAAA,GACpCJ,KAAK,CAACG,IAAI,EAAC,IAAE,EAACH,KAAK,CAACvB,KAAK,EAAC,IAC7B;kBAAA,GAFauB,KAAK,CAACF,EAAE;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEb,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvE,OAAA,CAACb,GAAG;YAAA+E,QAAA,gBACFlE,OAAA,CAACZ,GAAG;cAACyF,EAAE,EAAE,CAAE;cAAAX,QAAA,eACTlE,OAAA,CAACN,IAAI,CAAC2G,KAAK;gBAAClC,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BlE,OAAA,CAACN,IAAI,CAAC4G,KAAK;kBAAApC,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzCvE,OAAA,CAACN,IAAI,CAAC6G,OAAO;kBACXC,IAAI,EAAC,QAAQ;kBACbM,IAAI,EAAC,KAAK;kBACVC,GAAG,EAAC,GAAG;kBACPC,GAAG,EAAC,IAAI;kBACRP,KAAK,EAAEvF,QAAQ,CAACI,WAAY;kBAC5BoF,QAAQ,EAAGpD,CAAC,IAAKnC,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEI,WAAW,EAAEgC,CAAC,CAACqD,MAAM,CAACF;kBAAM,CAAC,CAAE;kBAC3EQ,WAAW,EAAC;gBAAO;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACFvE,OAAA,CAACN,IAAI,CAACwH,IAAI;kBAAC/C,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAAC;gBAElC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNvE,OAAA,CAACZ,GAAG;cAACyF,EAAE,EAAE,CAAE;cAAAX,QAAA,eACTlE,OAAA,CAACN,IAAI,CAAC2G,KAAK;gBAAClC,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BlE,OAAA,CAACN,IAAI,CAAC4G,KAAK;kBAAApC,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxCvE,OAAA,CAACN,IAAI,CAAC6G,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEvF,QAAQ,CAACK,SAAU;kBAC1BmF,QAAQ,EAAGpD,CAAC,IAAKnC,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEK,SAAS,EAAE+B,CAAC,CAACqD,MAAM,CAACF;kBAAM,CAAC;gBAAE;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNvE,OAAA,CAACZ,GAAG;cAACyF,EAAE,EAAE,CAAE;cAAAX,QAAA,eACTlE,OAAA,CAACN,IAAI,CAAC2G,KAAK;gBAAClC,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BlE,OAAA,CAACN,IAAI,CAAC4G,KAAK;kBAAApC,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvCvE,OAAA,CAACN,IAAI,CAAC6G,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEvF,QAAQ,CAACM,QAAS;kBACzBkF,QAAQ,EAAGpD,CAAC,IAAKnC,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEM,QAAQ,EAAE8B,CAAC,CAACqD,MAAM,CAACF;kBAAM,CAAC;gBAAE;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvE,OAAA,CAACN,IAAI,CAAC2G,KAAK;YAAClC,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BlE,OAAA,CAACN,IAAI,CAAC4G,KAAK;cAAApC,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpCvE,OAAA,CAACN,IAAI,CAAC6G,OAAO;cACXY,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRX,KAAK,EAAEvF,QAAQ,CAACO,WAAY;cAC5BiF,QAAQ,EAAGpD,CAAC,IAAKnC,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEO,WAAW,EAAE6B,CAAC,CAACqD,MAAM,CAACF;cAAM,CAAC,CAAE;cAC3EQ,WAAW,EAAC;YAA+C;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbvE,OAAA,CAACP,KAAK,CAAC4H,MAAM;QAAAnD,QAAA,gBACXlE,OAAA,CAACX,MAAM;UAACmF,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAM1D,YAAY,CAAC,KAAK,CAAE;UAAAmD,QAAA,EAAC;QAEhE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvE,OAAA,CAACX,MAAM;UACLmF,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAEpB,YAAa;UACtBqB,QAAQ,EAAEhE,OAAO,IAAI,CAACQ,QAAQ,CAACE,IAAK;UAAA8C,QAAA,EAEnCxD,OAAO,GAAG,cAAc,GAAIM,UAAU,GAAG,YAAY,GAAG;QAAY;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV;AAAClE,EAAA,CAlXQJ,gBAAgB;AAAAqH,EAAA,GAAhBrH,gBAAgB;AAoXzB,eAAeA,gBAAgB;AAAC,IAAAqH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}