const analysisService = require('../services/analysisService');

const compareCalendars = (req, res) => {
    console.log('compareCalendars called');
    try {
        const { startDate, endDate } = req.query;
        
        if (!startDate || !endDate) {
            return res.status(400).json({ 
                message: 'startDate and endDate are required parameters' 
            });
        }
        
        console.log('Comparing calendars from', startDate, 'to', endDate);
        const comparison = analysisService.compareCalendars(startDate, endDate);
        console.log('Comparison completed, returning', comparison.length, 'days');
        
        res.json(comparison);
    } catch (error) {
        console.error('Error in compareCalendars:', error);
        res.status(500).json({ message: error.message });
    }
};

const getComplianceStats = (req, res) => {
    console.log('getComplianceStats called');
    try {
        const { startDate, endDate } = req.query;
        
        if (!startDate || !endDate) {
            return res.status(400).json({ 
                message: 'startDate and endDate are required parameters' 
            });
        }
        
        console.log('Generating compliance stats from', startDate, 'to', endDate);
        const stats = analysisService.generateComplianceStats(startDate, endDate);
        console.log('Stats generated:', stats);
        
        res.json(stats);
    } catch (error) {
        console.error('Error in getComplianceStats:', error);
        res.status(500).json({ message: error.message });
    }
};

const getVarianceSummary = (req, res) => {
    console.log('getVarianceSummary called');
    try {
        const { startDate, endDate } = req.query;
        
        if (!startDate || !endDate) {
            return res.status(400).json({ 
                message: 'startDate and endDate are required parameters' 
            });
        }
        
        console.log('Generating variance summary from', startDate, 'to', endDate);
        const summary = analysisService.getVarianceSummary(startDate, endDate);
        console.log('Summary generated:', summary);
        
        res.json(summary);
    } catch (error) {
        console.error('Error in getVarianceSummary:', error);
        res.status(500).json({ message: error.message });
    }
};

module.exports = {
    compareCalendars,
    getComplianceStats,
    getVarianceSummary
};
