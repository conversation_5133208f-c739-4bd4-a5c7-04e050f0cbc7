{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\SettingsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Form, Alert, ListGroup, Modal } from 'react-bootstrap';\nimport { getAllPatterns, createPattern, updatePattern, deletePattern, getAllShifts } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SettingsPage() {\n  _s();\n  const [patterns, setPatterns] = useState([]);\n  const [currentPattern, setCurrentPattern] = useState(null);\n  const [showPatternForm, setShowPatternForm] = useState(false);\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    loadPatterns();\n    loadAvailableShifts();\n  }, []);\n  const loadPatterns = async () => {\n    setLoading(true);\n    try {\n      const data = await getAllPatterns();\n      setPatterns(data);\n    } catch (err) {\n      setError('Error cargando patrones.');\n      console.error('Error loading patterns:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (err) {\n      console.error('Error loading shifts:', err);\n    }\n  };\n  const handleAddPattern = () => {\n    setCurrentPattern({\n      name: '',\n      basePattern: Array.from({\n        length: 7\n      }, () => ({\n        type: '',\n        hours: 0,\n        description: '',\n        shiftId: null\n      })),\n      overrides: []\n    });\n    setShowPatternForm(true);\n  };\n  const handleEditPattern = pattern => {\n    setCurrentPattern({\n      ...pattern\n    });\n    setShowPatternForm(true);\n  };\n  const handleDeletePattern = async id => {\n    if (window.confirm('¿Estás seguro de que quieres eliminar este patrón?')) {\n      setLoading(true);\n      try {\n        await deletePattern(id);\n        await loadPatterns();\n      } catch (err) {\n        setError('Error eliminando patrón.');\n        console.error('Error deleting pattern:', err);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  const handleSavePattern = async () => {\n    if (!currentPattern || !currentPattern.name || !currentPattern.basePattern) {\n      setError('El nombre del patrón y el patrón base son requeridos.');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      if (currentPattern.id) {\n        await updatePattern(currentPattern.id, currentPattern.name, currentPattern.basePattern, currentPattern.overrides);\n      } else {\n        await createPattern(currentPattern.name, currentPattern.basePattern, currentPattern.overrides);\n      }\n      setShowPatternForm(false);\n      setCurrentPattern(null);\n      await loadPatterns();\n    } catch (err) {\n      setError('Error guardando patrón.');\n      console.error('Error saving pattern:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCancelEdit = () => {\n    setShowPatternForm(false);\n    setCurrentPattern(null);\n    setError('');\n  };\n  const handlePatternDayChange = (index, field, value) => {\n    const newPatternDays = [...currentPattern.patternDays];\n    newPatternDays[index][field] = value;\n    if (field === 'shiftId' && value) {\n      const shift = availableShifts.find(s => s.id === value);\n      if (shift) {\n        newPatternDays[index].type = 'worked';\n        newPatternDays[index].hours = shift.totalHours;\n        newPatternDays[index].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;\n      }\n    } else if (field === 'shiftId' && !value) {\n      newPatternDays[index].description = '';\n    }\n    setCurrentPattern({\n      ...currentPattern,\n      patternDays: newPatternDays\n    });\n  };\n  const handleAddPatternDay = () => {\n    setCurrentPattern({\n      ...currentPattern,\n      patternDays: [...currentPattern.patternDays, {\n        type: '',\n        hours: 0,\n        description: '',\n        shiftId: null\n      }]\n    });\n  };\n  const handleRemovePatternDay = index => {\n    const newPatternDays = currentPattern.patternDays.filter((_, i) => i !== index);\n    setCurrentPattern({\n      ...currentPattern,\n      patternDays: newPatternDays\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"my-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Configuraci\\xF3n de Patrones de Calendario\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleAddPattern,\n          className: \"mb-3\",\n          children: \"Crear Nuevo Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Cargando patrones...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this) : patterns.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No hay patrones definidos.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ListGroup, {\n          children: patterns.map(pattern => /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n            className: \"d-flex justify-content-between align-items-center\",\n            children: [pattern.name, /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"info\",\n                size: \"sm\",\n                className: \"me-2\",\n                onClick: () => handleEditPattern(pattern),\n                children: \"Editar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"danger\",\n                size: \"sm\",\n                onClick: () => handleDeletePattern(pattern.id),\n                children: \"Eliminar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 19\n            }, this)]\n          }, pattern.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPatternForm,\n      onHide: handleCancelEdit,\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: currentPattern && currentPattern.id ? 'Editar Patrón' : 'Crear Nuevo Patrón'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: currentPattern && /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Nombre del Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              value: currentPattern.name,\n              onChange: e => setCurrentPattern({\n                ...currentPattern,\n                name: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"D\\xEDas del Patr\\xF3n:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this), currentPattern.patternDays.map((day, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border p-3 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: [\"D\\xEDa \", index + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Tipo de D\\xEDa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: day.type,\n                onChange: e => handlePatternDayChange(index, 'type', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Seleccionar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"worked\",\n                  children: \"Trabajado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"holiday\",\n                  children: \"Vacaciones\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"permit\",\n                  children: \"Permiso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"negative\",\n                  children: \"C\\xF3mputo Negativo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Horas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"number\",\n                value: day.hours,\n                onChange: e => handlePatternDayChange(index, 'hours', parseFloat(e.target.value))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Turno Predefinido\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: day.shiftId || '',\n                onChange: e => handlePatternDayChange(index, 'shiftId', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ninguno\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 23\n                }, this), availableShifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: shift.id,\n                  children: [shift.name, \" - \", shift.startTime, \" a \", shift.endTime, \" (\", shift.totalHours, \"h)\"]\n                }, shift.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Descripci\\xF3n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                as: \"textarea\",\n                rows: 1,\n                value: day.description,\n                onChange: e => handlePatternDayChange(index, 'description', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              size: \"sm\",\n              onClick: () => handleRemovePatternDay(index),\n              children: \"Eliminar D\\xEDa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 17\n          }, this)), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: handleAddPatternDay,\n            className: \"mt-3\",\n            children: \"A\\xF1adir D\\xEDa al Patr\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: handleCancelEdit,\n          children: \"Cancelar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSavePattern,\n          disabled: loading,\n          children: \"Guardar Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this);\n}\n_s(SettingsPage, \"b7P1wL7WR8a8/hQr8hgslhhk7SU=\");\n_c = SettingsPage;\nexport default SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "<PERSON><PERSON>", "Form", "<PERSON><PERSON>", "ListGroup", "Modal", "getAllPatterns", "createPattern", "updatePattern", "deletePattern", "getAllShifts", "jsxDEV", "_jsxDEV", "SettingsPage", "_s", "patterns", "setPatterns", "currentPattern", "setCurrentPattern", "showPatternForm", "setShowPatternForm", "availableShifts", "setAvailableShifts", "loading", "setLoading", "error", "setError", "loadPatterns", "loadAvailableShifts", "data", "err", "console", "shifts", "handleAddPattern", "name", "basePattern", "Array", "from", "length", "type", "hours", "description", "shiftId", "overrides", "handleEditPattern", "pattern", "handleDeletePattern", "id", "window", "confirm", "handleSavePattern", "handleCancelEdit", "handlePatternDayChange", "index", "field", "value", "newPatternDays", "patternDays", "shift", "find", "s", "totalHours", "startTime", "endTime", "breakMinutes", "handleAddPatternDay", "handleRemovePatternDay", "filter", "_", "i", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "map", "<PERSON><PERSON>", "size", "show", "onHide", "Header", "closeButton", "Title", "Body", "Group", "Label", "Control", "onChange", "e", "target", "day", "Select", "parseFloat", "as", "rows", "Footer", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/SettingsPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Form, Alert, ListGroup, Modal } from 'react-bootstrap';\nimport { getAllPatterns, createPattern, updatePattern, deletePattern, getAllShifts } from '../services/api';\n\nfunction SettingsPage() {\n  const [patterns, setPatterns] = useState([]);\n  const [currentPattern, setCurrentPattern] = useState(null);\n  const [showPatternForm, setShowPatternForm] = useState(false);\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    loadPatterns();\n    loadAvailableShifts();\n  }, []);\n\n  const loadPatterns = async () => {\n    setLoading(true);\n    try {\n      const data = await getAllPatterns();\n      setPatterns(data);\n    } catch (err) {\n      setError('Error cargando patrones.');\n      console.error('Error loading patterns:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (err) {\n      console.error('Error loading shifts:', err);\n    }\n  };\n\n  const handleAddPattern = () => {\n    setCurrentPattern({\n      name: '',\n      basePattern: Array.from({ length: 7 }, () => ({ type: '', hours: 0, description: '', shiftId: null })),\n      overrides: [],\n    });\n    setShowPatternForm(true);\n  };\n\n  const handleEditPattern = (pattern) => {\n    setCurrentPattern({ ...pattern });\n    setShowPatternForm(true);\n  };\n\n  const handleDeletePattern = async (id) => {\n    if (window.confirm('¿Estás seguro de que quieres eliminar este patrón?')) {\n      setLoading(true);\n      try {\n        await deletePattern(id);\n        await loadPatterns();\n      } catch (err) {\n        setError('Error eliminando patrón.');\n        console.error('Error deleting pattern:', err);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  const handleSavePattern = async () => {\n    if (!currentPattern || !currentPattern.name || !currentPattern.basePattern) {\n      setError('El nombre del patrón y el patrón base son requeridos.');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    try {\n      if (currentPattern.id) {\n        await updatePattern(currentPattern.id, currentPattern.name, currentPattern.basePattern, currentPattern.overrides);\n      } else {\n        await createPattern(currentPattern.name, currentPattern.basePattern, currentPattern.overrides);\n      }\n      setShowPatternForm(false);\n      setCurrentPattern(null);\n      await loadPatterns();\n    } catch (err) {\n      setError('Error guardando patrón.');\n      console.error('Error saving pattern:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancelEdit = () => {\n    setShowPatternForm(false);\n    setCurrentPattern(null);\n    setError('');\n  };\n\n  const handlePatternDayChange = (index, field, value) => {\n    const newPatternDays = [...currentPattern.patternDays];\n    newPatternDays[index][field] = value;\n\n    if (field === 'shiftId' && value) {\n      const shift = availableShifts.find(s => s.id === value);\n      if (shift) {\n        newPatternDays[index].type = 'worked';\n        newPatternDays[index].hours = shift.totalHours;\n        newPatternDays[index].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;\n      }\n    } else if (field === 'shiftId' && !value) {\n      newPatternDays[index].description = '';\n    }\n\n    setCurrentPattern({ ...currentPattern, patternDays: newPatternDays });\n  };\n\n  const handleAddPatternDay = () => {\n    setCurrentPattern({\n      ...currentPattern,\n      patternDays: [...currentPattern.patternDays, { type: '', hours: 0, description: '', shiftId: null }]\n    });\n  };\n\n  const handleRemovePatternDay = (index) => {\n    const newPatternDays = currentPattern.patternDays.filter((_, i) => i !== index);\n    setCurrentPattern({ ...currentPattern, patternDays: newPatternDays });\n  };\n\n  return (\n    <Container>\n      <Row className=\"my-4\">\n        <Col>\n          <h2>Configuración de Patrones de Calendario</h2>\n          {error && <Alert variant=\"danger\">{error}</Alert>}\n          <Button onClick={handleAddPattern} className=\"mb-3\">Crear Nuevo Patrón</Button>\n\n          {loading ? (\n            <p>Cargando patrones...</p>\n          ) : patterns.length === 0 ? (\n            <p>No hay patrones definidos.</p>\n          ) : (\n            <ListGroup>\n              {patterns.map(pattern => (\n                <ListGroup.Item key={pattern.id} className=\"d-flex justify-content-between align-items-center\">\n                  {pattern.name}\n                  <div>\n                    <Button variant=\"info\" size=\"sm\" className=\"me-2\" onClick={() => handleEditPattern(pattern)}>Editar</Button>\n                    <Button variant=\"danger\" size=\"sm\" onClick={() => handleDeletePattern(pattern.id)}>Eliminar</Button>\n                  </div>\n                </ListGroup.Item>\n              ))}\n            </ListGroup>\n          )}\n        </Col>\n      </Row>\n\n      <Modal show={showPatternForm} onHide={handleCancelEdit} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>{currentPattern && currentPattern.id ? 'Editar Patrón' : 'Crear Nuevo Patrón'}</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {currentPattern && (\n            <Form>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Nombre del Patrón</Form.Label>\n                <Form.Control\n                  type=\"text\"\n                  value={currentPattern.name}\n                  onChange={(e) => setCurrentPattern({ ...currentPattern, name: e.target.value })}\n                />\n              </Form.Group>\n\n              <h5>Días del Patrón:</h5>\n              {currentPattern.patternDays.map((day, index) => (\n                <div key={index} className=\"border p-3 mb-2\">\n                  <h6>Día {index + 1}</h6>\n                  <Form.Group className=\"mb-2\">\n                    <Form.Label>Tipo de Día</Form.Label>\n                    <Form.Select\n                      value={day.type}\n                      onChange={(e) => handlePatternDayChange(index, 'type', e.target.value)}\n                    >\n                      <option value=\"\">Seleccionar</option>\n                      <option value=\"worked\">Trabajado</option>\n                      <option value=\"holiday\">Vacaciones</option>\n                      <option value=\"permit\">Permiso</option>\n                      <option value=\"negative\">Cómputo Negativo</option>\n                    </Form.Select>\n                  </Form.Group>\n                  <Form.Group className=\"mb-2\">\n                    <Form.Label>Horas</Form.Label>\n                    <Form.Control\n                      type=\"number\"\n                      value={day.hours}\n                      onChange={(e) => handlePatternDayChange(index, 'hours', parseFloat(e.target.value))}\n                    />\n                  </Form.Group>\n                  <Form.Group className=\"mb-2\">\n                    <Form.Label>Turno Predefinido</Form.Label>\n                    <Form.Select\n                      value={day.shiftId || ''}\n                      onChange={(e) => handlePatternDayChange(index, 'shiftId', e.target.value)}\n                    >\n                      <option value=\"\">Ninguno</option>\n                      {availableShifts.map((shift) => (\n                        <option key={shift.id} value={shift.id}>\n                          {shift.name} - {shift.startTime} a {shift.endTime} ({shift.totalHours}h)\n                        </option>\n                      ))}\n                    </Form.Select>\n                  </Form.Group>\n                  <Form.Group className=\"mb-2\">\n                    <Form.Label>Descripción</Form.Label>\n                    <Form.Control\n                      as=\"textarea\"\n                      rows={1}\n                      value={day.description}\n                      onChange={(e) => handlePatternDayChange(index, 'description', e.target.value)}\n                    />\n                  </Form.Group>\n                  <Button variant=\"danger\" size=\"sm\" onClick={() => handleRemovePatternDay(index)}>\n                    Eliminar Día\n                  </Button>\n                </div>\n              ))}\n              <Button variant=\"secondary\" onClick={handleAddPatternDay} className=\"mt-3\">Añadir Día al Patrón</Button>\n            </Form>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={handleCancelEdit}>Cancelar</Button>\n          <Button variant=\"primary\" onClick={handleSavePattern} disabled={loading}>Guardar Patrón</Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n}\n\nexport default SettingsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAQ,iBAAiB;AAC5F,SAASC,cAAc,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5G,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd8B,YAAY,CAAC,CAAC;IACdC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMK,IAAI,GAAG,MAAMvB,cAAc,CAAC,CAAC;MACnCU,WAAW,CAACa,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZJ,QAAQ,CAAC,0BAA0B,CAAC;MACpCK,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEK,GAAG,CAAC;IAC/C,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMI,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMI,MAAM,GAAG,MAAMtB,YAAY,CAAC,CAAC;MACnCY,kBAAkB,CAACU,MAAM,CAAC;IAC5B,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAACN,KAAK,CAAC,uBAAuB,EAAEK,GAAG,CAAC;IAC7C;EACF,CAAC;EAED,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bf,iBAAiB,CAAC;MAChBgB,IAAI,EAAE,EAAE;MACRC,WAAW,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,EAAE,OAAO;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,WAAW,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC,CAAC;MACtGC,SAAS,EAAE;IACb,CAAC,CAAC;IACFvB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMwB,iBAAiB,GAAIC,OAAO,IAAK;IACrC3B,iBAAiB,CAAC;MAAE,GAAG2B;IAAQ,CAAC,CAAC;IACjCzB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM0B,mBAAmB,GAAG,MAAOC,EAAE,IAAK;IACxC,IAAIC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACxEzB,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMf,aAAa,CAACsC,EAAE,CAAC;QACvB,MAAMpB,YAAY,CAAC,CAAC;MACtB,CAAC,CAAC,OAAOG,GAAG,EAAE;QACZJ,QAAQ,CAAC,0BAA0B,CAAC;QACpCK,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEK,GAAG,CAAC;MAC/C,CAAC,SAAS;QACRN,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;EAED,MAAM0B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACjC,cAAc,IAAI,CAACA,cAAc,CAACiB,IAAI,IAAI,CAACjB,cAAc,CAACkB,WAAW,EAAE;MAC1ET,QAAQ,CAAC,uDAAuD,CAAC;MACjE;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,IAAIT,cAAc,CAAC8B,EAAE,EAAE;QACrB,MAAMvC,aAAa,CAACS,cAAc,CAAC8B,EAAE,EAAE9B,cAAc,CAACiB,IAAI,EAAEjB,cAAc,CAACkB,WAAW,EAAElB,cAAc,CAAC0B,SAAS,CAAC;MACnH,CAAC,MAAM;QACL,MAAMpC,aAAa,CAACU,cAAc,CAACiB,IAAI,EAAEjB,cAAc,CAACkB,WAAW,EAAElB,cAAc,CAAC0B,SAAS,CAAC;MAChG;MACAvB,kBAAkB,CAAC,KAAK,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;MACvB,MAAMS,YAAY,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZJ,QAAQ,CAAC,yBAAyB,CAAC;MACnCK,OAAO,CAACN,KAAK,CAAC,uBAAuB,EAAEK,GAAG,CAAC;IAC7C,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B/B,kBAAkB,CAAC,KAAK,CAAC;IACzBF,iBAAiB,CAAC,IAAI,CAAC;IACvBQ,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAM0B,sBAAsB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,KAAK;IACtD,MAAMC,cAAc,GAAG,CAAC,GAAGvC,cAAc,CAACwC,WAAW,CAAC;IACtDD,cAAc,CAACH,KAAK,CAAC,CAACC,KAAK,CAAC,GAAGC,KAAK;IAEpC,IAAID,KAAK,KAAK,SAAS,IAAIC,KAAK,EAAE;MAChC,MAAMG,KAAK,GAAGrC,eAAe,CAACsC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACb,EAAE,KAAKQ,KAAK,CAAC;MACvD,IAAIG,KAAK,EAAE;QACTF,cAAc,CAACH,KAAK,CAAC,CAACd,IAAI,GAAG,QAAQ;QACrCiB,cAAc,CAACH,KAAK,CAAC,CAACb,KAAK,GAAGkB,KAAK,CAACG,UAAU;QAC9CL,cAAc,CAACH,KAAK,CAAC,CAACZ,WAAW,GAAG,GAAGiB,KAAK,CAACxB,IAAI,KAAKwB,KAAK,CAACI,SAAS,MAAMJ,KAAK,CAACK,OAAO,KAAKL,KAAK,CAACM,YAAY,eAAe;MAChI;IACF,CAAC,MAAM,IAAIV,KAAK,KAAK,SAAS,IAAI,CAACC,KAAK,EAAE;MACxCC,cAAc,CAACH,KAAK,CAAC,CAACZ,WAAW,GAAG,EAAE;IACxC;IAEAvB,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAEwC,WAAW,EAAED;IAAe,CAAC,CAAC;EACvE,CAAC;EAED,MAAMS,mBAAmB,GAAGA,CAAA,KAAM;IAChC/C,iBAAiB,CAAC;MAChB,GAAGD,cAAc;MACjBwC,WAAW,EAAE,CAAC,GAAGxC,cAAc,CAACwC,WAAW,EAAE;QAAElB,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,WAAW,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAK,CAAC;IACrG,CAAC,CAAC;EACJ,CAAC;EAED,MAAMwB,sBAAsB,GAAIb,KAAK,IAAK;IACxC,MAAMG,cAAc,GAAGvC,cAAc,CAACwC,WAAW,CAACU,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKhB,KAAK,CAAC;IAC/EnC,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAEwC,WAAW,EAAED;IAAe,CAAC,CAAC;EACvE,CAAC;EAED,oBACE5C,OAAA,CAACd,SAAS;IAAAwE,QAAA,gBACR1D,OAAA,CAACb,GAAG;MAACwE,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnB1D,OAAA,CAACZ,GAAG;QAAAsE,QAAA,gBACF1D,OAAA;UAAA0D,QAAA,EAAI;QAAuC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC/ClD,KAAK,iBAAIb,OAAA,CAACT,KAAK;UAACyE,OAAO,EAAC,QAAQ;UAAAN,QAAA,EAAE7C;QAAK;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACjD/D,OAAA,CAACX,MAAM;UAAC4E,OAAO,EAAE5C,gBAAiB;UAACsC,SAAS,EAAC,MAAM;UAAAD,QAAA,EAAC;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAE9EpD,OAAO,gBACNX,OAAA;UAAA0D,QAAA,EAAG;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,GACzB5D,QAAQ,CAACuB,MAAM,KAAK,CAAC,gBACvB1B,OAAA;UAAA0D,QAAA,EAAG;QAA0B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAEjC/D,OAAA,CAACR,SAAS;UAAAkE,QAAA,EACPvD,QAAQ,CAAC+D,GAAG,CAACjC,OAAO,iBACnBjC,OAAA,CAACR,SAAS,CAAC2E,IAAI;YAAkBR,SAAS,EAAC,mDAAmD;YAAAD,QAAA,GAC3FzB,OAAO,CAACX,IAAI,eACbtB,OAAA;cAAA0D,QAAA,gBACE1D,OAAA,CAACX,MAAM;gBAAC2E,OAAO,EAAC,MAAM;gBAACI,IAAI,EAAC,IAAI;gBAACT,SAAS,EAAC,MAAM;gBAACM,OAAO,EAAEA,CAAA,KAAMjC,iBAAiB,CAACC,OAAO,CAAE;gBAAAyB,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5G/D,OAAA,CAACX,MAAM;gBAAC2E,OAAO,EAAC,QAAQ;gBAACI,IAAI,EAAC,IAAI;gBAACH,OAAO,EAAEA,CAAA,KAAM/B,mBAAmB,CAACD,OAAO,CAACE,EAAE,CAAE;gBAAAuB,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjG,CAAC;UAAA,GALa9B,OAAO,CAACE,EAAE;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMf,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CACZ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN/D,OAAA,CAACP,KAAK;MAAC4E,IAAI,EAAE9D,eAAgB;MAAC+D,MAAM,EAAE/B,gBAAiB;MAAC6B,IAAI,EAAC,IAAI;MAAAV,QAAA,gBAC/D1D,OAAA,CAACP,KAAK,CAAC8E,MAAM;QAACC,WAAW;QAAAd,QAAA,eACvB1D,OAAA,CAACP,KAAK,CAACgF,KAAK;UAAAf,QAAA,EAAErD,cAAc,IAAIA,cAAc,CAAC8B,EAAE,GAAG,eAAe,GAAG;QAAoB;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F,CAAC,eACf/D,OAAA,CAACP,KAAK,CAACiF,IAAI;QAAAhB,QAAA,EACRrD,cAAc,iBACbL,OAAA,CAACV,IAAI;UAAAoE,QAAA,gBACH1D,OAAA,CAACV,IAAI,CAACqF,KAAK;YAAChB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1B1D,OAAA,CAACV,IAAI,CAACsF,KAAK;cAAAlB,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1C/D,OAAA,CAACV,IAAI,CAACuF,OAAO;cACXlD,IAAI,EAAC,MAAM;cACXgB,KAAK,EAAEtC,cAAc,CAACiB,IAAK;cAC3BwD,QAAQ,EAAGC,CAAC,IAAKzE,iBAAiB,CAAC;gBAAE,GAAGD,cAAc;gBAAEiB,IAAI,EAAEyD,CAAC,CAACC,MAAM,CAACrC;cAAM,CAAC;YAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEb/D,OAAA;YAAA0D,QAAA,EAAI;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACxB1D,cAAc,CAACwC,WAAW,CAACqB,GAAG,CAAC,CAACe,GAAG,EAAExC,KAAK,kBACzCzC,OAAA;YAAiB2D,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC1C1D,OAAA;cAAA0D,QAAA,GAAI,SAAI,EAACjB,KAAK,GAAG,CAAC;YAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxB/D,OAAA,CAACV,IAAI,CAACqF,KAAK;cAAChB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1B1D,OAAA,CAACV,IAAI,CAACsF,KAAK;gBAAAlB,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpC/D,OAAA,CAACV,IAAI,CAAC4F,MAAM;gBACVvC,KAAK,EAAEsC,GAAG,CAACtD,IAAK;gBAChBmD,QAAQ,EAAGC,CAAC,IAAKvC,sBAAsB,CAACC,KAAK,EAAE,MAAM,EAAEsC,CAAC,CAACC,MAAM,CAACrC,KAAK,CAAE;gBAAAe,QAAA,gBAEvE1D,OAAA;kBAAQ2C,KAAK,EAAC,EAAE;kBAAAe,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrC/D,OAAA;kBAAQ2C,KAAK,EAAC,QAAQ;kBAAAe,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzC/D,OAAA;kBAAQ2C,KAAK,EAAC,SAAS;kBAAAe,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3C/D,OAAA;kBAAQ2C,KAAK,EAAC,QAAQ;kBAAAe,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvC/D,OAAA;kBAAQ2C,KAAK,EAAC,UAAU;kBAAAe,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACb/D,OAAA,CAACV,IAAI,CAACqF,KAAK;cAAChB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1B1D,OAAA,CAACV,IAAI,CAACsF,KAAK;gBAAAlB,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9B/D,OAAA,CAACV,IAAI,CAACuF,OAAO;gBACXlD,IAAI,EAAC,QAAQ;gBACbgB,KAAK,EAAEsC,GAAG,CAACrD,KAAM;gBACjBkD,QAAQ,EAAGC,CAAC,IAAKvC,sBAAsB,CAACC,KAAK,EAAE,OAAO,EAAE0C,UAAU,CAACJ,CAAC,CAACC,MAAM,CAACrC,KAAK,CAAC;cAAE;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eACb/D,OAAA,CAACV,IAAI,CAACqF,KAAK;cAAChB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1B1D,OAAA,CAACV,IAAI,CAACsF,KAAK;gBAAAlB,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1C/D,OAAA,CAACV,IAAI,CAAC4F,MAAM;gBACVvC,KAAK,EAAEsC,GAAG,CAACnD,OAAO,IAAI,EAAG;gBACzBgD,QAAQ,EAAGC,CAAC,IAAKvC,sBAAsB,CAACC,KAAK,EAAE,SAAS,EAAEsC,CAAC,CAACC,MAAM,CAACrC,KAAK,CAAE;gBAAAe,QAAA,gBAE1E1D,OAAA;kBAAQ2C,KAAK,EAAC,EAAE;kBAAAe,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAChCtD,eAAe,CAACyD,GAAG,CAAEpB,KAAK,iBACzB9C,OAAA;kBAAuB2C,KAAK,EAAEG,KAAK,CAACX,EAAG;kBAAAuB,QAAA,GACpCZ,KAAK,CAACxB,IAAI,EAAC,KAAG,EAACwB,KAAK,CAACI,SAAS,EAAC,KAAG,EAACJ,KAAK,CAACK,OAAO,EAAC,IAAE,EAACL,KAAK,CAACG,UAAU,EAAC,IACxE;gBAAA,GAFaH,KAAK,CAACX,EAAE;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACb/D,OAAA,CAACV,IAAI,CAACqF,KAAK;cAAChB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1B1D,OAAA,CAACV,IAAI,CAACsF,KAAK;gBAAAlB,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpC/D,OAAA,CAACV,IAAI,CAACuF,OAAO;gBACXO,EAAE,EAAC,UAAU;gBACbC,IAAI,EAAE,CAAE;gBACR1C,KAAK,EAAEsC,GAAG,CAACpD,WAAY;gBACvBiD,QAAQ,EAAGC,CAAC,IAAKvC,sBAAsB,CAACC,KAAK,EAAE,aAAa,EAAEsC,CAAC,CAACC,MAAM,CAACrC,KAAK;cAAE;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eACb/D,OAAA,CAACX,MAAM;cAAC2E,OAAO,EAAC,QAAQ;cAACI,IAAI,EAAC,IAAI;cAACH,OAAO,EAAEA,CAAA,KAAMX,sBAAsB,CAACb,KAAK,CAAE;cAAAiB,QAAA,EAAC;YAEjF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,GAhDDtB,KAAK;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiDV,CACN,CAAC,eACF/D,OAAA,CAACX,MAAM;YAAC2E,OAAO,EAAC,WAAW;YAACC,OAAO,EAAEZ,mBAAoB;YAACM,SAAS,EAAC,MAAM;YAAAD,QAAA,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpG;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACb/D,OAAA,CAACP,KAAK,CAAC6F,MAAM;QAAA5B,QAAA,gBACX1D,OAAA,CAACX,MAAM;UAAC2E,OAAO,EAAC,WAAW;UAACC,OAAO,EAAE1B,gBAAiB;UAAAmB,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxE/D,OAAA,CAACX,MAAM;UAAC2E,OAAO,EAAC,SAAS;UAACC,OAAO,EAAE3B,iBAAkB;UAACiD,QAAQ,EAAE5E,OAAQ;UAAA+C,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB;AAAC7D,EAAA,CAzOQD,YAAY;AAAAuF,EAAA,GAAZvF,YAAY;AA2OrB,eAAeA,YAAY;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}