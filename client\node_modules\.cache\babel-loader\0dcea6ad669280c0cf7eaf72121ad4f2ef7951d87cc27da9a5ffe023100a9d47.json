{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\CalendarPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Row, Col, Tabs, Tab } from 'react-bootstrap';\nimport RealCalendarView from '../components/RealCalendarView';\nimport PlannedCalendarView from '../components/PlannedCalendarView';\nimport CalendarAnalysis from '../components/CalendarAnalysis';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CalendarPage() {\n  _s();\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('real');\n  const [calendarKey, setCalendarKey] = useState(0);\n  const handleDateSelect = date => {\n    setSelectedDate(date);\n  };\n  const handleCalendarUpdate = () => {\n    // Forzar actualización de todos los calendarios\n    setCalendarKey(prev => prev + 1);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Sistema de Horarios Dual\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-primary\",\n              size: \"sm\",\n              onClick: () => setShowRealDayModal(true),\n              disabled: !selectedDate,\n              className: \"me-2\",\n              children: \"Registrar D\\xEDa Real\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-danger\",\n              size: \"sm\",\n              onClick: handleClearPlannedCalendar,\n              disabled: loading,\n              children: \"Limpiar Planificado\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Aplicar Patr\\xF3n a Rango Seleccionado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"mb-3\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de inicio\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : '',\n                  onChange: e => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                controlId: \"patternEndDate\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de fin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 74,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : '',\n                  onChange: e => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Seleccionar Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: selectedPatternId,\n              onChange: e => setSelectedPatternId(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Seleccionar patr\\xF3n...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this), allPatterns.map(pattern => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: pattern.id,\n                children: pattern.name\n              }, pattern.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleApplyPattern,\n            disabled: loading || !selectedPatternId || !selectionStartDate || !selectionEndDate,\n            children: loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => {\n              setSelectionStartDate(null);\n              setSelectionEndDate(null);\n              setSelectedPatternId('');\n              setError('');\n            },\n            className: \"ms-2\",\n            children: \"Limpiar Selecci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Aplicar Patr\\xF3n a Rango Seleccionado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"mb-3\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de inicio\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : '',\n                  onChange: e => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de fin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : '',\n                  onChange: e => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Seleccionar Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: selectedPatternId,\n              onChange: e => setSelectedPatternId(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Seleccionar patr\\xF3n...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), allPatterns.map(pattern => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: pattern.id,\n                children: pattern.name\n              }, pattern.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleApplyPattern,\n            disabled: loading || !selectedPatternId || !selectionStartDate || !selectionEndDate,\n            children: loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => {\n              setSelectionStartDate(null);\n              setSelectionEndDate(null);\n              setSelectedPatternId('');\n              setError('');\n            },\n            className: \"ms-2\",\n            children: \"Limpiar Selecci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n      activeKey: activeTab,\n      onSelect: setActiveTab,\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"calendar\",\n        title: \"\\uD83D\\uDCC5 Calendario Dual\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dual-calendar\",\n          children: /*#__PURE__*/_jsxDEV(DualCalendarView, {\n            onDateSelect: handleDateSelect,\n            selectedDate: selectedDate\n          }, calendarKey, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"analysis\",\n        title: \"\\uD83D\\uDCCA An\\xE1lisis\",\n        children: /*#__PURE__*/_jsxDEV(CalendarAnalysis, {\n          selectedDate: selectedDate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RealCalendarManager, {\n      show: showRealDayModal,\n      onHide: () => setShowRealDayModal(false),\n      selectedDate: selectedDate,\n      onDayUpdated: handleRealDayUpdated\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n}\n_s(CalendarPage, \"wzK0Ju2Td/fRUgmzeDqwaKhFGtc=\");\n_c = CalendarPage;\nexport default CalendarPage;\nvar _c;\n$RefreshReg$(_c, \"CalendarPage\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Row", "Col", "Tabs", "Tab", "RealCalendarView", "PlannedCalendarView", "CalendarAnalysis", "jsxDEV", "_jsxDEV", "CalendarPage", "_s", "selectedDate", "setSelectedDate", "Date", "activeTab", "setActiveTab", "calendarKey", "setCalendarKey", "handleDateSelect", "date", "handleCalendarUpdate", "prev", "fluid", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON>", "variant", "size", "onClick", "setShowRealDayModal", "disabled", "handleClearPlannedCalendar", "loading", "error", "<PERSON><PERSON>", "Form", "md", "Group", "Label", "Control", "type", "value", "selectionStartDate", "toISOString", "split", "onChange", "e", "setSelectionStartDate", "target", "controlId", "selectionEndDate", "setSelectionEndDate", "Select", "selectedPatternId", "setSelectedPatternId", "allPatterns", "map", "pattern", "id", "name", "handleApplyPattern", "setError", "active<PERSON><PERSON>", "onSelect", "eventKey", "title", "DualCalendarView", "onDateSelect", "RealCalendarManager", "show", "showRealDayModal", "onHide", "onDayUpdated", "handleRealDayUpdated", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/CalendarPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Container, Row, Col, Tabs, Tab } from 'react-bootstrap';\nimport RealCalendarView from '../components/RealCalendarView';\nimport PlannedCalendarView from '../components/PlannedCalendarView';\nimport CalendarAnalysis from '../components/CalendarAnalysis';\n\nfunction CalendarPage() {\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('real');\n  const [calendarKey, setCalendarKey] = useState(0);\n\n  const handleDateSelect = (date) => {\n    setSelectedDate(date);\n  };\n\n  const handleCalendarUpdate = () => {\n    // Forzar actualización de todos los calendarios\n    setCalendarKey(prev => prev + 1);\n  };\n\n\n\n  return (\n    <Container fluid>\n      <Row className=\"mb-3\">\n        <Col>\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <h2>Sistema de Horarios Dual</h2>\n            <div>\n              <Button\n                variant=\"outline-primary\"\n                size=\"sm\"\n                onClick={() => setShowRealDayModal(true)}\n                disabled={!selectedDate}\n                className=\"me-2\"\n              >\n                Registrar Día Real\n              </Button>\n              <Button\n                variant=\"outline-danger\"\n                size=\"sm\"\n                onClick={handleClearPlannedCalendar}\n                disabled={loading}\n              >\n                Limpiar Planificado\n              </Button>\n            </div>\n          </div>\n        </Col>\n      </Row>\n\n      <Row className=\"mb-3\">\n        <Col>\n          <h3>Aplicar Patrón a Rango Seleccionado</h3>\n          {error && (\n            <Alert variant=\"danger\" className=\"mb-3\">\n              {error}\n            </Alert>\n          )}\n          <Form>\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Fecha de inicio</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : ''}\n                    onChange={(e) => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group controlId=\"patternEndDate\">\n                  <Form.Label>Fecha de fin</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : ''}\n                    onChange={(e) => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Seleccionar Patrón</Form.Label>\n              <Form.Select\n                value={selectedPatternId}\n                onChange={(e) => setSelectedPatternId(e.target.value)}\n              >\n                <option value=\"\">Seleccionar patrón...</option>\n                {allPatterns.map((pattern) => (\n                  <option key={pattern.id} value={pattern.id}>\n                    {pattern.name}\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n\n            <Button\n              variant=\"primary\"\n              onClick={handleApplyPattern}\n              disabled={loading || !selectedPatternId || !selectionStartDate || !selectionEndDate}\n            >\n              {loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'}\n            </Button>\n            <Button\n              variant=\"secondary\"\n              onClick={() => {\n                setSelectionStartDate(null);\n                setSelectionEndDate(null);\n                setSelectedPatternId('');\n                setError('');\n              }}\n              className=\"ms-2\"\n            >\n              Limpiar Selección\n            </Button>\n          </Form>\n        </Col>\n      </Row>\n\n      <Row className=\"mb-3\">\n        <Col>\n          <h3>Aplicar Patrón a Rango Seleccionado</h3>\n          {error && (\n            <Alert variant=\"danger\" className=\"mb-3\">\n              {error}\n            </Alert>\n          )}\n          <Form>\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Fecha de inicio</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : ''}\n                    onChange={(e) => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Fecha de fin</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : ''}\n                    onChange={(e) => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Seleccionar Patrón</Form.Label>\n              <Form.Select\n                value={selectedPatternId}\n                onChange={(e) => setSelectedPatternId(e.target.value)}\n              >\n                <option value=\"\">Seleccionar patrón...</option>\n                {allPatterns.map((pattern) => (\n                  <option key={pattern.id} value={pattern.id}>\n                    {pattern.name}\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n\n            <Button\n              variant=\"primary\"\n              onClick={handleApplyPattern}\n              disabled={loading || !selectedPatternId || !selectionStartDate || !selectionEndDate}\n            >\n              {loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'}\n            </Button>\n            <Button\n              variant=\"secondary\"\n              onClick={() => {\n                setSelectionStartDate(null);\n                setSelectionEndDate(null);\n                setSelectedPatternId('');\n                setError('');\n              }}\n              className=\"ms-2\"\n            >\n              Limpiar Selección\n            </Button>\n          </Form>\n        </Col>\n      </Row>\n\n      {error && (\n        <Row className=\"mb-3\">\n          <Col>\n            <Alert variant=\"danger\">{error}</Alert>\n          </Col>\n        </Row>\n      )}\n\n      <Tabs activeKey={activeTab} onSelect={setActiveTab} className=\"mb-3\">\n        <Tab eventKey=\"calendar\" title=\"📅 Calendario Dual\">\n          <div className=\"dual-calendar\">\n            <DualCalendarView\n              key={calendarKey}\n              onDateSelect={handleDateSelect}\n              selectedDate={selectedDate}\n            />\n          </div>\n        </Tab>\n\n        <Tab eventKey=\"analysis\" title=\"📊 Análisis\">\n          <CalendarAnalysis selectedDate={selectedDate} />\n        </Tab>\n      </Tabs>\n\n      {/* Modal para gestión de días reales */}\n      <RealCalendarManager\n        show={showRealDayModal}\n        onHide={() => setShowRealDayModal(false)}\n        selectedDate={selectedDate}\n        onDayUpdated={handleRealDayUpdated}\n      />\n\n\n    </Container>\n  );\n}\n\nexport default CalendarPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,QAAQ,iBAAiB;AAChE,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,gBAAgB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,IAAIe,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EAEjD,MAAMoB,gBAAgB,GAAIC,IAAI,IAAK;IACjCP,eAAe,CAACO,IAAI,CAAC;EACvB,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAH,cAAc,CAACI,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;EAClC,CAAC;EAID,oBACEb,OAAA,CAACT,SAAS;IAACuB,KAAK;IAAAC,QAAA,gBACdf,OAAA,CAACR,GAAG;MAACwB,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBf,OAAA,CAACP,GAAG;QAAAsB,QAAA,eACFf,OAAA;UAAKgB,SAAS,EAAC,mDAAmD;UAAAD,QAAA,gBAChEf,OAAA;YAAAe,QAAA,EAAI;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjCpB,OAAA;YAAAe,QAAA,gBACEf,OAAA,CAACqB,MAAM;cACLC,OAAO,EAAC,iBAAiB;cACzBC,IAAI,EAAC,IAAI;cACTC,OAAO,EAAEA,CAAA,KAAMC,mBAAmB,CAAC,IAAI,CAAE;cACzCC,QAAQ,EAAE,CAACvB,YAAa;cACxBa,SAAS,EAAC,MAAM;cAAAD,QAAA,EACjB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpB,OAAA,CAACqB,MAAM;cACLC,OAAO,EAAC,gBAAgB;cACxBC,IAAI,EAAC,IAAI;cACTC,OAAO,EAAEG,0BAA2B;cACpCD,QAAQ,EAAEE,OAAQ;cAAAb,QAAA,EACnB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpB,OAAA,CAACR,GAAG;MAACwB,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBf,OAAA,CAACP,GAAG;QAAAsB,QAAA,gBACFf,OAAA;UAAAe,QAAA,EAAI;QAAmC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC3CS,KAAK,iBACJ7B,OAAA,CAAC8B,KAAK;UAACR,OAAO,EAAC,QAAQ;UAACN,SAAS,EAAC,MAAM;UAAAD,QAAA,EACrCc;QAAK;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eACDpB,OAAA,CAAC+B,IAAI;UAAAhB,QAAA,gBACHf,OAAA,CAACR,GAAG;YAAAuB,QAAA,gBACFf,OAAA,CAACP,GAAG;cAACuC,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACTf,OAAA,CAAC+B,IAAI,CAACE,KAAK;gBAACjB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1Bf,OAAA,CAAC+B,IAAI,CAACG,KAAK;kBAAAnB,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxCpB,OAAA,CAAC+B,IAAI,CAACI,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEC,kBAAkB,GAAGA,kBAAkB,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;kBAChFC,QAAQ,EAAGC,CAAC,IAAKC,qBAAqB,CAACD,CAAC,CAACE,MAAM,CAACP,KAAK,GAAG,IAAIhC,IAAI,CAACqC,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC,GAAG,IAAI;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNpB,OAAA,CAACP,GAAG;cAACuC,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACTf,OAAA,CAAC+B,IAAI,CAACE,KAAK;gBAACY,SAAS,EAAC,gBAAgB;gBAAA9B,QAAA,gBACpCf,OAAA,CAAC+B,IAAI,CAACG,KAAK;kBAAAnB,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCpB,OAAA,CAAC+B,IAAI,CAACI,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAES,gBAAgB,GAAGA,gBAAgB,CAACP,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;kBAC5EC,QAAQ,EAAGC,CAAC,IAAKK,mBAAmB,CAACL,CAAC,CAACE,MAAM,CAACP,KAAK,GAAG,IAAIhC,IAAI,CAACqC,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC,GAAG,IAAI;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpB,OAAA,CAAC+B,IAAI,CAACE,KAAK;YAACjB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1Bf,OAAA,CAAC+B,IAAI,CAACG,KAAK;cAAAnB,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3CpB,OAAA,CAAC+B,IAAI,CAACiB,MAAM;cACVX,KAAK,EAAEY,iBAAkB;cACzBR,QAAQ,EAAGC,CAAC,IAAKQ,oBAAoB,CAACR,CAAC,CAACE,MAAM,CAACP,KAAK,CAAE;cAAAtB,QAAA,gBAEtDf,OAAA;gBAAQqC,KAAK,EAAC,EAAE;gBAAAtB,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9C+B,WAAW,CAACC,GAAG,CAAEC,OAAO,iBACvBrD,OAAA;gBAAyBqC,KAAK,EAAEgB,OAAO,CAACC,EAAG;gBAAAvC,QAAA,EACxCsC,OAAO,CAACE;cAAI,GADFF,OAAO,CAACC,EAAE;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEbpB,OAAA,CAACqB,MAAM;YACLC,OAAO,EAAC,SAAS;YACjBE,OAAO,EAAEgC,kBAAmB;YAC5B9B,QAAQ,EAAEE,OAAO,IAAI,CAACqB,iBAAiB,IAAI,CAACX,kBAAkB,IAAI,CAACQ,gBAAiB;YAAA/B,QAAA,EAEnFa,OAAO,GAAG,cAAc,GAAG;UAAwB;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACTpB,OAAA,CAACqB,MAAM;YACLC,OAAO,EAAC,WAAW;YACnBE,OAAO,EAAEA,CAAA,KAAM;cACbmB,qBAAqB,CAAC,IAAI,CAAC;cAC3BI,mBAAmB,CAAC,IAAI,CAAC;cACzBG,oBAAoB,CAAC,EAAE,CAAC;cACxBO,QAAQ,CAAC,EAAE,CAAC;YACd,CAAE;YACFzC,SAAS,EAAC,MAAM;YAAAD,QAAA,EACjB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpB,OAAA,CAACR,GAAG;MAACwB,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBf,OAAA,CAACP,GAAG;QAAAsB,QAAA,gBACFf,OAAA;UAAAe,QAAA,EAAI;QAAmC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC3CS,KAAK,iBACJ7B,OAAA,CAAC8B,KAAK;UAACR,OAAO,EAAC,QAAQ;UAACN,SAAS,EAAC,MAAM;UAAAD,QAAA,EACrCc;QAAK;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eACDpB,OAAA,CAAC+B,IAAI;UAAAhB,QAAA,gBACHf,OAAA,CAACR,GAAG;YAAAuB,QAAA,gBACFf,OAAA,CAACP,GAAG;cAACuC,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACTf,OAAA,CAAC+B,IAAI,CAACE,KAAK;gBAACjB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1Bf,OAAA,CAAC+B,IAAI,CAACG,KAAK;kBAAAnB,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxCpB,OAAA,CAAC+B,IAAI,CAACI,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEC,kBAAkB,GAAGA,kBAAkB,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;kBAChFC,QAAQ,EAAGC,CAAC,IAAKC,qBAAqB,CAACD,CAAC,CAACE,MAAM,CAACP,KAAK,GAAG,IAAIhC,IAAI,CAACqC,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC,GAAG,IAAI;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNpB,OAAA,CAACP,GAAG;cAACuC,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACTf,OAAA,CAAC+B,IAAI,CAACE,KAAK;gBAACjB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1Bf,OAAA,CAAC+B,IAAI,CAACG,KAAK;kBAAAnB,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCpB,OAAA,CAAC+B,IAAI,CAACI,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAES,gBAAgB,GAAGA,gBAAgB,CAACP,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;kBAC5EC,QAAQ,EAAGC,CAAC,IAAKK,mBAAmB,CAACL,CAAC,CAACE,MAAM,CAACP,KAAK,GAAG,IAAIhC,IAAI,CAACqC,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC,GAAG,IAAI;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpB,OAAA,CAAC+B,IAAI,CAACE,KAAK;YAACjB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1Bf,OAAA,CAAC+B,IAAI,CAACG,KAAK;cAAAnB,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3CpB,OAAA,CAAC+B,IAAI,CAACiB,MAAM;cACVX,KAAK,EAAEY,iBAAkB;cACzBR,QAAQ,EAAGC,CAAC,IAAKQ,oBAAoB,CAACR,CAAC,CAACE,MAAM,CAACP,KAAK,CAAE;cAAAtB,QAAA,gBAEtDf,OAAA;gBAAQqC,KAAK,EAAC,EAAE;gBAAAtB,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9C+B,WAAW,CAACC,GAAG,CAAEC,OAAO,iBACvBrD,OAAA;gBAAyBqC,KAAK,EAAEgB,OAAO,CAACC,EAAG;gBAAAvC,QAAA,EACxCsC,OAAO,CAACE;cAAI,GADFF,OAAO,CAACC,EAAE;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEbpB,OAAA,CAACqB,MAAM;YACLC,OAAO,EAAC,SAAS;YACjBE,OAAO,EAAEgC,kBAAmB;YAC5B9B,QAAQ,EAAEE,OAAO,IAAI,CAACqB,iBAAiB,IAAI,CAACX,kBAAkB,IAAI,CAACQ,gBAAiB;YAAA/B,QAAA,EAEnFa,OAAO,GAAG,cAAc,GAAG;UAAwB;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACTpB,OAAA,CAACqB,MAAM;YACLC,OAAO,EAAC,WAAW;YACnBE,OAAO,EAAEA,CAAA,KAAM;cACbmB,qBAAqB,CAAC,IAAI,CAAC;cAC3BI,mBAAmB,CAAC,IAAI,CAAC;cACzBG,oBAAoB,CAAC,EAAE,CAAC;cACxBO,QAAQ,CAAC,EAAE,CAAC;YACd,CAAE;YACFzC,SAAS,EAAC,MAAM;YAAAD,QAAA,EACjB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELS,KAAK,iBACJ7B,OAAA,CAACR,GAAG;MAACwB,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBf,OAAA,CAACP,GAAG;QAAAsB,QAAA,eACFf,OAAA,CAAC8B,KAAK;UAACR,OAAO,EAAC,QAAQ;UAAAP,QAAA,EAAEc;QAAK;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDpB,OAAA,CAACN,IAAI;MAACgE,SAAS,EAAEpD,SAAU;MAACqD,QAAQ,EAAEpD,YAAa;MAACS,SAAS,EAAC,MAAM;MAAAD,QAAA,gBAClEf,OAAA,CAACL,GAAG;QAACiE,QAAQ,EAAC,UAAU;QAACC,KAAK,EAAC,8BAAoB;QAAA9C,QAAA,eACjDf,OAAA;UAAKgB,SAAS,EAAC,eAAe;UAAAD,QAAA,eAC5Bf,OAAA,CAAC8D,gBAAgB;YAEfC,YAAY,EAAErD,gBAAiB;YAC/BP,YAAY,EAAEA;UAAa,GAFtBK,WAAW;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA,CAACL,GAAG;QAACiE,QAAQ,EAAC,UAAU;QAACC,KAAK,EAAC,0BAAa;QAAA9C,QAAA,eAC1Cf,OAAA,CAACF,gBAAgB;UAACK,YAAY,EAAEA;QAAa;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPpB,OAAA,CAACgE,mBAAmB;MAClBC,IAAI,EAAEC,gBAAiB;MACvBC,MAAM,EAAEA,CAAA,KAAM1C,mBAAmB,CAAC,KAAK,CAAE;MACzCtB,YAAY,EAAEA,YAAa;MAC3BiE,YAAY,EAAEC;IAAqB;MAAApD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGO,CAAC;AAEhB;AAAClB,EAAA,CA5NQD,YAAY;AAAAqE,EAAA,GAAZrE,YAAY;AA8NrB,eAAeA,YAAY;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}