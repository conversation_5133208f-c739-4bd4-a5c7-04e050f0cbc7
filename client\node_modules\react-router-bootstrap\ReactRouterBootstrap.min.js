!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react"),require("react-router-dom")):"function"==typeof define&&define.amd?define(["react","react-router-dom"],t):"object"==typeof exports?exports.ReactRouterBootstrap=t(require("react"),require("react-router-dom")):e.<PERSON>actRouterBootstrap=t(e.<PERSON><PERSON>,e.<PERSON>actRouterDOM)}(self,((e,t)=>(()=>{var r={703:(e,t,r)=>{"use strict";var o=r(414);function n(){}function c(){}c.resetWarningCache=n,e.exports=function(){function e(e,t,r,n,c,i){if(i!==o){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:c,resetWarningCache:n};return r.PropTypes=r,r}},697:(e,t,r)=>{e.exports=r(703)()},414:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},787:t=>{"use strict";t.exports=e},944:e=>{"use strict";e.exports=t}},o={};function n(e){var t=o[e];if(void 0!==t)return t.exports;var c=o[e]={exports:{}};return r[e](c,c.exports,n),c.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var c={};return(()=>{"use strict";n.r(c),n.d(c,{LinkContainer:()=>y});var e=n(787),t=n.n(e),r=n(697),o=n.n(r),i=n(944),a=["children","onClick","replace","to","state","activeClassName","className","activeStyle","style","isActive"];function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,o)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach((function(t){u(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function u(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}var f=function(e){var r=e.children,o=e.onClick,n=e.replace,c=void 0!==n&&n,s=e.to,u=e.state,f=e.activeClassName,y=void 0===f?"active":f,b=e.className,m=e.activeStyle,O=e.style,d=e.isActive,v=function(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r,o,n={},c=Object.keys(e);for(o=0;o<c.length;o++)r=c[o],t.indexOf(r)>=0||(n[r]=e[r]);return n}(e,t);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(e);for(o=0;o<c.length;o++)r=c[o],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,a),j="object"===l(s)?s.pathname||"":s,g=(0,i.useNavigate)(),h=(0,i.useHref)("string"==typeof s?{pathname:s}:s),P=(0,i.useMatch)(j),S=(0,i.useLocation)(),T=t().Children.only(r),x=!!(d?"function"==typeof d?d(P,S):d:P);return t().cloneElement(T,p(p({},v),{},{className:[b,T.props.className,x?y:null].join(" ").trim(),style:x?p(p({},O),m):O,href:h,onClick:function(e){r.props.onClick&&r.props.onClick(e),o&&o(e),e.defaultPrevented||0!==e.button||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)||(e.preventDefault(),g(s,{replace:c,state:u}))}}))};f.propTypes={children:o().element.isRequired,onClick:o().func,replace:o().bool,to:o().oneOfType([o().string,o().object]).isRequired,state:o().object,className:o().string,activeClassName:o().string,style:o().objectOf(o().oneOfType([o().string,o().number])),activeStyle:o().objectOf(o().oneOfType([o().string,o().number])),isActive:o().oneOfType([o().func,o().bool])};const y=f})(),c})()));
//# sourceMappingURL=ReactRouterBootstrap.min.js.map