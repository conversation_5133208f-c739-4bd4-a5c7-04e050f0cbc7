import React, { useState, useEffect } from 'react';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css';
import { Container, Row, Col, Button, Form, Alert } from 'react-bootstrap';
import {
  applyPattern,
  getAllPatterns,
  getAllPlannedDays
} from '../services/api';

function TheoreticalCalendarPage() {
  const [date, setDate] = useState(new Date());
  const [selectionStartDate, setSelectionStartDate] = useState(null);
  const [selectionEndDate, setSelectionEndDate] = useState(null);
  const [allPatterns, setAllPatterns] = useState([]);
  const [selectedPatternId, setSelectedPatternId] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [plannedDays, setPlannedDays] = useState([]);

  useEffect(() => {
    loadAllPatterns();
    loadPlannedDays();
  }, []);

  useEffect(() => {
    loadPlannedDays();
  }, [date]); // Reload planned days when calendar view changes

  const loadAllPatterns = async () => {
    try {
      const patterns = await getAllPatterns();
      setAllPatterns(patterns);
    } catch (err) {
      console.error('Error loading patterns:', err);
      setError('Error cargando patrones.');
    }
  };

  const loadPlannedDays = async () => {
    try {
      const days = await getAllPlannedDays();
      setPlannedDays(days);
    } catch (err) {
      console.error('Error loading planned days:', err);
      setError('Error cargando días planificados.');
    }
  };

  const handleDateClick = (value) => {
    if (!selectionStartDate) {
      setSelectionStartDate(value);
      setSelectionEndDate(null);
    } else if (!selectionEndDate) {
      if (value < selectionStartDate) {
        setSelectionEndDate(selectionStartDate);
        setSelectionStartDate(value);
      } else {
        setSelectionEndDate(value);
      }
    } else {
      setSelectionStartDate(value);
      setSelectionEndDate(null);
    }
  };

  const handleApplyPattern = async () => {
    if (!selectionStartDate || !selectionEndDate || !selectedPatternId) {
      setError('Selecciona un rango de fechas y un patrón');
      return;
    }

    setLoading(true);
    setError('');
    try {
      const formattedStartDate = selectionStartDate.toISOString().split('T')[0];
      const formattedEndDate = selectionEndDate.toISOString().split('T')[0];
      await applyPattern(formattedStartDate, formattedEndDate, selectedPatternId);
      setSelectionStartDate(null);
      setSelectionEndDate(null);
      setSelectedPatternId('');
      loadPlannedDays(); // Refresh planned days after applying pattern
      alert('Patrón aplicado al calendario planificado exitosamente!');
    } catch (err) {
      console.error('Error applying pattern:', err);
      setError('Error aplicando el patrón: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const tileClassName = ({ date, view }) => {
    if (view === 'month') {
      // Highlight selected range for pattern application
      if (selectionStartDate && selectionEndDate) {
        if (date >= selectionStartDate && date <= selectionEndDate) {
          return 'selected-range';
        }
      } else if (selectionStartDate && date.toDateString() === selectionStartDate.toDateString()) {
        return 'selected-range-start';
      }

      // Highlight planned days
      const formattedDate = date.toISOString().split('T')[0];
      const plannedDay = plannedDays.find(day => day.date === formattedDate);
      if (plannedDay) {
        return 'planned-day'; // You'll need to define this CSS class
      }
    }
    return null;
  };

  return (
    <Container fluid>
      <Row className="justify-content-md-center">
        <Col md="auto">
          <h2>Calendario Teórico (Patrones Anuales)</h2>
          <Calendar
            onChange={setDate}
            value={date}
            onClickDay={handleDateClick}
            tileClassName={tileClassName}
          />
        </Col>
      </Row>

      <Row className="justify-content-md-center mt-3">
        <Col md="auto">
          <h3>Aplicar Patrón a Rango Seleccionado</h3>
          {error && (
            <Alert variant="danger" className="mb-3">
              {error}
            </Alert>
          )}
          <Form>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Fecha de inicio</Form.Label>
                  <Form.Control
                    type="date"
                    value={selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : ''}
                    onChange={(e) => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group controlId="patternEndDate">
                  <Form.Label>Fecha de fin</Form.Label>
                  <Form.Control
                    type="date"
                    value={selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : ''}
                    onChange={(e) => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Seleccionar Patrón</Form.Label>
              <Form.Select
                value={selectedPatternId}
                onChange={(e) => setSelectedPatternId(e.target.value)}
              >
                <option value="">Seleccionar patrón...</option>
                {allPatterns.map((pattern) => (
                  <option key={pattern.id} value={pattern.id}>
                    {pattern.name}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>

            {selectionStartDate && selectionEndDate && (
              <Alert variant="secondary">
                <strong>Rango seleccionado:</strong> {' '}
                {selectionStartDate.toLocaleDateString('es-ES')} - {selectionEndDate.toLocaleDateString('es-ES')}
                <br />
                <strong>Días:</strong> {Math.ceil((selectionEndDate - selectionStartDate) / (1000 * 60 * 60 * 24)) + 1}
              </Alert>
            )}

            <Button
              variant="primary"
              onClick={handleApplyPattern}
              disabled={loading || !selectedPatternId || !selectionStartDate || !selectionEndDate}
            >
              {loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'}
            </Button>
            <Button
              variant="secondary"
              onClick={() => {
                setSelectionStartDate(null);
                setSelectionEndDate(null);
                setSelectedPatternId('');
                setError('');
              }}
              className="ms-2"
            >
              Limpiar Selección
            </Button>
          </Form>
        </Col>
      </Row>
    </Container>
  );
}

export default TheoreticalCalendarPage;