{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\components\\\\PlannedCalendarView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Button, Alert, Table, Badge, Modal, Form } from 'react-bootstrap';\nimport { getAllPlannedDays, getAllPatterns, applyPattern, deletePlannedDay, clearPlannedCalendar } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction PlannedCalendarView({\n  onDateSelect,\n  selectedDate,\n  onCalendarUpdate\n}) {\n  _s();\n  const [plannedDays, setPlannedDays] = useState([]);\n  const [patterns, setPatterns] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPatternModal, setShowPatternModal] = useState(false);\n\n  // Form state for pattern application\n  const [selectedPatternId, setSelectedPatternId] = useState('');\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n  useEffect(() => {\n    loadPlannedDays();\n    loadPatterns();\n  }, []);\n  const loadPlannedDays = async () => {\n    try {\n      setLoading(true);\n      const days = await getAllPlannedDays();\n      setPlannedDays(days);\n      setError('');\n    } catch (err) {\n      console.error('Error loading planned days:', err);\n      setError('Error cargando días planificados: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadPatterns = async () => {\n    try {\n      const patternsData = await getAllPatterns();\n      setPatterns(patternsData);\n    } catch (err) {\n      console.error('Error loading patterns:', err);\n    }\n  };\n  const handleApplyPattern = async () => {\n    if (!selectedPatternId || !startDate || !endDate) {\n      setError('Todos los campos son obligatorios');\n      return;\n    }\n    try {\n      setLoading(true);\n      setError('');\n      await applyPattern(selectedPatternId, startDate, endDate);\n      await loadPlannedDays();\n      if (onCalendarUpdate) onCalendarUpdate();\n      setShowPatternModal(false);\n      resetPatternForm();\n    } catch (err) {\n      setError('Error aplicando patrón: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleClearCalendar = async () => {\n    if (window.confirm('¿Estás seguro de que quieres limpiar todo el calendario planificado?')) {\n      try {\n        setLoading(true);\n        await clearPlannedCalendar();\n        await loadPlannedDays();\n        if (onCalendarUpdate) onCalendarUpdate();\n        setError('');\n      } catch (err) {\n        setError('Error limpiando calendario: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  const handleDeleteDay = async dayId => {\n    if (window.confirm('¿Eliminar este día del calendario planificado?')) {\n      try {\n        setLoading(true);\n        await deletePlannedDay(dayId);\n        await loadPlannedDays();\n        if (onCalendarUpdate) onCalendarUpdate();\n      } catch (err) {\n        setError('Error eliminando día: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  const resetPatternForm = () => {\n    setSelectedPatternId('');\n    setStartDate('');\n    setEndDate('');\n  };\n  const getTotalHours = () => {\n    return (plannedDays || []).reduce((total, day) => total + (day.hours || 0), 0);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Calendario Te\\xF3rico (Planificado)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"success\",\n              onClick: () => setShowPatternModal(true),\n              className: \"me-2\",\n              disabled: loading,\n              children: \"\\uD83D\\uDCCB Aplicar Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-danger\",\n              onClick: handleClearCalendar,\n              disabled: loading,\n              children: \"\\uD83D\\uDDD1\\uFE0F Limpiar Todo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          onClose: () => setError(''),\n          dismissible: true,\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"calendar-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Calendario Planificado\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), loading ? /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Cargando d\\xEDas planificados...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this) : (plannedDays || []).length > 0 ? /*#__PURE__*/_jsxDEV(Table, {\n            striped: true,\n            bordered: true,\n            hover: true,\n            responsive: true,\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Fecha\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Turno\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Horas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Patr\\xF3n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Acciones\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: (plannedDays || []).sort((a, b) => new Date(a.date) - new Date(b.date)).map(day => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: new Date(day.date).toLocaleDateString('es-ES', {\n                    weekday: 'short',\n                    day: '2-digit',\n                    month: '2-digit',\n                    year: 'numeric'\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(Badge, {\n                    bg: \"info\",\n                    children: day.shiftName || 'Sin turno'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [day.hours, \"h\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: day.patternName || 'Manual'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-danger\",\n                    size: \"sm\",\n                    onClick: () => handleDeleteDay(day.id),\n                    children: \"\\uD83D\\uDDD1\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 25\n                }, this)]\n              }, day.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"info\",\n            children: \"No hay d\\xEDas planificados. \\xA1Aplica un patr\\xF3n para generar el calendario te\\xF3rico!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 4,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"calendar-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Estad\\xEDsticas Planificadas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total de d\\xEDas planificados:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), \" \", (plannedDays || []).length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total de horas:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), \" \", getTotalHours().toFixed(1), \"h\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pattern-info mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Patrones disponibles:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), patterns.length > 0 ? /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"list-unstyled\",\n            children: patterns.map(pattern => /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"mb-1\",\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: pattern.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 23\n                }, this), \" (\", pattern.days.length, \" d\\xEDas)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 21\n              }, this)\n            }, pattern.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-muted\",\n            children: \"No hay patrones creados\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPatternModal,\n      onHide: () => setShowPatternModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Aplicar Patr\\xF3n al Calendario Te\\xF3rico\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          onSubmit: e => e.preventDefault(),\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Patr\\xF3n a aplicar *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: selectedPatternId,\n              onChange: e => setSelectedPatternId(e.target.value),\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Selecciona un patr\\xF3n...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this), patterns.map(pattern => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: pattern.id,\n                children: [pattern.name, \" (\", pattern.days.length, \" d\\xEDas, \", pattern.days.reduce((total, day) => total + (day.hours || 0), 0), \"h total)\"]\n              }, pattern.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), patterns.length === 0 && /*#__PURE__*/_jsxDEV(Form.Text, {\n              className: \"text-muted\",\n              children: \"No hay patrones disponibles. Crea patrones en la secci\\xF3n de Patrones.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de inicio *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: startDate,\n                  onChange: e => setStartDate(e.target.value),\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de fin *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: endDate,\n                  onChange: e => setEndDate(e.target.value),\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), selectedPatternId && patterns.find(p => p.id === selectedPatternId) && /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Vista previa del patr\\xF3n seleccionado:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"mb-0\",\n              children: patterns.find(p => p.id === selectedPatternId).days.map((day, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [\"D\\xEDa \", day.dayNumber, \":\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 23\n                }, this), \" \", day.shiftName, \" (\", day.hours, \"h)\"]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowPatternModal(false),\n          children: \"Cancelar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleApplyPattern,\n          disabled: loading || !selectedPatternId || !startDate || !endDate,\n          children: loading ? 'Aplicando...' : 'Aplicar Patrón'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n}\n_s(PlannedCalendarView, \"bHLOIOTv80Vusyuq5JzquYOe2YY=\");\n_c = PlannedCalendarView;\nexport default PlannedCalendarView;\nvar _c;\n$RefreshReg$(_c, \"PlannedCalendarView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "<PERSON><PERSON>", "<PERSON><PERSON>", "Table", "Badge", "Modal", "Form", "getAllPlannedDays", "getAllPatterns", "applyPattern", "deletePlannedDay", "clearPlannedCalendar", "jsxDEV", "_jsxDEV", "PlannedCalendarView", "onDateSelect", "selectedDate", "onCalendarUpdate", "_s", "plannedDays", "setPlannedDays", "patterns", "setPatterns", "loading", "setLoading", "error", "setError", "showPatternModal", "setShowPatternModal", "selectedPatternId", "setSelectedPatternId", "startDate", "setStartDate", "endDate", "setEndDate", "loadPlannedDays", "loadPatterns", "days", "err", "console", "message", "patternsData", "handleApplyPattern", "resetPatternForm", "handleClearCalendar", "window", "confirm", "handleDeleteDay", "dayId", "getTotalHours", "reduce", "total", "day", "hours", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "disabled", "onClose", "dismissible", "md", "length", "striped", "bordered", "hover", "responsive", "sort", "a", "b", "Date", "date", "map", "toLocaleDateString", "weekday", "month", "year", "bg", "shiftName", "patternName", "size", "id", "toFixed", "pattern", "name", "show", "onHide", "Header", "closeButton", "Title", "Body", "onSubmit", "e", "preventDefault", "Group", "Label", "Select", "value", "onChange", "target", "required", "Text", "Control", "type", "find", "p", "index", "dayNumber", "Footer", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/components/PlannedCalendarView.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Button, Alert, Table, Badge, Modal, Form } from 'react-bootstrap';\nimport {\n  getAllPlannedDays,\n  getAllPatterns,\n  applyPattern,\n  deletePlannedDay,\n  clearPlannedCalendar\n} from '../services/api';\n\nfunction PlannedCalendarView({ onDateSelect, selectedDate, onCalendarUpdate }) {\n  const [plannedDays, setPlannedDays] = useState([]);\n  const [patterns, setPatterns] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPatternModal, setShowPatternModal] = useState(false);\n\n  // Form state for pattern application\n  const [selectedPatternId, setSelectedPatternId] = useState('');\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n\n  useEffect(() => {\n    loadPlannedDays();\n    loadPatterns();\n  }, []);\n\n  const loadPlannedDays = async () => {\n    try {\n      setLoading(true);\n      const days = await getAllPlannedDays();\n      setPlannedDays(days);\n      setError('');\n    } catch (err) {\n      console.error('Error loading planned days:', err);\n      setError('Error cargando días planificados: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadPatterns = async () => {\n    try {\n      const patternsData = await getAllPatterns();\n      setPatterns(patternsData);\n    } catch (err) {\n      console.error('Error loading patterns:', err);\n    }\n  };\n\n  const handleApplyPattern = async () => {\n    if (!selectedPatternId || !startDate || !endDate) {\n      setError('Todos los campos son obligatorios');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError('');\n\n      await applyPattern(selectedPatternId, startDate, endDate);\n      await loadPlannedDays();\n      if (onCalendarUpdate) onCalendarUpdate();\n      setShowPatternModal(false);\n      resetPatternForm();\n    } catch (err) {\n      setError('Error aplicando patrón: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleClearCalendar = async () => {\n    if (window.confirm('¿Estás seguro de que quieres limpiar todo el calendario planificado?')) {\n      try {\n        setLoading(true);\n        await clearPlannedCalendar();\n        await loadPlannedDays();\n        if (onCalendarUpdate) onCalendarUpdate();\n        setError('');\n      } catch (err) {\n        setError('Error limpiando calendario: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  const handleDeleteDay = async (dayId) => {\n    if (window.confirm('¿Eliminar este día del calendario planificado?')) {\n      try {\n        setLoading(true);\n        await deletePlannedDay(dayId);\n        await loadPlannedDays();\n        if (onCalendarUpdate) onCalendarUpdate();\n      } catch (err) {\n        setError('Error eliminando día: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  const resetPatternForm = () => {\n    setSelectedPatternId('');\n    setStartDate('');\n    setEndDate('');\n  };\n\n  const getTotalHours = () => {\n    return (plannedDays || []).reduce((total, day) => total + (day.hours || 0), 0);\n  };\n\n  return (\n    <div>\n      <Row className=\"mb-3\">\n        <Col>\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <h4>Calendario Teórico (Planificado)</h4>\n            <div>\n              <Button\n                variant=\"success\"\n                onClick={() => setShowPatternModal(true)}\n                className=\"me-2\"\n                disabled={loading}\n              >\n                📋 Aplicar Patrón\n              </Button>\n              <Button\n                variant=\"outline-danger\"\n                onClick={handleClearCalendar}\n                disabled={loading}\n              >\n                🗑️ Limpiar Todo\n              </Button>\n            </div>\n          </div>\n        </Col>\n      </Row>\n\n      {error && (\n        <Row className=\"mb-3\">\n          <Col>\n            <Alert variant=\"danger\" onClose={() => setError('')} dismissible>\n              {error}\n            </Alert>\n          </Col>\n        </Row>\n      )}\n\n      <Row className=\"mb-3\">\n        <Col md={8}>\n          <div className=\"calendar-container\">\n            <h5>Calendario Planificado</h5>\n            {loading ? (\n              <p>Cargando días planificados...</p>\n            ) : (plannedDays || []).length > 0 ? (\n              <Table striped bordered hover responsive>\n                <thead>\n                  <tr>\n                    <th>Fecha</th>\n                    <th>Turno</th>\n                    <th>Horas</th>\n                    <th>Patrón</th>\n                    <th>Acciones</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {(plannedDays || [])\n                    .sort((a, b) => new Date(a.date) - new Date(b.date))\n                    .map(day => (\n                      <tr key={day.id}>\n                        <td>\n                          {new Date(day.date).toLocaleDateString('es-ES', {\n                            weekday: 'short',\n                            day: '2-digit',\n                            month: '2-digit',\n                            year: 'numeric'\n                          })}\n                        </td>\n                        <td>\n                          <Badge bg=\"info\">\n                            {day.shiftName || 'Sin turno'}\n                          </Badge>\n                        </td>\n                        <td><strong>{day.hours}h</strong></td>\n                        <td>\n                          <small className=\"text-muted\">\n                            {day.patternName || 'Manual'}\n                          </small>\n                        </td>\n                        <td>\n                          <Button\n                            variant=\"outline-danger\"\n                            size=\"sm\"\n                            onClick={() => handleDeleteDay(day.id)}\n                          >\n                            🗑️\n                          </Button>\n                        </td>\n                      </tr>\n                    ))}\n                </tbody>\n              </Table>\n            ) : (\n              <Alert variant=\"info\">\n                No hay días planificados. ¡Aplica un patrón para generar el calendario teórico!\n              </Alert>\n            )}\n          </div>\n        </Col>\n        <Col md={4}>\n          <div className=\"calendar-stats\">\n            <h5>Estadísticas Planificadas</h5>\n            <div className=\"stat-item\">\n              <strong>Total de días planificados:</strong> {(plannedDays || []).length}\n            </div>\n            <div className=\"stat-item\">\n              <strong>Total de horas:</strong> {getTotalHours().toFixed(1)}h\n            </div>\n          </div>\n\n          <div className=\"pattern-info mt-3\">\n            <h6>Patrones disponibles:</h6>\n            {patterns.length > 0 ? (\n              <ul className=\"list-unstyled\">\n                {patterns.map(pattern => (\n                  <li key={pattern.id} className=\"mb-1\">\n                    <small>\n                      <strong>{pattern.name}</strong> ({pattern.days.length} días)\n                    </small>\n                  </li>\n                ))}\n              </ul>\n            ) : (\n              <p className=\"text-muted\">No hay patrones creados</p>\n            )}\n          </div>\n        </Col>\n      </Row>\n\n      {/* Modal para aplicar patrón */}\n      <Modal show={showPatternModal} onHide={() => setShowPatternModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>Aplicar Patrón al Calendario Teórico</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <Form onSubmit={(e) => e.preventDefault()}>\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Patrón a aplicar *</Form.Label>\n              <Form.Select\n                value={selectedPatternId}\n                onChange={(e) => setSelectedPatternId(e.target.value)}\n                required\n              >\n                <option value=\"\">Selecciona un patrón...</option>\n                {patterns.map(pattern => (\n                  <option key={pattern.id} value={pattern.id}>\n                    {pattern.name} ({pattern.days.length} días, {pattern.days.reduce((total, day) => total + (day.hours || 0), 0)}h total)\n                  </option>\n                ))}\n              </Form.Select>\n              {patterns.length === 0 && (\n                <Form.Text className=\"text-muted\">\n                  No hay patrones disponibles. Crea patrones en la sección de Patrones.\n                </Form.Text>\n              )}\n            </Form.Group>\n\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Fecha de inicio *</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={startDate}\n                    onChange={(e) => setStartDate(e.target.value)}\n                    required\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Fecha de fin *</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={endDate}\n                    onChange={(e) => setEndDate(e.target.value)}\n                    required\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n\n            {selectedPatternId && patterns.find(p => p.id === selectedPatternId) && (\n              <Alert variant=\"info\">\n                <h6>Vista previa del patrón seleccionado:</h6>\n                <ul className=\"mb-0\">\n                  {patterns.find(p => p.id === selectedPatternId).days.map((day, index) => (\n                    <li key={index}>\n                      <strong>Día {day.dayNumber}:</strong> {day.shiftName} ({day.hours}h)\n                    </li>\n                  ))}\n                </ul>\n              </Alert>\n            )}\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowPatternModal(false)}>\n            Cancelar\n          </Button>\n          <Button\n            variant=\"primary\"\n            onClick={handleApplyPattern}\n            disabled={loading || !selectedPatternId || !startDate || !endDate}\n          >\n            {loading ? 'Aplicando...' : 'Aplicar Patrón'}\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </div>\n  );\n}\n\nexport default PlannedCalendarView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AACpF,SACEC,iBAAiB,EACjBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,oBAAoB,QACf,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,SAASC,mBAAmBA,CAAC;EAAEC,YAAY;EAAEC,YAAY;EAAEC;AAAiB,CAAC,EAAE;EAAAC,EAAA;EAC7E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACgC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACdqC,eAAe,CAAC,CAAC;IACjBC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMa,IAAI,GAAG,MAAM9B,iBAAiB,CAAC,CAAC;MACtCa,cAAc,CAACiB,IAAI,CAAC;MACpBX,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOY,GAAG,EAAE;MACZC,OAAO,CAACd,KAAK,CAAC,6BAA6B,EAAEa,GAAG,CAAC;MACjDZ,QAAQ,CAAC,oCAAoC,GAAGY,GAAG,CAACE,OAAO,CAAC;IAC9D,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMY,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMK,YAAY,GAAG,MAAMjC,cAAc,CAAC,CAAC;MAC3Cc,WAAW,CAACmB,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAOH,GAAG,EAAE;MACZC,OAAO,CAACd,KAAK,CAAC,yBAAyB,EAAEa,GAAG,CAAC;IAC/C;EACF,CAAC;EAED,MAAMI,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACb,iBAAiB,IAAI,CAACE,SAAS,IAAI,CAACE,OAAO,EAAE;MAChDP,QAAQ,CAAC,mCAAmC,CAAC;MAC7C;IACF;IAEA,IAAI;MACFF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMjB,YAAY,CAACoB,iBAAiB,EAAEE,SAAS,EAAEE,OAAO,CAAC;MACzD,MAAME,eAAe,CAAC,CAAC;MACvB,IAAIlB,gBAAgB,EAAEA,gBAAgB,CAAC,CAAC;MACxCW,mBAAmB,CAAC,KAAK,CAAC;MAC1Be,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOL,GAAG,EAAE;MACZZ,QAAQ,CAAC,0BAA0B,GAAGY,GAAG,CAACE,OAAO,CAAC;IACpD,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAIC,MAAM,CAACC,OAAO,CAAC,sEAAsE,CAAC,EAAE;MAC1F,IAAI;QACFtB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMb,oBAAoB,CAAC,CAAC;QAC5B,MAAMwB,eAAe,CAAC,CAAC;QACvB,IAAIlB,gBAAgB,EAAEA,gBAAgB,CAAC,CAAC;QACxCS,QAAQ,CAAC,EAAE,CAAC;MACd,CAAC,CAAC,OAAOY,GAAG,EAAE;QACZZ,QAAQ,CAAC,8BAA8B,GAAGY,GAAG,CAACE,OAAO,CAAC;MACxD,CAAC,SAAS;QACRhB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;EAED,MAAMuB,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,IAAIH,MAAM,CAACC,OAAO,CAAC,gDAAgD,CAAC,EAAE;MACpE,IAAI;QACFtB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMd,gBAAgB,CAACsC,KAAK,CAAC;QAC7B,MAAMb,eAAe,CAAC,CAAC;QACvB,IAAIlB,gBAAgB,EAAEA,gBAAgB,CAAC,CAAC;MAC1C,CAAC,CAAC,OAAOqB,GAAG,EAAE;QACZZ,QAAQ,CAAC,wBAAwB,GAAGY,GAAG,CAACE,OAAO,CAAC;MAClD,CAAC,SAAS;QACRhB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;EAED,MAAMmB,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bb,oBAAoB,CAAC,EAAE,CAAC;IACxBE,YAAY,CAAC,EAAE,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;EAChB,CAAC;EAED,MAAMe,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAO,CAAC9B,WAAW,IAAI,EAAE,EAAE+B,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAKD,KAAK,IAAIC,GAAG,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAChF,CAAC;EAED,oBACExC,OAAA;IAAAyC,QAAA,gBACEzC,OAAA,CAACd,GAAG;MAACwD,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBzC,OAAA,CAACb,GAAG;QAAAsD,QAAA,eACFzC,OAAA;UAAK0C,SAAS,EAAC,mDAAmD;UAAAD,QAAA,gBAChEzC,OAAA;YAAAyC,QAAA,EAAI;UAAgC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzC9C,OAAA;YAAAyC,QAAA,gBACEzC,OAAA,CAACZ,MAAM;cACL2D,OAAO,EAAC,SAAS;cACjBC,OAAO,EAAEA,CAAA,KAAMjC,mBAAmB,CAAC,IAAI,CAAE;cACzC2B,SAAS,EAAC,MAAM;cAChBO,QAAQ,EAAEvC,OAAQ;cAAA+B,QAAA,EACnB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9C,OAAA,CAACZ,MAAM;cACL2D,OAAO,EAAC,gBAAgB;cACxBC,OAAO,EAAEjB,mBAAoB;cAC7BkB,QAAQ,EAAEvC,OAAQ;cAAA+B,QAAA,EACnB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELlC,KAAK,iBACJZ,OAAA,CAACd,GAAG;MAACwD,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBzC,OAAA,CAACb,GAAG;QAAAsD,QAAA,eACFzC,OAAA,CAACX,KAAK;UAAC0D,OAAO,EAAC,QAAQ;UAACG,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAAC,EAAE,CAAE;UAACsC,WAAW;UAAAV,QAAA,EAC7D7B;QAAK;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED9C,OAAA,CAACd,GAAG;MAACwD,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBzC,OAAA,CAACb,GAAG;QAACiE,EAAE,EAAE,CAAE;QAAAX,QAAA,eACTzC,OAAA;UAAK0C,SAAS,EAAC,oBAAoB;UAAAD,QAAA,gBACjCzC,OAAA;YAAAyC,QAAA,EAAI;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC9BpC,OAAO,gBACNV,OAAA;YAAAyC,QAAA,EAAG;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,GAClC,CAACxC,WAAW,IAAI,EAAE,EAAE+C,MAAM,GAAG,CAAC,gBAChCrD,OAAA,CAACV,KAAK;YAACgE,OAAO;YAACC,QAAQ;YAACC,KAAK;YAACC,UAAU;YAAAhB,QAAA,gBACtCzC,OAAA;cAAAyC,QAAA,eACEzC,OAAA;gBAAAyC,QAAA,gBACEzC,OAAA;kBAAAyC,QAAA,EAAI;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACd9C,OAAA;kBAAAyC,QAAA,EAAI;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACd9C,OAAA;kBAAAyC,QAAA,EAAI;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACd9C,OAAA;kBAAAyC,QAAA,EAAI;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACf9C,OAAA;kBAAAyC,QAAA,EAAI;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR9C,OAAA;cAAAyC,QAAA,EACG,CAACnC,WAAW,IAAI,EAAE,EAChBoD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACF,CAAC,CAACG,IAAI,CAAC,GAAG,IAAID,IAAI,CAACD,CAAC,CAACE,IAAI,CAAC,CAAC,CACnDC,GAAG,CAACxB,GAAG,iBACNvC,OAAA;gBAAAyC,QAAA,gBACEzC,OAAA;kBAAAyC,QAAA,EACG,IAAIoB,IAAI,CAACtB,GAAG,CAACuB,IAAI,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;oBAC9CC,OAAO,EAAE,OAAO;oBAChB1B,GAAG,EAAE,SAAS;oBACd2B,KAAK,EAAE,SAAS;oBAChBC,IAAI,EAAE;kBACR,CAAC;gBAAC;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACL9C,OAAA;kBAAAyC,QAAA,eACEzC,OAAA,CAACT,KAAK;oBAAC6E,EAAE,EAAC,MAAM;oBAAA3B,QAAA,EACbF,GAAG,CAAC8B,SAAS,IAAI;kBAAW;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACL9C,OAAA;kBAAAyC,QAAA,eAAIzC,OAAA;oBAAAyC,QAAA,GAASF,GAAG,CAACC,KAAK,EAAC,GAAC;kBAAA;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtC9C,OAAA;kBAAAyC,QAAA,eACEzC,OAAA;oBAAO0C,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAC1BF,GAAG,CAAC+B,WAAW,IAAI;kBAAQ;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACL9C,OAAA;kBAAAyC,QAAA,eACEzC,OAAA,CAACZ,MAAM;oBACL2D,OAAO,EAAC,gBAAgB;oBACxBwB,IAAI,EAAC,IAAI;oBACTvB,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAACK,GAAG,CAACiC,EAAE,CAAE;oBAAA/B,QAAA,EACxC;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GA5BEP,GAAG,CAACiC,EAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6BX,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAER9C,OAAA,CAACX,KAAK;YAAC0D,OAAO,EAAC,MAAM;YAAAN,QAAA,EAAC;UAEtB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN9C,OAAA,CAACb,GAAG;QAACiE,EAAE,EAAE,CAAE;QAAAX,QAAA,gBACTzC,OAAA;UAAK0C,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC7BzC,OAAA;YAAAyC,QAAA,EAAI;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClC9C,OAAA;YAAK0C,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxBzC,OAAA;cAAAyC,QAAA,EAAQ;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,CAACxC,WAAW,IAAI,EAAE,EAAE+C,MAAM;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eACN9C,OAAA;YAAK0C,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxBzC,OAAA;cAAAyC,QAAA,EAAQ;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACV,aAAa,CAAC,CAAC,CAACqC,OAAO,CAAC,CAAC,CAAC,EAAC,GAC/D;UAAA;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9C,OAAA;UAAK0C,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAChCzC,OAAA;YAAAyC,QAAA,EAAI;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC7BtC,QAAQ,CAAC6C,MAAM,GAAG,CAAC,gBAClBrD,OAAA;YAAI0C,SAAS,EAAC,eAAe;YAAAD,QAAA,EAC1BjC,QAAQ,CAACuD,GAAG,CAACW,OAAO,iBACnB1E,OAAA;cAAqB0C,SAAS,EAAC,MAAM;cAAAD,QAAA,eACnCzC,OAAA;gBAAAyC,QAAA,gBACEzC,OAAA;kBAAAyC,QAAA,EAASiC,OAAO,CAACC;gBAAI;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,MAAE,EAAC4B,OAAO,CAAClD,IAAI,CAAC6B,MAAM,EAAC,WACxD;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC,GAHD4B,OAAO,CAACF,EAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIf,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,gBAEL9C,OAAA;YAAG0C,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACrD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9C,OAAA,CAACR,KAAK;MAACoF,IAAI,EAAE9D,gBAAiB;MAAC+D,MAAM,EAAEA,CAAA,KAAM9D,mBAAmB,CAAC,KAAK,CAAE;MAACwD,IAAI,EAAC,IAAI;MAAA9B,QAAA,gBAChFzC,OAAA,CAACR,KAAK,CAACsF,MAAM;QAACC,WAAW;QAAAtC,QAAA,eACvBzC,OAAA,CAACR,KAAK,CAACwF,KAAK;UAAAvC,QAAA,EAAC;QAAoC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eACf9C,OAAA,CAACR,KAAK,CAACyF,IAAI;QAAAxC,QAAA,eACTzC,OAAA,CAACP,IAAI;UAACyF,QAAQ,EAAGC,CAAC,IAAKA,CAAC,CAACC,cAAc,CAAC,CAAE;UAAA3C,QAAA,gBACxCzC,OAAA,CAACP,IAAI,CAAC4F,KAAK;YAAC3C,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BzC,OAAA,CAACP,IAAI,CAAC6F,KAAK;cAAA7C,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3C9C,OAAA,CAACP,IAAI,CAAC8F,MAAM;cACVC,KAAK,EAAExE,iBAAkB;cACzByE,QAAQ,EAAGN,CAAC,IAAKlE,oBAAoB,CAACkE,CAAC,CAACO,MAAM,CAACF,KAAK,CAAE;cACtDG,QAAQ;cAAAlD,QAAA,gBAERzC,OAAA;gBAAQwF,KAAK,EAAC,EAAE;gBAAA/C,QAAA,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAChDtC,QAAQ,CAACuD,GAAG,CAACW,OAAO,iBACnB1E,OAAA;gBAAyBwF,KAAK,EAAEd,OAAO,CAACF,EAAG;gBAAA/B,QAAA,GACxCiC,OAAO,CAACC,IAAI,EAAC,IAAE,EAACD,OAAO,CAAClD,IAAI,CAAC6B,MAAM,EAAC,YAAO,EAACqB,OAAO,CAAClD,IAAI,CAACa,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAKD,KAAK,IAAIC,GAAG,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC,UAChH;cAAA,GAFakC,OAAO,CAACF,EAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,EACbtC,QAAQ,CAAC6C,MAAM,KAAK,CAAC,iBACpBrD,OAAA,CAACP,IAAI,CAACmG,IAAI;cAAClD,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAElC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CACZ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eAEb9C,OAAA,CAACd,GAAG;YAAAuD,QAAA,gBACFzC,OAAA,CAACb,GAAG;cAACiE,EAAE,EAAE,CAAE;cAAAX,QAAA,eACTzC,OAAA,CAACP,IAAI,CAAC4F,KAAK;gBAAC3C,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BzC,OAAA,CAACP,IAAI,CAAC6F,KAAK;kBAAA7C,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1C9C,OAAA,CAACP,IAAI,CAACoG,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXN,KAAK,EAAEtE,SAAU;kBACjBuE,QAAQ,EAAGN,CAAC,IAAKhE,YAAY,CAACgE,CAAC,CAACO,MAAM,CAACF,KAAK,CAAE;kBAC9CG,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN9C,OAAA,CAACb,GAAG;cAACiE,EAAE,EAAE,CAAE;cAAAX,QAAA,eACTzC,OAAA,CAACP,IAAI,CAAC4F,KAAK;gBAAC3C,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BzC,OAAA,CAACP,IAAI,CAAC6F,KAAK;kBAAA7C,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvC9C,OAAA,CAACP,IAAI,CAACoG,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXN,KAAK,EAAEpE,OAAQ;kBACfqE,QAAQ,EAAGN,CAAC,IAAK9D,UAAU,CAAC8D,CAAC,CAACO,MAAM,CAACF,KAAK,CAAE;kBAC5CG,QAAQ;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEL9B,iBAAiB,IAAIR,QAAQ,CAACuF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxB,EAAE,KAAKxD,iBAAiB,CAAC,iBAClEhB,OAAA,CAACX,KAAK;YAAC0D,OAAO,EAAC,MAAM;YAAAN,QAAA,gBACnBzC,OAAA;cAAAyC,QAAA,EAAI;YAAqC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9C9C,OAAA;cAAI0C,SAAS,EAAC,MAAM;cAAAD,QAAA,EACjBjC,QAAQ,CAACuF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxB,EAAE,KAAKxD,iBAAiB,CAAC,CAACQ,IAAI,CAACuC,GAAG,CAAC,CAACxB,GAAG,EAAE0D,KAAK,kBAClEjG,OAAA;gBAAAyC,QAAA,gBACEzC,OAAA;kBAAAyC,QAAA,GAAQ,SAAI,EAACF,GAAG,CAAC2D,SAAS,EAAC,GAAC;gBAAA;kBAAAvD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACP,GAAG,CAAC8B,SAAS,EAAC,IAAE,EAAC9B,GAAG,CAACC,KAAK,EAAC,IACpE;cAAA,GAFSyD,KAAK;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACb9C,OAAA,CAACR,KAAK,CAAC2G,MAAM;QAAA1D,QAAA,gBACXzC,OAAA,CAACZ,MAAM;UAAC2D,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMjC,mBAAmB,CAAC,KAAK,CAAE;UAAA0B,QAAA,EAAC;QAEvE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9C,OAAA,CAACZ,MAAM;UACL2D,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAEnB,kBAAmB;UAC5BoB,QAAQ,EAAEvC,OAAO,IAAI,CAACM,iBAAiB,IAAI,CAACE,SAAS,IAAI,CAACE,OAAQ;UAAAqB,QAAA,EAEjE/B,OAAO,GAAG,cAAc,GAAG;QAAgB;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV;AAACzC,EAAA,CAzTQJ,mBAAmB;AAAAmG,EAAA,GAAnBnG,mBAAmB;AA2T5B,eAAeA,mBAAmB;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}