{"realCalendar": [{"date": "2025-07-03", "type": "", "hours": 0, "description": "", "shift": {"id": "afternoon", "name": "Tarde", "startTime": "14:00", "endTime": "22:00", "breakMinutes": 15}, "shiftId": null}, {"date": "2025-07-04", "type": "worked", "hours": 7.75, "description": "Tarde: 14:00 - 22:00 (15min descanso)", "shift": {"id": "holidayMorning", "name": "Festivos Mañana", "startTime": "06:00", "endTime": "18:00", "breakMinutes": 60}, "shiftId": "afternoon"}, {"date": "2025-07-05", "type": "worked", "hours": 11, "description": "Festivos Mañana: 06:00 - 18:00 (60min descanso)", "shift": {"id": "holidayMorning", "name": "Festivos Mañana", "startTime": "06:00", "endTime": "18:00", "breakMinutes": 60}, "shiftId": "holidayMorning"}, {"date": "2025-07-06", "type": "worked", "hours": 11, "description": "Festivos Mañana: 06:00 - 18:00 (60min descanso)", "shiftId": "holidayMorning"}], "plannedCalendar": [], "patternCalendar": {}, "holidays": [], "permits": [], "patterns": [{"id": "b8f7c3d2-4e5f-6a7b-8c9d-0e1f2a3b4c5d", "name": "<PERSON><PERSON><PERSON>", "description": "Patrón de trabajo de lunes a viernes con fines de semana libres", "basePattern": [{"type": "rest", "hours": 0, "description": "Domingo - <PERSON><PERSON><PERSON>"}, {"type": "worked", "hours": 8, "description": "Lunes - <PERSON><PERSON>", "shiftId": "morning"}, {"type": "worked", "hours": 8, "description": "Martes - <PERSON><PERSON>", "shiftId": "morning"}, {"type": "worked", "hours": 8, "description": "Miércoles - <PERSON><PERSON>", "shiftId": "morning"}, {"type": "worked", "hours": 8, "description": "Jueves - <PERSON><PERSON>", "shiftId": "morning"}, {"type": "worked", "hours": 8, "description": "Viernes - <PERSON><PERSON>", "shiftId": "morning"}, {"type": "rest", "hours": 0, "description": "Sábado - Descanso"}], "overrides": []}]}