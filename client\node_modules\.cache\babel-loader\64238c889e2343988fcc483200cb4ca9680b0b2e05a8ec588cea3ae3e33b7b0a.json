{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\CalendarPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Calendar from 'react-calendar';\nimport 'react-calendar/dist/Calendar.css';\nimport { Container, Row, Col, Button, Modal, Form, Alert, Badge } from 'react-bootstrap';\nimport { getDayByDate, createOrUpdateDay, getAllShifts, applyShiftToDate, suggestShiftsForDate, applyPattern, getAllPatterns } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction CalendarPage() {\n  _s();\n  const [date, setDate] = useState(new Date());\n  const [showModal, setShowModal] = useState(false);\n  const [selectedDay, setSelectedDay] = useState(null);\n  const [dayType, setDayType] = useState('');\n  const [hours, setHours] = useState(0);\n  const [description, setDescription] = useState('');\n\n  // Estados para turnos predefinidos\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [selectedShift, setSelectedShift] = useState('');\n  const [suggestedShifts, setSuggestedShifts] = useState([]);\n  const [useShift, setUseShift] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estados para el calendario patrón\n  const [showPatternModal, setShowPatternModal] = useState(false);\n  const [patternStartDate, setPatternStartDate] = useState(new Date());\n  const [patternEndDate, setPatternEndDate] = useState(new Date());\n  const [patternDays, setPatternDays] = useState([]); // Array of { type, hours, description, shiftId }\n  const [patternLength, setPatternLength] = useState(7); // Default to a 7-day pattern\n\n  useEffect(() => {\n    // Cargar turnos disponibles al montar el componente\n    loadAvailableShifts();\n  }, []);\n  useEffect(() => {\n    if (selectedDay) {\n      fetchDayDetails(selectedDay);\n      loadSuggestedShifts(selectedDay);\n    }\n  }, [selectedDay]);\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (error) {\n      console.error('Error loading shifts:', error);\n      setError('Error cargando turnos predefinidos');\n    }\n  };\n  const loadSuggestedShifts = async date => {\n    try {\n      const formattedDate = date.toISOString().split('T')[0];\n      const suggestions = await suggestShiftsForDate(formattedDate);\n      setSuggestedShifts(suggestions.suggestions || []);\n    } catch (error) {\n      console.error('Error loading suggested shifts:', error);\n      setSuggestedShifts([]);\n    }\n  };\n  const fetchDayDetails = async date => {\n    try {\n      const formattedDate = date.toISOString().split('T')[0];\n      const response = await getDayByDate(formattedDate);\n      if (response) {\n        setDayType(response.type || '');\n        setHours(response.hours || 0);\n        setDescription(response.description || '');\n        // Si el día tiene un turno asignado, mostrar esa información\n        if (response.shift) {\n          setSelectedShift(response.shift.id);\n          setUseShift(true);\n        } else {\n          setSelectedShift('');\n          setUseShift(false);\n        }\n      } else {\n        resetForm();\n      }\n    } catch (error) {\n      console.error('Error fetching day details:', error);\n      resetForm();\n    }\n  };\n  const resetForm = () => {\n    setDayType('');\n    setHours(0);\n    setDescription('');\n    setSelectedShift('');\n    setUseShift(false);\n    setError('');\n  };\n  const handleDateClick = value => {\n    setSelectedDay(value);\n    setShowModal(true);\n    setError('');\n  };\n  const handleShiftChange = shiftId => {\n    setSelectedShift(shiftId);\n    if (shiftId && availableShifts.length > 0) {\n      const shift = availableShifts.find(s => s.id === shiftId);\n      if (shift) {\n        setDayType('worked');\n        setHours(shift.totalHours);\n        setDescription(`${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`);\n      }\n    }\n  };\n  const handleSave = async () => {\n    if (!selectedDay) return;\n    const formattedDate = selectedDay.toISOString().split('T')[0];\n    setLoading(true);\n    setError('');\n    try {\n      if (useShift && selectedShift) {\n        // Usar turno predefinido\n        await applyShiftToDate(formattedDate, selectedShift, description);\n      } else {\n        // Usar entrada manual\n        await createOrUpdateDay(formattedDate, dayType, hours, description);\n      }\n      setShowModal(false);\n      resetForm();\n    } catch (error) {\n      console.error('Error saving day:', error);\n      setError('Error guardando los datos. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleApplyPattern = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      const formattedStartDate = patternStartDate.toISOString().split('T')[0];\n      const formattedEndDate = patternEndDate.toISOString().split('T')[0];\n      await applyPattern(formattedStartDate, formattedEndDate, patternDays);\n      setShowPatternModal(false);\n      // Optionally, refresh calendar data here if needed\n      alert('Patrón aplicado exitosamente!');\n    } catch (error) {\n      console.error('Error applying pattern:', error);\n      setError('Error aplicando el patrón. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"justify-content-md-center\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: \"auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Calendario de D\\xEDas Trabajados\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Calendar, {\n          onChange: setDate,\n          value: date,\n          onClickDay: handleDateClick\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"info\",\n          className: \"mt-3\",\n          onClick: () => setShowPatternModal(true),\n          children: \"Aplicar Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [\"Detalles del D\\xEDa: \", selectedDay && selectedDay.toDateString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"mb-3\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n              type: \"radio\",\n              id: \"manual-entry\",\n              name: \"entry-method\",\n              label: \"Entrada manual\",\n              checked: !useShift,\n              onChange: () => setUseShift(false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n              type: \"radio\",\n              id: \"shift-entry\",\n              name: \"entry-method\",\n              label: \"Usar turno predefinido\",\n              checked: useShift,\n              onChange: () => setUseShift(true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), useShift && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [suggestedShifts.length > 0 && /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Turnos sugeridos para esta fecha:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex flex-wrap gap-2 mb-2\",\n                children: suggestedShifts.map(shift => /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: selectedShift === shift.id ? \"primary\" : \"secondary\",\n                  style: {\n                    cursor: 'pointer'\n                  },\n                  onClick: () => handleShiftChange(shift.id),\n                  children: [shift.name, \" (\", shift.startTime, \" - \", shift.endTime, \")\"]\n                }, shift.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Seleccionar Turno\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: selectedShift,\n                onChange: e => handleShiftChange(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Seleccionar turno...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 21\n                }, this), availableShifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: shift.id,\n                  children: [shift.name, \" - \", shift.startTime, \" a \", shift.endTime, \" (\", shift.totalHours, \"h)\"]\n                }, shift.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), !useShift && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Tipo de D\\xEDa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: dayType,\n                onChange: e => setDayType(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Seleccionar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"worked\",\n                  children: \"Trabajado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"holiday\",\n                  children: \"Vacaciones\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"permit\",\n                  children: \"Permiso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"negative\",\n                  children: \"C\\xF3mputo Negativo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Horas (C\\xF3mputo Positivo/Negativo)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"number\",\n                value: hours,\n                onChange: e => setHours(parseFloat(e.target.value)),\n                placeholder: \"Introduce horas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Descripci\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              value: description,\n              onChange: e => setDescription(e.target.value),\n              placeholder: \"A\\xF1ade una descripci\\xF3n (opcional)\",\n              disabled: useShift && selectedShift && !description.includes('Personalizado:')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), useShift && selectedShift && /*#__PURE__*/_jsxDEV(Form.Text, {\n              className: \"text-muted\",\n              children: \"La descripci\\xF3n se genera autom\\xE1ticamente. Puedes editarla si es necesario.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => {\n            setShowModal(false);\n            resetForm();\n          },\n          disabled: loading,\n          children: \"Cerrar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSave,\n          disabled: loading || !useShift && !dayType || useShift && !selectedShift,\n          children: loading ? 'Guardando...' : 'Guardar Cambios'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPatternModal,\n      onHide: () => setShowPatternModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Aplicar Patr\\xF3n de D\\xEDas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"mb-3\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                controlId: \"patternStartDate\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de Inicio\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: patternStartDate.toISOString().split('T')[0],\n                  onChange: e => setPatternStartDate(new Date(e.target.value))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                controlId: \"patternEndDate\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de Fin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: patternEndDate.toISOString().split('T')[0],\n                  onChange: e => setPatternEndDate(new Date(e.target.value))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Longitud del Patr\\xF3n (d\\xEDas)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"number\",\n              min: \"1\",\n              value: patternLength,\n              onChange: e => {\n                const newLength = parseInt(e.target.value, 10);\n                setPatternLength(newLength);\n                setPatternDays(Array.from({\n                  length: newLength\n                }, (_, i) => patternDays[i] || {\n                  type: '',\n                  hours: 0,\n                  description: '',\n                  shiftId: null\n                }));\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Definir D\\xEDas del Patr\\xF3n:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this), patternDays.map((day, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border p-3 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: [\"D\\xEDa \", index + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Tipo de D\\xEDa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: day.type,\n                onChange: e => {\n                  const newPatternDays = [...patternDays];\n                  newPatternDays[index].type = e.target.value;\n                  setPatternDays(newPatternDays);\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Seleccionar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"worked\",\n                  children: \"Trabajado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"holiday\",\n                  children: \"Vacaciones\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"permit\",\n                  children: \"Permiso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"negative\",\n                  children: \"C\\xF3mputo Negativo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Horas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"number\",\n                value: day.hours,\n                onChange: e => {\n                  const newPatternDays = [...patternDays];\n                  newPatternDays[index].hours = parseFloat(e.target.value);\n                  setPatternDays(newPatternDays);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Turno Predefinido\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: day.shiftId || '',\n                onChange: e => {\n                  const newPatternDays = [...patternDays];\n                  const selectedShiftId = e.target.value;\n                  newPatternDays[index].shiftId = selectedShiftId || null;\n                  if (selectedShiftId) {\n                    const shift = availableShifts.find(s => s.id === selectedShiftId);\n                    if (shift) {\n                      newPatternDays[index].type = 'worked';\n                      newPatternDays[index].hours = shift.totalHours;\n                      newPatternDays[index].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;\n                    }\n                  } else {\n                    newPatternDays[index].description = '';\n                  }\n                  setPatternDays(newPatternDays);\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ninguno\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 21\n                }, this), availableShifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: shift.id,\n                  children: [shift.name, \" - \", shift.startTime, \" a \", shift.endTime, \" (\", shift.totalHours, \"h)\"]\n                }, shift.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Descripci\\xF3n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                as: \"textarea\",\n                rows: 1,\n                value: day.description,\n                onChange: e => {\n                  const newPatternDays = [...patternDays];\n                  newPatternDays[index].description = e.target.value;\n                  setPatternDays(newPatternDays);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => {\n            setShowPatternModal(false);\n            setError('');\n          },\n          children: \"Cerrar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleApplyPattern,\n          disabled: loading,\n          children: loading ? 'Aplicando...' : 'Aplicar Patrón'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 167,\n    columnNumber: 5\n  }, this);\n}\n_s(CalendarPage, \"aGfETcyM9pZKeWTS0dkRomdeGuQ=\");\n_c = CalendarPage;\nexport default CalendarPage;\nvar _c;\n$RefreshReg$(_c, \"CalendarPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Calendar", "Container", "Row", "Col", "<PERSON><PERSON>", "Modal", "Form", "<PERSON><PERSON>", "Badge", "getDayByDate", "createOrUpdateDay", "getAllShifts", "applyShiftToDate", "suggestShiftsForDate", "applyPattern", "getAllPatterns", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CalendarPage", "_s", "date", "setDate", "Date", "showModal", "setShowModal", "selected<PERSON>ay", "setSelectedDay", "dayType", "setDayType", "hours", "setHours", "description", "setDescription", "availableShifts", "setAvailableShifts", "selectedShift", "setSelectedShift", "suggestedShifts", "setSuggestedShifts", "useShift", "setUseShift", "loading", "setLoading", "error", "setError", "showPatternModal", "setShowPatternModal", "patternStartDate", "setPatternStartDate", "patternEndDate", "setPatternEndDate", "patternDays", "setPatternDays", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadAvailableShifts", "fetchDayDetails", "loadSuggestedShifts", "shifts", "console", "formattedDate", "toISOString", "split", "suggestions", "response", "type", "shift", "id", "resetForm", "handleDateClick", "value", "handleShiftChange", "shiftId", "length", "find", "s", "totalHours", "name", "startTime", "endTime", "breakMinutes", "handleSave", "handleApplyPattern", "formattedStartDate", "formattedEndDate", "alert", "children", "className", "md", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "onClickDay", "variant", "onClick", "show", "onHide", "Header", "closeButton", "Title", "toDateString", "Body", "Group", "Check", "label", "checked", "Label", "map", "bg", "style", "cursor", "Select", "e", "target", "Control", "parseFloat", "placeholder", "as", "rows", "disabled", "includes", "Text", "Footer", "size", "controlId", "min", "<PERSON><PERSON><PERSON><PERSON>", "parseInt", "Array", "from", "_", "i", "day", "index", "newPatternDays", "selectedShiftId", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/CalendarPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Calendar from 'react-calendar';\nimport 'react-calendar/dist/Calendar.css';\nimport { Container, Row, Col, Button, Modal, Form, Alert, Badge } from 'react-bootstrap';\nimport {\n  getDayByDate,\n  createOrUpdateDay,\n  getAllShifts,\n  applyShiftToDate,\n  suggestShiftsForDate,\n  applyPattern,\n  getAllPatterns\n} from '../services/api';\n\nfunction CalendarPage() {\n  const [date, setDate] = useState(new Date());\n  const [showModal, setShowModal] = useState(false);\n  const [selectedDay, setSelectedDay] = useState(null);\n  const [dayType, setDayType] = useState('');\n  const [hours, setHours] = useState(0);\n  const [description, setDescription] = useState('');\n\n  // Estados para turnos predefinidos\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [selectedShift, setSelectedShift] = useState('');\n  const [suggestedShifts, setSuggestedShifts] = useState([]);\n  const [useShift, setUseShift] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estados para el calendario patrón\n  const [showPatternModal, setShowPatternModal] = useState(false);\n  const [patternStartDate, setPatternStartDate] = useState(new Date());\n  const [patternEndDate, setPatternEndDate] = useState(new Date());\n  const [patternDays, setPatternDays] = useState([]); // Array of { type, hours, description, shiftId }\n  const [patternLength, setPatternLength] = useState(7); // Default to a 7-day pattern\n\n  useEffect(() => {\n    // Cargar turnos disponibles al montar el componente\n    loadAvailableShifts();\n  }, []);\n\n  useEffect(() => {\n    if (selectedDay) {\n      fetchDayDetails(selectedDay);\n      loadSuggestedShifts(selectedDay);\n    }\n  }, [selectedDay]);\n\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (error) {\n      console.error('Error loading shifts:', error);\n      setError('Error cargando turnos predefinidos');\n    }\n  };\n\n  const loadSuggestedShifts = async (date) => {\n    try {\n      const formattedDate = date.toISOString().split('T')[0];\n      const suggestions = await suggestShiftsForDate(formattedDate);\n      setSuggestedShifts(suggestions.suggestions || []);\n    } catch (error) {\n      console.error('Error loading suggested shifts:', error);\n      setSuggestedShifts([]);\n    }\n  };\n\n  const fetchDayDetails = async (date) => {\n    try {\n      const formattedDate = date.toISOString().split('T')[0];\n      const response = await getDayByDate(formattedDate);\n      if (response) {\n        setDayType(response.type || '');\n        setHours(response.hours || 0);\n        setDescription(response.description || '');\n        // Si el día tiene un turno asignado, mostrar esa información\n        if (response.shift) {\n          setSelectedShift(response.shift.id);\n          setUseShift(true);\n        } else {\n          setSelectedShift('');\n          setUseShift(false);\n        }\n      } else {\n        resetForm();\n      }\n    } catch (error) {\n      console.error('Error fetching day details:', error);\n      resetForm();\n    }\n  };\n\n  const resetForm = () => {\n    setDayType('');\n    setHours(0);\n    setDescription('');\n    setSelectedShift('');\n    setUseShift(false);\n    setError('');\n  };\n\n  const handleDateClick = (value) => {\n    setSelectedDay(value);\n    setShowModal(true);\n    setError('');\n  };\n\n  const handleShiftChange = (shiftId) => {\n    setSelectedShift(shiftId);\n    if (shiftId && availableShifts.length > 0) {\n      const shift = availableShifts.find(s => s.id === shiftId);\n      if (shift) {\n        setDayType('worked');\n        setHours(shift.totalHours);\n        setDescription(`${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`);\n      }\n    }\n  };\n\n  const handleSave = async () => {\n    if (!selectedDay) return;\n\n    const formattedDate = selectedDay.toISOString().split('T')[0];\n    setLoading(true);\n    setError('');\n\n    try {\n      if (useShift && selectedShift) {\n        // Usar turno predefinido\n        await applyShiftToDate(formattedDate, selectedShift, description);\n      } else {\n        // Usar entrada manual\n        await createOrUpdateDay(formattedDate, dayType, hours, description);\n      }\n      setShowModal(false);\n      resetForm();\n    } catch (error) {\n      console.error('Error saving day:', error);\n      setError('Error guardando los datos. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleApplyPattern = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      const formattedStartDate = patternStartDate.toISOString().split('T')[0];\n      const formattedEndDate = patternEndDate.toISOString().split('T')[0];\n      await applyPattern(formattedStartDate, formattedEndDate, patternDays);\n      setShowPatternModal(false);\n      // Optionally, refresh calendar data here if needed\n      alert('Patrón aplicado exitosamente!');\n    } catch (error) {\n      console.error('Error applying pattern:', error);\n      setError('Error aplicando el patrón. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Container>\n      <Row className=\"justify-content-md-center\">\n        <Col md=\"auto\">\n          <h2>Calendario de Días Trabajados</h2>\n          <Calendar\n            onChange={setDate}\n            value={date}\n            onClickDay={handleDateClick}\n          />\n          <Button variant=\"info\" className=\"mt-3\" onClick={() => setShowPatternModal(true)}>\n            Aplicar Patrón\n          </Button>\n        </Col>\n      </Row>\n\n      <Modal show={showModal} onHide={() => setShowModal(false)}>\n        <Modal.Header closeButton>\n          <Modal.Title>Detalles del Día: {selectedDay && selectedDay.toDateString()}</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {error && (\n            <Alert variant=\"danger\" className=\"mb-3\">\n              {error}\n            </Alert>\n          )}\n\n          <Form>\n            {/* Selector de método de entrada */}\n            <Form.Group className=\"mb-3\">\n              <Form.Check\n                type=\"radio\"\n                id=\"manual-entry\"\n                name=\"entry-method\"\n                label=\"Entrada manual\"\n                checked={!useShift}\n                onChange={() => setUseShift(false)}\n              />\n              <Form.Check\n                type=\"radio\"\n                id=\"shift-entry\"\n                name=\"entry-method\"\n                label=\"Usar turno predefinido\"\n                checked={useShift}\n                onChange={() => setUseShift(true)}\n              />\n            </Form.Group>\n\n            {/* Sección de turnos predefinidos */}\n            {useShift && (\n              <>\n                {suggestedShifts.length > 0 && (\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Turnos sugeridos para esta fecha:</Form.Label>\n                    <div className=\"d-flex flex-wrap gap-2 mb-2\">\n                      {suggestedShifts.map((shift) => (\n                        <Badge\n                          key={shift.id}\n                          bg={selectedShift === shift.id ? \"primary\" : \"secondary\"}\n                          style={{ cursor: 'pointer' }}\n                          onClick={() => handleShiftChange(shift.id)}\n                        >\n                          {shift.name} ({shift.startTime} - {shift.endTime})\n                        </Badge>\n                      ))}\n                    </div>\n                  </Form.Group>\n                )}\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Seleccionar Turno</Form.Label>\n                  <Form.Select\n                    value={selectedShift}\n                    onChange={(e) => handleShiftChange(e.target.value)}\n                  >\n                    <option value=\"\">Seleccionar turno...</option>\n                    {availableShifts.map((shift) => (\n                      <option key={shift.id} value={shift.id}>\n                        {shift.name} - {shift.startTime} a {shift.endTime} ({shift.totalHours}h)\n                      </option>\n                    ))}\n                  </Form.Select>\n                </Form.Group>\n              </>\n            )}\n\n            {/* Sección de entrada manual */}\n            {!useShift && (\n              <>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Tipo de Día</Form.Label>\n                  <Form.Select value={dayType} onChange={(e) => setDayType(e.target.value)}>\n                    <option value=\"\">Seleccionar</option>\n                    <option value=\"worked\">Trabajado</option>\n                    <option value=\"holiday\">Vacaciones</option>\n                    <option value=\"permit\">Permiso</option>\n                    <option value=\"negative\">Cómputo Negativo</option>\n                  </Form.Select>\n                </Form.Group>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Horas (Cómputo Positivo/Negativo)</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    value={hours}\n                    onChange={(e) => setHours(parseFloat(e.target.value))}\n                    placeholder=\"Introduce horas\"\n                  />\n                </Form.Group>\n              </>\n            )}\n\n            {/* Descripción (común para ambos métodos) */}\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Descripción</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={3}\n                value={description}\n                onChange={(e) => setDescription(e.target.value)}\n                placeholder=\"Añade una descripción (opcional)\"\n                disabled={useShift && selectedShift && !description.includes('Personalizado:')}\n              />\n              {useShift && selectedShift && (\n                <Form.Text className=\"text-muted\">\n                  La descripción se genera automáticamente. Puedes editarla si es necesario.\n                </Form.Text>\n              )}\n            </Form.Group>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button\n            variant=\"secondary\"\n            onClick={() => {\n              setShowModal(false);\n              resetForm();\n            }}\n            disabled={loading}\n          >\n            Cerrar\n          </Button>\n          <Button\n            variant=\"primary\"\n            onClick={handleSave}\n            disabled={loading || (!useShift && !dayType) || (useShift && !selectedShift)}\n          >\n            {loading ? 'Guardando...' : 'Guardar Cambios'}\n          </Button>\n        </Modal.Footer>\n      </Modal>\n\n      {/* Modal para Aplicar Patrón */}\n      <Modal show={showPatternModal} onHide={() => setShowPatternModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>Aplicar Patrón de Días</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {error && (\n            <Alert variant=\"danger\" className=\"mb-3\">\n              {error}\n            </Alert>\n          )}\n          <Form>\n            <Row className=\"mb-3\">\n              <Col>\n                <Form.Group controlId=\"patternStartDate\">\n                  <Form.Label>Fecha de Inicio</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={patternStartDate.toISOString().split('T')[0]}\n                    onChange={(e) => setPatternStartDate(new Date(e.target.value))}\n                  />\n                </Form.Group>\n              </Col>\n              <Col>\n                <Form.Group controlId=\"patternEndDate\">\n                  <Form.Label>Fecha de Fin</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={patternEndDate.toISOString().split('T')[0]}\n                    onChange={(e) => setPatternEndDate(new Date(e.target.value))}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Longitud del Patrón (días)</Form.Label>\n              <Form.Control\n                type=\"number\"\n                min=\"1\"\n                value={patternLength}\n                onChange={(e) => {\n                  const newLength = parseInt(e.target.value, 10);\n                  setPatternLength(newLength);\n                  setPatternDays(Array.from({ length: newLength }, (_, i) => patternDays[i] || { type: '', hours: 0, description: '', shiftId: null }));\n                }}\n              />\n            </Form.Group>\n\n            <h5>Definir Días del Patrón:</h5>\n            {patternDays.map((day, index) => (\n              <div key={index} className=\"border p-3 mb-2\">\n                <h6>Día {index + 1}</h6>\n                <Form.Group className=\"mb-2\">\n                  <Form.Label>Tipo de Día</Form.Label>\n                  <Form.Select\n                    value={day.type}\n                    onChange={(e) => {\n                      const newPatternDays = [...patternDays];\n                      newPatternDays[index].type = e.target.value;\n                      setPatternDays(newPatternDays);\n                    }}\n                  >\n                    <option value=\"\">Seleccionar</option>\n                    <option value=\"worked\">Trabajado</option>\n                    <option value=\"holiday\">Vacaciones</option>\n                    <option value=\"permit\">Permiso</option>\n                    <option value=\"negative\">Cómputo Negativo</option>\n                  </Form.Select>\n                </Form.Group>\n                <Form.Group className=\"mb-2\">\n                  <Form.Label>Horas</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    value={day.hours}\n                    onChange={(e) => {\n                      const newPatternDays = [...patternDays];\n                      newPatternDays[index].hours = parseFloat(e.target.value);\n                      setPatternDays(newPatternDays);\n                    }}\n                  />\n                </Form.Group>\n                <Form.Group className=\"mb-2\">\n                  <Form.Label>Turno Predefinido</Form.Label>\n                  <Form.Select\n                    value={day.shiftId || ''}\n                    onChange={(e) => {\n                      const newPatternDays = [...patternDays];\n                      const selectedShiftId = e.target.value;\n                      newPatternDays[index].shiftId = selectedShiftId || null;\n                      if (selectedShiftId) {\n                        const shift = availableShifts.find(s => s.id === selectedShiftId);\n                        if (shift) {\n                          newPatternDays[index].type = 'worked';\n                          newPatternDays[index].hours = shift.totalHours;\n                          newPatternDays[index].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;\n                        }\n                      } else {\n                        newPatternDays[index].description = '';\n                      }\n                      setPatternDays(newPatternDays);\n                    }}\n                  >\n                    <option value=\"\">Ninguno</option>\n                    {availableShifts.map((shift) => (\n                      <option key={shift.id} value={shift.id}>\n                        {shift.name} - {shift.startTime} a {shift.endTime} ({shift.totalHours}h)\n                      </option>\n                    ))}\n                  </Form.Select>\n                </Form.Group>\n                <Form.Group className=\"mb-2\">\n                  <Form.Label>Descripción</Form.Label>\n                  <Form.Control\n                    as=\"textarea\"\n                    rows={1}\n                    value={day.description}\n                    onChange={(e) => {\n                      const newPatternDays = [...patternDays];\n                      newPatternDays[index].description = e.target.value;\n                      setPatternDays(newPatternDays);\n                    }}\n                  />\n                </Form.Group>\n              </div>\n            ))}\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button\n            variant=\"secondary\"\n            onClick={() => {\n              setShowPatternModal(false);\n              setError('');\n            }}\n          >\n            Cerrar\n          </Button>\n          <Button\n            variant=\"primary\"\n            onClick={handleApplyPattern}\n            disabled={loading}\n          >\n            {loading ? 'Aplicando...' : 'Aplicar Patrón'}\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n}\n\nexport default CalendarPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAO,kCAAkC;AACzC,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AACxF,SACEC,YAAY,EACZC,iBAAiB,EACjBC,YAAY,EACZC,gBAAgB,EAChBC,oBAAoB,EACpBC,YAAY,EACZC,cAAc,QACT,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGzB,QAAQ,CAAC,IAAI0B,IAAI,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiC,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAM,CAACiD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACmD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,IAAI0B,IAAI,CAAC,CAAC,CAAC;EACpE,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,IAAI0B,IAAI,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACpD,MAAM,CAACyD,aAAa,EAAEC,gBAAgB,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEvDC,SAAS,CAAC,MAAM;IACd;IACA0D,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN1D,SAAS,CAAC,MAAM;IACd,IAAI4B,WAAW,EAAE;MACf+B,eAAe,CAAC/B,WAAW,CAAC;MAC5BgC,mBAAmB,CAAChC,WAAW,CAAC;IAClC;EACF,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjB,MAAM8B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMG,MAAM,GAAG,MAAMjD,YAAY,CAAC,CAAC;MACnCyB,kBAAkB,CAACwB,MAAM,CAAC;IAC5B,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdgB,OAAO,CAAChB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,oCAAoC,CAAC;IAChD;EACF,CAAC;EAED,MAAMa,mBAAmB,GAAG,MAAOrC,IAAI,IAAK;IAC1C,IAAI;MACF,MAAMwC,aAAa,GAAGxC,IAAI,CAACyC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACtD,MAAMC,WAAW,GAAG,MAAMpD,oBAAoB,CAACiD,aAAa,CAAC;MAC7DtB,kBAAkB,CAACyB,WAAW,CAACA,WAAW,IAAI,EAAE,CAAC;IACnD,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdgB,OAAO,CAAChB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDL,kBAAkB,CAAC,EAAE,CAAC;IACxB;EACF,CAAC;EAED,MAAMkB,eAAe,GAAG,MAAOpC,IAAI,IAAK;IACtC,IAAI;MACF,MAAMwC,aAAa,GAAGxC,IAAI,CAACyC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACtD,MAAME,QAAQ,GAAG,MAAMzD,YAAY,CAACqD,aAAa,CAAC;MAClD,IAAII,QAAQ,EAAE;QACZpC,UAAU,CAACoC,QAAQ,CAACC,IAAI,IAAI,EAAE,CAAC;QAC/BnC,QAAQ,CAACkC,QAAQ,CAACnC,KAAK,IAAI,CAAC,CAAC;QAC7BG,cAAc,CAACgC,QAAQ,CAACjC,WAAW,IAAI,EAAE,CAAC;QAC1C;QACA,IAAIiC,QAAQ,CAACE,KAAK,EAAE;UAClB9B,gBAAgB,CAAC4B,QAAQ,CAACE,KAAK,CAACC,EAAE,CAAC;UACnC3B,WAAW,CAAC,IAAI,CAAC;QACnB,CAAC,MAAM;UACLJ,gBAAgB,CAAC,EAAE,CAAC;UACpBI,WAAW,CAAC,KAAK,CAAC;QACpB;MACF,CAAC,MAAM;QACL4B,SAAS,CAAC,CAAC;MACb;IACF,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdgB,OAAO,CAAChB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDyB,SAAS,CAAC,CAAC;IACb;EACF,CAAC;EAED,MAAMA,SAAS,GAAGA,CAAA,KAAM;IACtBxC,UAAU,CAAC,EAAE,CAAC;IACdE,QAAQ,CAAC,CAAC,CAAC;IACXE,cAAc,CAAC,EAAE,CAAC;IAClBI,gBAAgB,CAAC,EAAE,CAAC;IACpBI,WAAW,CAAC,KAAK,CAAC;IAClBI,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMyB,eAAe,GAAIC,KAAK,IAAK;IACjC5C,cAAc,CAAC4C,KAAK,CAAC;IACrB9C,YAAY,CAAC,IAAI,CAAC;IAClBoB,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAM2B,iBAAiB,GAAIC,OAAO,IAAK;IACrCpC,gBAAgB,CAACoC,OAAO,CAAC;IACzB,IAAIA,OAAO,IAAIvC,eAAe,CAACwC,MAAM,GAAG,CAAC,EAAE;MACzC,MAAMP,KAAK,GAAGjC,eAAe,CAACyC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACR,EAAE,KAAKK,OAAO,CAAC;MACzD,IAAIN,KAAK,EAAE;QACTtC,UAAU,CAAC,QAAQ,CAAC;QACpBE,QAAQ,CAACoC,KAAK,CAACU,UAAU,CAAC;QAC1B5C,cAAc,CAAC,GAAGkC,KAAK,CAACW,IAAI,KAAKX,KAAK,CAACY,SAAS,MAAMZ,KAAK,CAACa,OAAO,KAAKb,KAAK,CAACc,YAAY,eAAe,CAAC;MAC5G;IACF;EACF,CAAC;EAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACxD,WAAW,EAAE;IAElB,MAAMmC,aAAa,GAAGnC,WAAW,CAACoC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7DpB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,IAAIL,QAAQ,IAAIJ,aAAa,EAAE;QAC7B;QACA,MAAMzB,gBAAgB,CAACkD,aAAa,EAAEzB,aAAa,EAAEJ,WAAW,CAAC;MACnE,CAAC,MAAM;QACL;QACA,MAAMvB,iBAAiB,CAACoD,aAAa,EAAEjC,OAAO,EAAEE,KAAK,EAAEE,WAAW,CAAC;MACrE;MACAP,YAAY,CAAC,KAAK,CAAC;MACnB4C,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdgB,OAAO,CAAChB,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCC,QAAQ,CAAC,2DAA2D,CAAC;IACvE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrCxC,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,MAAMuC,kBAAkB,GAAGpC,gBAAgB,CAACc,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACvE,MAAMsB,gBAAgB,GAAGnC,cAAc,CAACY,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACnE,MAAMlD,YAAY,CAACuE,kBAAkB,EAAEC,gBAAgB,EAAEjC,WAAW,CAAC;MACrEL,mBAAmB,CAAC,KAAK,CAAC;MAC1B;MACAuC,KAAK,CAAC,+BAA+B,CAAC;IACxC,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACdgB,OAAO,CAAChB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,2DAA2D,CAAC;IACvE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACE3B,OAAA,CAAChB,SAAS;IAAAuF,QAAA,gBACRvE,OAAA,CAACf,GAAG;MAACuF,SAAS,EAAC,2BAA2B;MAAAD,QAAA,eACxCvE,OAAA,CAACd,GAAG;QAACuF,EAAE,EAAC,MAAM;QAAAF,QAAA,gBACZvE,OAAA;UAAAuE,QAAA,EAAI;QAA6B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtC7E,OAAA,CAACjB,QAAQ;UACP+F,QAAQ,EAAExE,OAAQ;UAClBiD,KAAK,EAAElD,IAAK;UACZ0E,UAAU,EAAEzB;QAAgB;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACF7E,OAAA,CAACb,MAAM;UAAC6F,OAAO,EAAC,MAAM;UAACR,SAAS,EAAC,MAAM;UAACS,OAAO,EAAEA,CAAA,KAAMlD,mBAAmB,CAAC,IAAI,CAAE;UAAAwC,QAAA,EAAC;QAElF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7E,OAAA,CAACZ,KAAK;MAAC8F,IAAI,EAAE1E,SAAU;MAAC2E,MAAM,EAAEA,CAAA,KAAM1E,YAAY,CAAC,KAAK,CAAE;MAAA8D,QAAA,gBACxDvE,OAAA,CAACZ,KAAK,CAACgG,MAAM;QAACC,WAAW;QAAAd,QAAA,eACvBvE,OAAA,CAACZ,KAAK,CAACkG,KAAK;UAAAf,QAAA,GAAC,uBAAkB,EAAC7D,WAAW,IAAIA,WAAW,CAAC6E,YAAY,CAAC,CAAC;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,eACf7E,OAAA,CAACZ,KAAK,CAACoG,IAAI;QAAAjB,QAAA,GACR3C,KAAK,iBACJ5B,OAAA,CAACV,KAAK;UAAC0F,OAAO,EAAC,QAAQ;UAACR,SAAS,EAAC,MAAM;UAAAD,QAAA,EACrC3C;QAAK;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eAED7E,OAAA,CAACX,IAAI;UAAAkF,QAAA,gBAEHvE,OAAA,CAACX,IAAI,CAACoG,KAAK;YAACjB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAACqG,KAAK;cACTxC,IAAI,EAAC,OAAO;cACZE,EAAE,EAAC,cAAc;cACjBU,IAAI,EAAC,cAAc;cACnB6B,KAAK,EAAC,gBAAgB;cACtBC,OAAO,EAAE,CAACpE,QAAS;cACnBsD,QAAQ,EAAEA,CAAA,KAAMrD,WAAW,CAAC,KAAK;YAAE;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACF7E,OAAA,CAACX,IAAI,CAACqG,KAAK;cACTxC,IAAI,EAAC,OAAO;cACZE,EAAE,EAAC,aAAa;cAChBU,IAAI,EAAC,cAAc;cACnB6B,KAAK,EAAC,wBAAwB;cAC9BC,OAAO,EAAEpE,QAAS;cAClBsD,QAAQ,EAAEA,CAAA,KAAMrD,WAAW,CAAC,IAAI;YAAE;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,EAGZrD,QAAQ,iBACPxB,OAAA,CAAAE,SAAA;YAAAqE,QAAA,GACGjD,eAAe,CAACoC,MAAM,GAAG,CAAC,iBACzB1D,OAAA,CAACX,IAAI,CAACoG,KAAK;cAACjB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAACwG,KAAK;gBAAAtB,QAAA,EAAC;cAAiC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1D7E,OAAA;gBAAKwE,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,EACzCjD,eAAe,CAACwE,GAAG,CAAE3C,KAAK,iBACzBnD,OAAA,CAACT,KAAK;kBAEJwG,EAAE,EAAE3E,aAAa,KAAK+B,KAAK,CAACC,EAAE,GAAG,SAAS,GAAG,WAAY;kBACzD4C,KAAK,EAAE;oBAAEC,MAAM,EAAE;kBAAU,CAAE;kBAC7BhB,OAAO,EAAEA,CAAA,KAAMzB,iBAAiB,CAACL,KAAK,CAACC,EAAE,CAAE;kBAAAmB,QAAA,GAE1CpB,KAAK,CAACW,IAAI,EAAC,IAAE,EAACX,KAAK,CAACY,SAAS,EAAC,KAAG,EAACZ,KAAK,CAACa,OAAO,EAAC,GACnD;gBAAA,GANOb,KAAK,CAACC,EAAE;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMR,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAED7E,OAAA,CAACX,IAAI,CAACoG,KAAK;cAACjB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAACwG,KAAK;gBAAAtB,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1C7E,OAAA,CAACX,IAAI,CAAC6G,MAAM;gBACV3C,KAAK,EAAEnC,aAAc;gBACrB0D,QAAQ,EAAGqB,CAAC,IAAK3C,iBAAiB,CAAC2C,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAE;gBAAAgB,QAAA,gBAEnDvE,OAAA;kBAAQuD,KAAK,EAAC,EAAE;kBAAAgB,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC7C3D,eAAe,CAAC4E,GAAG,CAAE3C,KAAK,iBACzBnD,OAAA;kBAAuBuD,KAAK,EAAEJ,KAAK,CAACC,EAAG;kBAAAmB,QAAA,GACpCpB,KAAK,CAACW,IAAI,EAAC,KAAG,EAACX,KAAK,CAACY,SAAS,EAAC,KAAG,EAACZ,KAAK,CAACa,OAAO,EAAC,IAAE,EAACb,KAAK,CAACU,UAAU,EAAC,IACxE;gBAAA,GAFaV,KAAK,CAACC,EAAE;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,eACb,CACH,EAGA,CAACrD,QAAQ,iBACRxB,OAAA,CAAAE,SAAA;YAAAqE,QAAA,gBACEvE,OAAA,CAACX,IAAI,CAACoG,KAAK;cAACjB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAACwG,KAAK;gBAAAtB,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpC7E,OAAA,CAACX,IAAI,CAAC6G,MAAM;gBAAC3C,KAAK,EAAE3C,OAAQ;gBAACkE,QAAQ,EAAGqB,CAAC,IAAKtF,UAAU,CAACsF,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAE;gBAAAgB,QAAA,gBACvEvE,OAAA;kBAAQuD,KAAK,EAAC,EAAE;kBAAAgB,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrC7E,OAAA;kBAAQuD,KAAK,EAAC,QAAQ;kBAAAgB,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzC7E,OAAA;kBAAQuD,KAAK,EAAC,SAAS;kBAAAgB,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3C7E,OAAA;kBAAQuD,KAAK,EAAC,QAAQ;kBAAAgB,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvC7E,OAAA;kBAAQuD,KAAK,EAAC,UAAU;kBAAAgB,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACb7E,OAAA,CAACX,IAAI,CAACoG,KAAK;cAACjB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAACwG,KAAK;gBAAAtB,QAAA,EAAC;cAAiC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1D7E,OAAA,CAACX,IAAI,CAACgH,OAAO;gBACXnD,IAAI,EAAC,QAAQ;gBACbK,KAAK,EAAEzC,KAAM;gBACbgE,QAAQ,EAAGqB,CAAC,IAAKpF,QAAQ,CAACuF,UAAU,CAACH,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAC,CAAE;gBACtDgD,WAAW,EAAC;cAAiB;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA,eACb,CACH,eAGD7E,OAAA,CAACX,IAAI,CAACoG,KAAK;YAACjB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAACwG,KAAK;cAAAtB,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpC7E,OAAA,CAACX,IAAI,CAACgH,OAAO;cACXG,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRlD,KAAK,EAAEvC,WAAY;cACnB8D,QAAQ,EAAGqB,CAAC,IAAKlF,cAAc,CAACkF,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAE;cAChDgD,WAAW,EAAC,wCAAkC;cAC9CG,QAAQ,EAAElF,QAAQ,IAAIJ,aAAa,IAAI,CAACJ,WAAW,CAAC2F,QAAQ,CAAC,gBAAgB;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,EACDrD,QAAQ,IAAIJ,aAAa,iBACxBpB,OAAA,CAACX,IAAI,CAACuH,IAAI;cAACpC,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAElC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CACZ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACb7E,OAAA,CAACZ,KAAK,CAACyH,MAAM;QAAAtC,QAAA,gBACXvE,OAAA,CAACb,MAAM;UACL6F,OAAO,EAAC,WAAW;UACnBC,OAAO,EAAEA,CAAA,KAAM;YACbxE,YAAY,CAAC,KAAK,CAAC;YACnB4C,SAAS,CAAC,CAAC;UACb,CAAE;UACFqD,QAAQ,EAAEhF,OAAQ;UAAA6C,QAAA,EACnB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7E,OAAA,CAACb,MAAM;UACL6F,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAEf,UAAW;UACpBwC,QAAQ,EAAEhF,OAAO,IAAK,CAACF,QAAQ,IAAI,CAACZ,OAAQ,IAAKY,QAAQ,IAAI,CAACJ,aAAe;UAAAmD,QAAA,EAE5E7C,OAAO,GAAG,cAAc,GAAG;QAAiB;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGR7E,OAAA,CAACZ,KAAK;MAAC8F,IAAI,EAAEpD,gBAAiB;MAACqD,MAAM,EAAEA,CAAA,KAAMpD,mBAAmB,CAAC,KAAK,CAAE;MAAC+E,IAAI,EAAC,IAAI;MAAAvC,QAAA,gBAChFvE,OAAA,CAACZ,KAAK,CAACgG,MAAM;QAACC,WAAW;QAAAd,QAAA,eACvBvE,OAAA,CAACZ,KAAK,CAACkG,KAAK;UAAAf,QAAA,EAAC;QAAsB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACf7E,OAAA,CAACZ,KAAK,CAACoG,IAAI;QAAAjB,QAAA,GACR3C,KAAK,iBACJ5B,OAAA,CAACV,KAAK;UAAC0F,OAAO,EAAC,QAAQ;UAACR,SAAS,EAAC,MAAM;UAAAD,QAAA,EACrC3C;QAAK;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eACD7E,OAAA,CAACX,IAAI;UAAAkF,QAAA,gBACHvE,OAAA,CAACf,GAAG;YAACuF,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBvE,OAAA,CAACd,GAAG;cAAAqF,QAAA,eACFvE,OAAA,CAACX,IAAI,CAACoG,KAAK;gBAACsB,SAAS,EAAC,kBAAkB;gBAAAxC,QAAA,gBACtCvE,OAAA,CAACX,IAAI,CAACwG,KAAK;kBAAAtB,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxC7E,OAAA,CAACX,IAAI,CAACgH,OAAO;kBACXnD,IAAI,EAAC,MAAM;kBACXK,KAAK,EAAEvB,gBAAgB,CAACc,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;kBACpD+B,QAAQ,EAAGqB,CAAC,IAAKlE,mBAAmB,CAAC,IAAI1B,IAAI,CAAC4F,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAC;gBAAE;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN7E,OAAA,CAACd,GAAG;cAAAqF,QAAA,eACFvE,OAAA,CAACX,IAAI,CAACoG,KAAK;gBAACsB,SAAS,EAAC,gBAAgB;gBAAAxC,QAAA,gBACpCvE,OAAA,CAACX,IAAI,CAACwG,KAAK;kBAAAtB,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrC7E,OAAA,CAACX,IAAI,CAACgH,OAAO;kBACXnD,IAAI,EAAC,MAAM;kBACXK,KAAK,EAAErB,cAAc,CAACY,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;kBAClD+B,QAAQ,EAAGqB,CAAC,IAAKhE,iBAAiB,CAAC,IAAI5B,IAAI,CAAC4F,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAC;gBAAE;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7E,OAAA,CAACX,IAAI,CAACoG,KAAK;YAACjB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAACwG,KAAK;cAAAtB,QAAA,EAAC;YAA0B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnD7E,OAAA,CAACX,IAAI,CAACgH,OAAO;cACXnD,IAAI,EAAC,QAAQ;cACb8D,GAAG,EAAC,GAAG;cACPzD,KAAK,EAAEjB,aAAc;cACrBwC,QAAQ,EAAGqB,CAAC,IAAK;gBACf,MAAMc,SAAS,GAAGC,QAAQ,CAACf,CAAC,CAACC,MAAM,CAAC7C,KAAK,EAAE,EAAE,CAAC;gBAC9ChB,gBAAgB,CAAC0E,SAAS,CAAC;gBAC3B5E,cAAc,CAAC8E,KAAK,CAACC,IAAI,CAAC;kBAAE1D,MAAM,EAAEuD;gBAAU,CAAC,EAAE,CAACI,CAAC,EAAEC,CAAC,KAAKlF,WAAW,CAACkF,CAAC,CAAC,IAAI;kBAAEpE,IAAI,EAAE,EAAE;kBAAEpC,KAAK,EAAE,CAAC;kBAAEE,WAAW,EAAE,EAAE;kBAAEyC,OAAO,EAAE;gBAAK,CAAC,CAAC,CAAC;cACvI;YAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEb7E,OAAA;YAAAuE,QAAA,EAAI;UAAwB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAChCzC,WAAW,CAAC0D,GAAG,CAAC,CAACyB,GAAG,EAAEC,KAAK,kBAC1BxH,OAAA;YAAiBwE,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC1CvE,OAAA;cAAAuE,QAAA,GAAI,SAAI,EAACiD,KAAK,GAAG,CAAC;YAAA;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxB7E,OAAA,CAACX,IAAI,CAACoG,KAAK;cAACjB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAACwG,KAAK;gBAAAtB,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpC7E,OAAA,CAACX,IAAI,CAAC6G,MAAM;gBACV3C,KAAK,EAAEgE,GAAG,CAACrE,IAAK;gBAChB4B,QAAQ,EAAGqB,CAAC,IAAK;kBACf,MAAMsB,cAAc,GAAG,CAAC,GAAGrF,WAAW,CAAC;kBACvCqF,cAAc,CAACD,KAAK,CAAC,CAACtE,IAAI,GAAGiD,CAAC,CAACC,MAAM,CAAC7C,KAAK;kBAC3ClB,cAAc,CAACoF,cAAc,CAAC;gBAChC,CAAE;gBAAAlD,QAAA,gBAEFvE,OAAA;kBAAQuD,KAAK,EAAC,EAAE;kBAAAgB,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrC7E,OAAA;kBAAQuD,KAAK,EAAC,QAAQ;kBAAAgB,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzC7E,OAAA;kBAAQuD,KAAK,EAAC,SAAS;kBAAAgB,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3C7E,OAAA;kBAAQuD,KAAK,EAAC,QAAQ;kBAAAgB,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvC7E,OAAA;kBAAQuD,KAAK,EAAC,UAAU;kBAAAgB,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACb7E,OAAA,CAACX,IAAI,CAACoG,KAAK;cAACjB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAACwG,KAAK;gBAAAtB,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9B7E,OAAA,CAACX,IAAI,CAACgH,OAAO;gBACXnD,IAAI,EAAC,QAAQ;gBACbK,KAAK,EAAEgE,GAAG,CAACzG,KAAM;gBACjBgE,QAAQ,EAAGqB,CAAC,IAAK;kBACf,MAAMsB,cAAc,GAAG,CAAC,GAAGrF,WAAW,CAAC;kBACvCqF,cAAc,CAACD,KAAK,CAAC,CAAC1G,KAAK,GAAGwF,UAAU,CAACH,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAC;kBACxDlB,cAAc,CAACoF,cAAc,CAAC;gBAChC;cAAE;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eACb7E,OAAA,CAACX,IAAI,CAACoG,KAAK;cAACjB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAACwG,KAAK;gBAAAtB,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1C7E,OAAA,CAACX,IAAI,CAAC6G,MAAM;gBACV3C,KAAK,EAAEgE,GAAG,CAAC9D,OAAO,IAAI,EAAG;gBACzBqB,QAAQ,EAAGqB,CAAC,IAAK;kBACf,MAAMsB,cAAc,GAAG,CAAC,GAAGrF,WAAW,CAAC;kBACvC,MAAMsF,eAAe,GAAGvB,CAAC,CAACC,MAAM,CAAC7C,KAAK;kBACtCkE,cAAc,CAACD,KAAK,CAAC,CAAC/D,OAAO,GAAGiE,eAAe,IAAI,IAAI;kBACvD,IAAIA,eAAe,EAAE;oBACnB,MAAMvE,KAAK,GAAGjC,eAAe,CAACyC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACR,EAAE,KAAKsE,eAAe,CAAC;oBACjE,IAAIvE,KAAK,EAAE;sBACTsE,cAAc,CAACD,KAAK,CAAC,CAACtE,IAAI,GAAG,QAAQ;sBACrCuE,cAAc,CAACD,KAAK,CAAC,CAAC1G,KAAK,GAAGqC,KAAK,CAACU,UAAU;sBAC9C4D,cAAc,CAACD,KAAK,CAAC,CAACxG,WAAW,GAAG,GAAGmC,KAAK,CAACW,IAAI,KAAKX,KAAK,CAACY,SAAS,MAAMZ,KAAK,CAACa,OAAO,KAAKb,KAAK,CAACc,YAAY,eAAe;oBAChI;kBACF,CAAC,MAAM;oBACLwD,cAAc,CAACD,KAAK,CAAC,CAACxG,WAAW,GAAG,EAAE;kBACxC;kBACAqB,cAAc,CAACoF,cAAc,CAAC;gBAChC,CAAE;gBAAAlD,QAAA,gBAEFvE,OAAA;kBAAQuD,KAAK,EAAC,EAAE;kBAAAgB,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAChC3D,eAAe,CAAC4E,GAAG,CAAE3C,KAAK,iBACzBnD,OAAA;kBAAuBuD,KAAK,EAAEJ,KAAK,CAACC,EAAG;kBAAAmB,QAAA,GACpCpB,KAAK,CAACW,IAAI,EAAC,KAAG,EAACX,KAAK,CAACY,SAAS,EAAC,KAAG,EAACZ,KAAK,CAACa,OAAO,EAAC,IAAE,EAACb,KAAK,CAACU,UAAU,EAAC,IACxE;gBAAA,GAFaV,KAAK,CAACC,EAAE;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACb7E,OAAA,CAACX,IAAI,CAACoG,KAAK;cAACjB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAACwG,KAAK;gBAAAtB,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpC7E,OAAA,CAACX,IAAI,CAACgH,OAAO;gBACXG,EAAE,EAAC,UAAU;gBACbC,IAAI,EAAE,CAAE;gBACRlD,KAAK,EAAEgE,GAAG,CAACvG,WAAY;gBACvB8D,QAAQ,EAAGqB,CAAC,IAAK;kBACf,MAAMsB,cAAc,GAAG,CAAC,GAAGrF,WAAW,CAAC;kBACvCqF,cAAc,CAACD,KAAK,CAAC,CAACxG,WAAW,GAAGmF,CAAC,CAACC,MAAM,CAAC7C,KAAK;kBAClDlB,cAAc,CAACoF,cAAc,CAAC;gBAChC;cAAE;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA,GAxEL2C,KAAK;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyEV,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACb7E,OAAA,CAACZ,KAAK,CAACyH,MAAM;QAAAtC,QAAA,gBACXvE,OAAA,CAACb,MAAM;UACL6F,OAAO,EAAC,WAAW;UACnBC,OAAO,EAAEA,CAAA,KAAM;YACblD,mBAAmB,CAAC,KAAK,CAAC;YAC1BF,QAAQ,CAAC,EAAE,CAAC;UACd,CAAE;UAAA0C,QAAA,EACH;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7E,OAAA,CAACb,MAAM;UACL6F,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAEd,kBAAmB;UAC5BuC,QAAQ,EAAEhF,OAAQ;UAAA6C,QAAA,EAEjB7C,OAAO,GAAG,cAAc,GAAG;QAAgB;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB;AAACzE,EAAA,CAncQD,YAAY;AAAAwH,EAAA,GAAZxH,YAAY;AAqcrB,eAAeA,YAAY;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}