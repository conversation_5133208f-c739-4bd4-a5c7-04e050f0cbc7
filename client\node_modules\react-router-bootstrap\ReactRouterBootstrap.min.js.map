{"version": 3, "file": "ReactRouterBootstrap.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,EAAQG,QAAQ,SAAUA,QAAQ,qBAC1B,mBAAXC,QAAyBA,OAAOC,IAC9CD,OAAO,CAAC,QAAS,oBAAqBJ,GACZ,iBAAZC,QACdA,QAA8B,qBAAID,EAAQG,QAAQ,SAAUA,QAAQ,qBAEpEJ,EAA2B,qBAAIC,EAAQD,EAAY,MAAGA,EAAqB,gBAR7E,CASGO,MAAM,CAACC,EAAkCC,I,wCCA5C,IAAIC,EAAuB,EAAQ,KAEnC,SAASC,KACT,SAASC,KACTA,EAAuBC,kBAAoBF,EAE3CR,EAAOD,QAAU,WACf,SAASY,EAAKC,EAAOC,EAAUC,EAAeC,EAAUC,EAAcC,GACpE,GAAIA,IAAWV,EAAf,CAIA,IAAIW,EAAM,IAAIC,MACZ,mLAKF,MADAD,EAAIE,KAAO,sBACLF,GAGR,SAASG,IACP,OAAOV,EAFTA,EAAKW,WAAaX,EAMlB,IAAIY,EAAiB,CACnBC,MAAOb,EACPc,OAAQd,EACRe,KAAMf,EACNgB,KAAMhB,EACNiB,OAAQjB,EACRkB,OAAQlB,EACRmB,OAAQnB,EACRoB,OAAQpB,EAERqB,IAAKrB,EACLsB,QAASZ,EACTa,QAASvB,EACTwB,YAAaxB,EACbyB,WAAYf,EACZgB,KAAM1B,EACN2B,SAAUjB,EACVkB,MAAOlB,EACPmB,UAAWnB,EACXoB,MAAOpB,EACPqB,MAAOrB,EAEPsB,eAAgBlC,EAChBC,kBAAmBF,GAKrB,OAFAe,EAAeqB,UAAYrB,EAEpBA,I,cC9CPvB,EAAOD,QAAU,EAAQ,IAAR,I,qBCNnBC,EAAOD,QAFoB,gD,qBCT3BC,EAAOD,QAAUM,G,qBCAjBL,EAAOD,QAAUO,ICCbuC,EAA2B,GAG/B,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAajD,QAGrB,IAAIC,EAAS6C,EAAyBE,GAAY,CAGjDhD,QAAS,IAOV,OAHAmD,EAAoBH,GAAU/C,EAAQA,EAAOD,QAAS+C,GAG/C9C,EAAOD,QCpBf+C,EAAoBK,EAAKnD,IACxB,IAAIoD,EAASpD,GAAUA,EAAOqD,WAC7B,IAAOrD,EAAiB,QACxB,IAAM,EAEP,OADA8C,EAAoBQ,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRN,EAAoBQ,EAAI,CAACvD,EAASyD,KACjC,IAAI,IAAIC,KAAOD,EACXV,EAAoBY,EAAEF,EAAYC,KAASX,EAAoBY,EAAE3D,EAAS0D,IAC5EE,OAAOC,eAAe7D,EAAS0D,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3EX,EAAoBY,EAAI,CAACK,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFlB,EAAoBsB,EAAKrE,IACH,oBAAXsE,QAA0BA,OAAOC,aAC1CX,OAAOC,eAAe7D,EAASsE,OAAOC,YAAa,CAAEC,MAAO,WAE7DZ,OAAOC,eAAe7D,EAAS,aAAc,CAAEwE,OAAO,K,moCCDvD,IAGMC,EAAgB,SAAC,GAajB,IAZJC,EAYI,EAZJA,SACAC,EAWI,EAXJA,QAWI,IAVJC,QAAAA,OAUI,SATJC,EASI,EATJA,GACAC,EAQI,EARJA,MAQI,IAPJC,gBAAAA,OAOI,MAPc,SAOd,EANJC,EAMI,EANJA,UACAC,EAKI,EALJA,YACAC,EAII,EAJJA,MACUC,EAGN,EAHJC,SAEGvE,E,kXACC,MACEwE,EAAqB,WAAd,EAAOR,GAAkBA,EAAGS,UAAY,GAAKT,EACpDU,GAAWC,EAAAA,EAAAA,eACXC,GAAOC,EAAAA,EAAAA,SAAsB,iBAAPb,EAAkB,CAAES,SAAUT,GAAOA,GAC3Dc,GAAQC,EAAAA,EAAAA,UAASP,GACjBrE,GAAW6E,EAAAA,EAAAA,eACXC,EAAQC,IAAAA,SAAAA,KAAoBrB,GAE5BU,KAAcD,EACO,mBAAhBA,EACLA,EAAYQ,EAAO3E,GACnBmE,EACFQ,GAyBJ,OAAOI,IAAAA,aAAmBD,EAAnB,OACFjF,GADE,IAELmE,UAAW,CACTA,EACAc,EAAMjF,MAAMmE,UACZI,EAAWL,EAAkB,MAE5BiB,KAAK,KACLC,OACHf,MAAOE,EAAW,EAAH,KAAQF,GAAUD,GAAgBC,EACjDO,KAAAA,EACAd,QAlCkB,SAACuB,GACfxB,EAAS7D,MAAM8D,SACjBD,EAAS7D,MAAM8D,QAAQuB,GAGrBvB,GACFA,EAAQuB,GAIPA,EAAMC,kBACU,IAAjBD,EAAME,QAzCY,SAACF,GAAD,SACnBA,EAAMG,SAAWH,EAAMI,QAAUJ,EAAMK,SAAWL,EAAMM,UAyCtDC,CAAgBP,KAEjBA,EAAMQ,iBAENnB,EAASV,EAAI,CACXD,QAAAA,EACAE,MAAAA,UAoBRL,EAAckC,UAAY,CACxBjC,SAAU7B,IAAAA,QAAAA,WACV8B,QAAS9B,IAAAA,KACT+B,QAAS/B,IAAAA,KACTgC,GAAIhC,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,SAAmBtB,WAC9DuD,MAAOjC,IAAAA,OACPmC,UAAWnC,IAAAA,OACXkC,gBAAiBlC,IAAAA,OACjBqC,MAAOrC,IAAAA,SACLA,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAEzCoC,YAAapC,IAAAA,SACXA,IAAAA,UAAoB,CAACA,IAAAA,OAAkBA,IAAAA,UAEzCuC,SAAUvC,IAAAA,UAAoB,CAACA,IAAAA,KAAgBA,IAAAA,QAGjD,W", "sources": ["webpack://ReactRouterBootstrap/webpack/universalModuleDefinition", "webpack://ReactRouterBootstrap/./node_modules/prop-types/factoryWithThrowingShims.js", "webpack://ReactRouterBootstrap/./node_modules/prop-types/index.js", "webpack://ReactRouterBootstrap/./node_modules/prop-types/lib/ReactPropTypesSecret.js", "webpack://ReactRouterBootstrap/external umd {\"root\":\"React\",\"commonjs2\":\"react\",\"commonjs\":\"react\",\"amd\":\"react\"}", "webpack://ReactRouterBootstrap/external umd {\"root\":\"ReactRouterDOM\",\"commonjs2\":\"react-router-dom\",\"commonjs\":\"react-router-dom\",\"amd\":\"react-router-dom\"}", "webpack://ReactRouterBootstrap/webpack/bootstrap", "webpack://ReactRouterBootstrap/webpack/runtime/compat get default export", "webpack://ReactRouterBootstrap/webpack/runtime/define property getters", "webpack://ReactRouterBootstrap/webpack/runtime/hasOwnProperty shorthand", "webpack://ReactRouterBootstrap/webpack/runtime/make namespace object", "webpack://ReactRouterBootstrap/./src/LinkContainer.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"react\"), require(\"react-router-dom\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"react\", \"react-router-dom\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ReactRouterBootstrap\"] = factory(require(\"react\"), require(\"react-router-dom\"));\n\telse\n\t\troot[\"ReactRouterBootstrap\"] = factory(root[\"React\"], root[\"ReactRouterDOM\"]);\n})(self, (__WEBPACK_EXTERNAL_MODULE__787__, __WEBPACK_EXTERNAL_MODULE__944__) => {\nreturn ", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n\nfunction emptyFunction() {}\nfunction emptyFunctionWithReset() {}\nemptyFunctionWithReset.resetWarningCache = emptyFunction;\n\nmodule.exports = function() {\n  function shim(props, propName, componentName, location, propFullName, secret) {\n    if (secret === ReactPropTypesSecret) {\n      // It is still safe when called from React.\n      return;\n    }\n    var err = new Error(\n      'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n      'Use PropTypes.checkPropTypes() to call them. ' +\n      'Read more at http://fb.me/use-check-prop-types'\n    );\n    err.name = 'Invariant Violation';\n    throw err;\n  };\n  shim.isRequired = shim;\n  function getShim() {\n    return shim;\n  };\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n  var ReactPropTypes = {\n    array: shim,\n    bigint: shim,\n    bool: shim,\n    func: shim,\n    number: shim,\n    object: shim,\n    string: shim,\n    symbol: shim,\n\n    any: shim,\n    arrayOf: getShim,\n    element: shim,\n    elementType: shim,\n    instanceOf: getShim,\n    node: shim,\n    objectOf: getShim,\n    oneOf: getShim,\n    oneOfType: getShim,\n    shape: getShim,\n    exact: getShim,\n\n    checkPropTypes: emptyFunctionWithReset,\n    resetWarningCache: emptyFunction\n  };\n\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__787__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__944__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport { useHref, useLocation, useMatch, useNavigate } from 'react-router-dom';\n\nconst isModifiedEvent = (event) =>\n  !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n\nconst LinkContainer = ({\n  children,\n  onClick,\n  replace = false, // eslint-disable-line no-unused-vars\n  to,\n  state,\n  activeClassName = 'active',\n  className,\n  activeStyle,\n  style,\n  isActive: getIsActive,\n  // eslint-disable-next-line comma-dangle\n  ...props\n}) => {\n  const path = typeof to === 'object' ? to.pathname || '' : to;\n  const navigate = useNavigate();\n  const href = useHref(typeof to === 'string' ? { pathname: to } : to);\n  const match = useMatch(path);\n  const location = useLocation();\n  const child = React.Children.only(children);\n\n  const isActive = !!(getIsActive\n    ? typeof getIsActive === 'function'\n      ? getIsActive(match, location)\n      : getIsActive\n    : match);\n\n  const handleClick = (event) => {\n    if (children.props.onClick) {\n      children.props.onClick(event);\n    }\n\n    if (onClick) {\n      onClick(event);\n    }\n\n    if (\n      !event.defaultPrevented && // onClick prevented default\n      event.button === 0 && // ignore right clicks\n      !isModifiedEvent(event) // ignore clicks with modifier keys\n    ) {\n      event.preventDefault();\n\n      navigate(to, {\n        replace,\n        state,\n      });\n    }\n  };\n\n  return React.cloneElement(child, {\n    ...props,\n    className: [\n      className,\n      child.props.className,\n      isActive ? activeClassName : null,\n    ]\n      .join(' ')\n      .trim(),\n    style: isActive ? { ...style, ...activeStyle } : style,\n    href,\n    onClick: handleClick,\n  });\n};\n\nLinkContainer.propTypes = {\n  children: PropTypes.element.isRequired,\n  onClick: PropTypes.func,\n  replace: PropTypes.bool,\n  to: PropTypes.oneOfType([PropTypes.string, PropTypes.object]).isRequired,\n  state: PropTypes.object,\n  className: PropTypes.string,\n  activeClassName: PropTypes.string,\n  style: PropTypes.objectOf(\n    PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  ),\n  activeStyle: PropTypes.objectOf(\n    PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  ),\n  isActive: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n};\n\nexport default LinkContainer;\n"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "self", "__WEBPACK_EXTERNAL_MODULE__787__", "__WEBPACK_EXTERNAL_MODULE__944__", "ReactPropTypesSecret", "emptyFunction", "emptyFunctionWithReset", "resetWarningCache", "shim", "props", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "secret", "err", "Error", "name", "getShim", "isRequired", "ReactPropTypes", "array", "bigint", "bool", "func", "number", "object", "string", "symbol", "any", "arrayOf", "element", "elementType", "instanceOf", "node", "objectOf", "oneOf", "oneOfType", "shape", "exact", "checkPropTypes", "PropTypes", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "__webpack_modules__", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "r", "Symbol", "toStringTag", "value", "LinkContainer", "children", "onClick", "replace", "to", "state", "activeClassName", "className", "activeStyle", "style", "getIsActive", "isActive", "path", "pathname", "navigate", "useNavigate", "href", "useHref", "match", "useMatch", "useLocation", "child", "React", "join", "trim", "event", "defaultPrevented", "button", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "isModifiedEvent", "preventDefault", "propTypes"], "sourceRoot": ""}