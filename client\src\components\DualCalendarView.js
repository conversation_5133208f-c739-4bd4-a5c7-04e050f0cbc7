import React, { useState, useEffect } from 'react';
import { Container, <PERSON>, Col, Card, Button, Badge, Alert, Tabs, Tab } from 'react-bootstrap';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css';
import {
  getAllRealDays,
  getAllPlannedDays,
  getRealDayByDate,
  getPlannedDayByDate,
  compareCalendars
} from '../services/api';

function DualCalendarView({ onDateSelect, selectedDate }) {
  const [realDays, setRealDays] = useState([]);
  const [plannedDays, setPlannedDays] = useState([]);
  const [comparison, setComparison] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('real');

  useEffect(() => {
    loadCalendarData();
  }, []);

  useEffect(() => {
    if (selectedDate) {
      loadComparison();
    }
  }, [selectedDate, realDays, plannedDays]);

  const loadCalendarData = async () => {
    setLoading(true);
    try {
      const [realData, plannedData] = await Promise.all([
        getAllRealDays(),
        getAllPlannedDays()
      ]);
      setRealDays(realData);
      setPlannedDays(plannedData);
      setError('');
    } catch (err) {
      setError('Error cargando datos del calendario: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const loadComparison = async () => {
    if (!selectedDate) return;
    
    try {
      const startOfMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1);
      const endOfMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1, 0);
      
      const comparisonData = await compareCalendars(
        startOfMonth.toISOString().split('T')[0],
        endOfMonth.toISOString().split('T')[0]
      );
      setComparison(comparisonData);
    } catch (err) {
      console.error('Error loading comparison:', err);
    }
  };

  const getDayData = (date, calendar) => {
    const dateStr = date.toISOString().split('T')[0];
    const days = calendar === 'real' ? realDays : plannedDays;
    return days.find(day => day.date === dateStr);
  };

  const getTileContent = ({ date, view }) => {
    if (view !== 'month') return null;

    const realDay = getDayData(date, 'real');
    const plannedDay = getDayData(date, 'planned');

    return (
      <div className="calendar-tile-content">
        {realDay && (
          <div className="real-indicator">
            <Badge bg="primary" size="sm">{realDay.hours}h</Badge>
          </div>
        )}
        {plannedDay && (
          <div className="planned-indicator">
            <Badge bg="secondary" size="sm">{plannedDay.hours}h</Badge>
          </div>
        )}
      </div>
    );
  };

  const getTileClassName = ({ date, view }) => {
    if (view !== 'month') return null;

    const realDay = getDayData(date, 'real');
    const plannedDay = getDayData(date, 'planned');
    const classes = [];

    if (realDay && realDay.type === 'worked') {
      classes.push('has-real-work');
    }
    if (plannedDay && plannedDay.type === 'worked') {
      classes.push('has-planned-work');
    }
    if (realDay && plannedDay) {
      const variance = Math.abs(realDay.hours - plannedDay.hours);
      if (variance > 1) {
        classes.push('has-variance');
      }
    }

    return classes.join(' ');
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('es-ES', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getSelectedDayInfo = () => {
    if (!selectedDate) return null;

    const realDay = getDayData(selectedDate, 'real');
    const plannedDay = getDayData(selectedDate, 'planned');

    return { realDay, plannedDay };
  };

  const selectedDayInfo = getSelectedDayInfo();

  return (
    <Container fluid>
      {error && <Alert variant="danger">{error}</Alert>}
      
      <Row>
        <Col lg={8}>
          <Card>
            <Card.Header>
              <div className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0">Calendario Dual</h5>
                <div>
                  <Button 
                    variant="outline-primary" 
                    size="sm" 
                    onClick={loadCalendarData}
                    disabled={loading}
                  >
                    {loading ? 'Cargando...' : 'Actualizar'}
                  </Button>
                </div>
              </div>
            </Card.Header>
            <Card.Body>
              <div className="calendar-legend mb-3">
                <small className="text-muted">
                  <Badge bg="primary" className="me-2">Real</Badge>
                  <Badge bg="secondary" className="me-2">Planificado</Badge>
                  <span className="calendar-legend-item has-variance me-2">Varianza</span>
                </small>
              </div>
              
              <Calendar
                onChange={onDateSelect}
                value={selectedDate}
                tileContent={getTileContent}
                tileClassName={getTileClassName}
                locale="es-ES"
              />
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          <Card>
            <Card.Header>
              <h6 className="mb-0">
                {selectedDate ? formatDate(selectedDate) : 'Selecciona una fecha'}
              </h6>
            </Card.Header>
            <Card.Body>
              {selectedDate && selectedDayInfo && (
                <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-3">
                  <Tab eventKey="real" title="Real">
                    {selectedDayInfo.realDay ? (
                      <div>
                        <p><strong>Tipo:</strong> {selectedDayInfo.realDay.type || 'No definido'}</p>
                        <p><strong>Horas:</strong> {selectedDayInfo.realDay.hours || 0}</p>
                        <p><strong>Descripción:</strong> {selectedDayInfo.realDay.description || 'Sin descripción'}</p>
                        {selectedDayInfo.realDay.shift && (
                          <p><strong>Turno:</strong> {selectedDayInfo.realDay.shift.name}</p>
                        )}
                      </div>
                    ) : (
                      <p className="text-muted">No hay datos reales para esta fecha</p>
                    )}
                  </Tab>
                  
                  <Tab eventKey="planned" title="Planificado">
                    {selectedDayInfo.plannedDay ? (
                      <div>
                        <p><strong>Tipo:</strong> {selectedDayInfo.plannedDay.type || 'No definido'}</p>
                        <p><strong>Horas:</strong> {selectedDayInfo.plannedDay.hours || 0}</p>
                        <p><strong>Descripción:</strong> {selectedDayInfo.plannedDay.description || 'Sin descripción'}</p>
                        {selectedDayInfo.plannedDay.shift && (
                          <p><strong>Turno:</strong> {selectedDayInfo.plannedDay.shift.name}</p>
                        )}
                      </div>
                    ) : (
                      <p className="text-muted">No hay datos planificados para esta fecha</p>
                    )}
                  </Tab>
                  
                  <Tab eventKey="comparison" title="Comparación">
                    {selectedDayInfo.realDay && selectedDayInfo.plannedDay ? (
                      <div>
                        <p><strong>Varianza de horas:</strong> 
                          <Badge bg={Math.abs(selectedDayInfo.realDay.hours - selectedDayInfo.plannedDay.hours) > 1 ? 'warning' : 'success'}>
                            {(selectedDayInfo.realDay.hours - selectedDayInfo.plannedDay.hours).toFixed(2)}h
                          </Badge>
                        </p>
                        <p><strong>Cumplimiento:</strong> 
                          <Badge bg={selectedDayInfo.realDay.type === selectedDayInfo.plannedDay.type ? 'success' : 'warning'}>
                            {selectedDayInfo.realDay.type === selectedDayInfo.plannedDay.type ? 'Cumple' : 'No cumple'}
                          </Badge>
                        </p>
                      </div>
                    ) : (
                      <p className="text-muted">Necesitas datos en ambos calendarios para comparar</p>
                    )}
                  </Tab>
                </Tabs>
              )}
              
              {!selectedDate && (
                <p className="text-muted">Haz clic en una fecha del calendario para ver los detalles</p>
              )}
            </Card.Body>
          </Card>

          {comparison && (
            <Card className="mt-3">
              <Card.Header>
                <h6 className="mb-0">Resumen del Mes</h6>
              </Card.Header>
              <Card.Body>
                <p><strong>Días con datos reales:</strong> {comparison.summary?.realDaysCount || 0}</p>
                <p><strong>Días planificados:</strong> {comparison.summary?.plannedDaysCount || 0}</p>
                <p><strong>Cumplimiento promedio:</strong> 
                  <Badge bg="info" className="ms-2">
                    {comparison.summary?.averageCompliance ? `${(comparison.summary.averageCompliance * 100).toFixed(1)}%` : 'N/A'}
                  </Badge>
                </p>
              </Card.Body>
            </Card>
          )}
        </Col>
      </Row>
    </Container>
  );
}

export default DualCalendarView;
