{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\components\\\\RealCalendarView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Button, Alert } from 'react-bootstrap';\nimport { getAllRealDays } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction RealCalendarView({\n  onDateSelect,\n  selectedDate,\n  onCalendarUpdate\n}) {\n  _s();\n  const [realDays, setRealDays] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    loadRealDays();\n  }, []);\n  const loadRealDays = async () => {\n    try {\n      setLoading(true);\n      const days = await getAllRealDays();\n      setRealDays(days);\n      setError('');\n    } catch (err) {\n      console.error('Error loading real days:', err);\n      setError('Error cargando días reales: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getTotalHours = () => {\n    return realDays.reduce((total, day) => total + (day.hours || 0), 0);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Calendario de Trabajo Real\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              onClick: () => alert('Funcionalidad en desarrollo'),\n              children: \"\\u2795 Registrar D\\xEDa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          onClose: () => setError(''),\n          dismissible: true,\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"calendar-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Calendario visual estar\\xE1 disponible pronto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), loading && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Cargando d\\xEDas reales...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 4,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"calendar-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Estad\\xEDsticas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total de d\\xEDas registrados:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), \" \", realDays.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total de horas:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), \" \", getTotalHours().toFixed(1), \"h\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), selectedDate && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"selected-date-info mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Fecha seleccionada:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: selectedDate.toLocaleDateString('es-ES', {\n              weekday: 'long',\n              year: 'numeric',\n              month: 'long',\n              day: 'numeric'\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n}\n_s(RealCalendarView, \"LuMqwan3FYkBXFLgcVf5eFR4KAs=\");\n_c = RealCalendarView;\nexport default RealCalendarView;\nvar _c;\n$RefreshReg$(_c, \"RealCalendarView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "<PERSON><PERSON>", "<PERSON><PERSON>", "getAllRealDays", "jsxDEV", "_jsxDEV", "RealCalendarView", "onDateSelect", "selectedDate", "onCalendarUpdate", "_s", "realDays", "setRealDays", "loading", "setLoading", "error", "setError", "loadRealDays", "days", "err", "console", "message", "getTotalHours", "reduce", "total", "day", "hours", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "alert", "onClose", "dismissible", "md", "length", "toFixed", "toLocaleDateString", "weekday", "year", "month", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/components/RealCalendarView.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Button, Alert } from 'react-bootstrap';\nimport { getAllRealDays } from '../services/api';\n\nfunction RealCalendarView({ onDateSelect, selectedDate, onCalendarUpdate }) {\n  const [realDays, setRealDays] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    loadRealDays();\n  }, []);\n\n  const loadRealDays = async () => {\n    try {\n      setLoading(true);\n      const days = await getAllRealDays();\n      setRealDays(days);\n      setError('');\n    } catch (err) {\n      console.error('Error loading real days:', err);\n      setError('Error cargando días reales: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getTotalHours = () => {\n    return realDays.reduce((total, day) => total + (day.hours || 0), 0);\n  };\n\n  return (\n    <div>\n      <Row className=\"mb-3\">\n        <Col>\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <h4>Calendario de Trabajo Real</h4>\n            <div>\n              <Button\n                variant=\"primary\"\n                onClick={() => alert('Funcionalidad en desarrollo')}\n              >\n                ➕ Registrar Día\n              </Button>\n            </div>\n          </div>\n        </Col>\n      </Row>\n\n      {error && (\n        <Row className=\"mb-3\">\n          <Col>\n            <Alert variant=\"danger\" onClose={() => setError('')} dismissible>\n              {error}\n            </Alert>\n          </Col>\n        </Row>\n      )}\n\n      <Row className=\"mb-3\">\n        <Col md={8}>\n          <div className=\"calendar-container\">\n            <p>Calendario visual estará disponible pronto</p>\n            {loading && <p>Cargando días reales...</p>}\n          </div>\n        </Col>\n        <Col md={4}>\n          <div className=\"calendar-stats\">\n            <h5>Estadísticas</h5>\n            <div className=\"stat-item\">\n              <strong>Total de días registrados:</strong> {realDays.length}\n            </div>\n            <div className=\"stat-item\">\n              <strong>Total de horas:</strong> {getTotalHours().toFixed(1)}h\n            </div>\n          </div>\n\n          {selectedDate && (\n            <div className=\"selected-date-info mt-3\">\n              <h6>Fecha seleccionada:</h6>\n              <p>{selectedDate.toLocaleDateString('es-ES', {\n                weekday: 'long',\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n              })}</p>\n            </div>\n          )}\n        </Col>\n      </Row>\n    </div>\n  );\n}\n\nexport default RealCalendarView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,QAAQ,iBAAiB;AACzD,SAASC,cAAc,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,gBAAgBA,CAAC;EAAEC,YAAY;EAAEC,YAAY;EAAEC;AAAiB,CAAC,EAAE;EAAAC,EAAA;EAC1E,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACdmB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,IAAI,GAAG,MAAMf,cAAc,CAAC,CAAC;MACnCS,WAAW,CAACM,IAAI,CAAC;MACjBF,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAACL,KAAK,CAAC,0BAA0B,EAAEI,GAAG,CAAC;MAC9CH,QAAQ,CAAC,8BAA8B,GAAGG,GAAG,CAACE,OAAO,CAAC;IACxD,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAOX,QAAQ,CAACY,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAKD,KAAK,IAAIC,GAAG,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACrE,CAAC;EAED,oBACErB,OAAA;IAAAsB,QAAA,gBACEtB,OAAA,CAACN,GAAG;MAAC6B,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBtB,OAAA,CAACL,GAAG;QAAA2B,QAAA,eACFtB,OAAA;UAAKuB,SAAS,EAAC,mDAAmD;UAAAD,QAAA,gBAChEtB,OAAA;YAAAsB,QAAA,EAAI;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnC3B,OAAA;YAAAsB,QAAA,eACEtB,OAAA,CAACJ,MAAM;cACLgC,OAAO,EAAC,SAAS;cACjBC,OAAO,EAAEA,CAAA,KAAMC,KAAK,CAAC,6BAA6B,CAAE;cAAAR,QAAA,EACrD;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELjB,KAAK,iBACJV,OAAA,CAACN,GAAG;MAAC6B,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBtB,OAAA,CAACL,GAAG;QAAA2B,QAAA,eACFtB,OAAA,CAACH,KAAK;UAAC+B,OAAO,EAAC,QAAQ;UAACG,OAAO,EAAEA,CAAA,KAAMpB,QAAQ,CAAC,EAAE,CAAE;UAACqB,WAAW;UAAAV,QAAA,EAC7DZ;QAAK;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED3B,OAAA,CAACN,GAAG;MAAC6B,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBtB,OAAA,CAACL,GAAG;QAACsC,EAAE,EAAE,CAAE;QAAAX,QAAA,eACTtB,OAAA;UAAKuB,SAAS,EAAC,oBAAoB;UAAAD,QAAA,gBACjCtB,OAAA;YAAAsB,QAAA,EAAG;UAA0C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,EAChDnB,OAAO,iBAAIR,OAAA;YAAAsB,QAAA,EAAG;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN3B,OAAA,CAACL,GAAG;QAACsC,EAAE,EAAE,CAAE;QAAAX,QAAA,gBACTtB,OAAA;UAAKuB,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC7BtB,OAAA;YAAAsB,QAAA,EAAI;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrB3B,OAAA;YAAKuB,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxBtB,OAAA;cAAAsB,QAAA,EAAQ;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACrB,QAAQ,CAAC4B,MAAM;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACN3B,OAAA;YAAKuB,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxBtB,OAAA;cAAAsB,QAAA,EAAQ;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACV,aAAa,CAAC,CAAC,CAACkB,OAAO,CAAC,CAAC,CAAC,EAAC,GAC/D;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELxB,YAAY,iBACXH,OAAA;UAAKuB,SAAS,EAAC,yBAAyB;UAAAD,QAAA,gBACtCtB,OAAA;YAAAsB,QAAA,EAAI;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5B3B,OAAA;YAAAsB,QAAA,EAAInB,YAAY,CAACiC,kBAAkB,CAAC,OAAO,EAAE;cAC3CC,OAAO,EAAE,MAAM;cACfC,IAAI,EAAE,SAAS;cACfC,KAAK,EAAE,MAAM;cACbnB,GAAG,EAAE;YACP,CAAC;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACtB,EAAA,CAxFQJ,gBAAgB;AAAAuC,EAAA,GAAhBvC,gBAAgB;AA0FzB,eAAeA,gBAAgB;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}