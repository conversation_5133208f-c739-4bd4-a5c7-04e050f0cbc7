.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Calendar pattern selection styles */
.selected-range {
  background-color: #a0d9b5 !important;
  /* Light green for selected range */
  border-radius: 0;
}

.selected-range-start {
  background-color: #4CAF50 !important;
  /* Darker green for start date */
  color: white;
  border-radius: 50%;
}

.react-calendar__tile--active {
  background-color: #0069d9 !important;
  /* Default active tile color */
}

/* ===== ESTILOS PARA CALENDARIO DUAL ===== */

.calendar-tile-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  margin-top: 2px;
}

.real-indicator .badge {
  font-size: 0.6em;
  padding: 1px 3px;
}

.planned-indicator .badge {
  font-size: 0.6em;
  padding: 1px 3px;
}

.react-calendar__tile.has-real-work {
  background-color: #e3f2fd !important;
  border-left: 3px solid #2196f3;
}

.react-calendar__tile.has-planned-work {
  background-color: #f3e5f5 !important;
  border-right: 3px solid #9c27b0;
}

.react-calendar__tile.has-variance {
  background-color: #fff3e0 !important;
  border-bottom: 3px solid #ff9800;
}

.react-calendar__tile.has-real-work.has-planned-work {
  background: linear-gradient(45deg, #e3f2fd 50%, #f3e5f5 50%) !important;
}

.calendar-legend {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.calendar-legend-item.has-variance {
  padding: 2px 8px;
  background-color: #fff3e0;
  border: 1px solid #ff9800;
  border-radius: 3px;
  font-size: 0.8em;
}

.dual-calendar .react-calendar {
  width: 100%;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
}

.dual-calendar .react-calendar__tile {
  position: relative;
  height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding: 4px;
}

.dual-calendar .react-calendar__tile:enabled:hover,
.dual-calendar .react-calendar__tile:enabled:focus {
  background-color: #e6e6e6;
}

.dual-calendar .react-calendar__tile--active {
  background: #006edc !important;
  color: white;
}

.dual-calendar .react-calendar__tile--active:enabled:hover,
.dual-calendar .react-calendar__tile--active:enabled:focus {
  background: #1087ff !important;
}

/* ===== ESTILOS PARA CALENDARIOS SEPARADOS ===== */

.calendar-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.calendar-stats {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #dee2e6;
}

.stat-item {
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.stat-item:last-child {
  border-bottom: none;
}

.selected-date-info {
  background: white;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #dee2e6;
}

.day-details {
  margin-top: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.pattern-info {
  background: white;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #dee2e6;
}

/* Estilos específicos para tiles del calendario real */
.react-calendar__tile.has-real-work {
  background-color: #e3f2fd !important;
  border-color: #2196f3 !important;
}

.react-calendar__tile.has-real-work:hover {
  background-color: #bbdefb !important;
}

/* Estilos específicos para tiles del calendario planificado */
.react-calendar__tile.has-planned-work {
  background-color: #f3e5f5 !important;
  border-color: #9c27b0 !important;
}

.react-calendar__tile.has-planned-work:hover {
  background-color: #e1bee7 !important;
}

/* Estilos para vista previa de patrones */
.pattern-preview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
  border: 1px solid #dee2e6;
}

.pattern-days {
  max-height: 200px;
  overflow-y: auto;
}

.pattern-day-preview {
  padding: 5px 0;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
}

.pattern-day-preview:last-child {
  border-bottom: none;
}