{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\components\\\\CalendarAnalysis.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Badge, Alert, Button, Form, Table } from 'react-bootstrap';\nimport { compareCalendars, getComplianceStats, getVarianceAnalysis } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CalendarAnalysis({\n  selectedDate\n}) {\n  _s();\n  var _comparison$summary, _comparison$summary2, _comparison$summary3, _comparison$summary4, _comparison$summary5;\n  const [comparison, setComparison] = useState(null);\n  const [compliance, setCompliance] = useState(null);\n  const [variance, setVariance] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [dateRange, setDateRange] = useState({\n    startDate: '',\n    endDate: ''\n  });\n  useEffect(() => {\n    if (selectedDate) {\n      // Establecer rango por defecto al mes actual\n      const startOfMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1);\n      const endOfMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1, 0);\n      setDateRange({\n        startDate: startOfMonth.toISOString().split('T')[0],\n        endDate: endOfMonth.toISOString().split('T')[0]\n      });\n    }\n  }, [selectedDate]);\n  useEffect(() => {\n    if (dateRange.startDate && dateRange.endDate) {\n      loadAnalysis();\n    }\n  }, [dateRange]);\n  const loadAnalysis = async () => {\n    if (!dateRange.startDate || !dateRange.endDate) return;\n    setLoading(true);\n    setError('');\n    try {\n      const [comparisonData, complianceData, varianceData] = await Promise.all([compareCalendars(dateRange.startDate, dateRange.endDate), getComplianceStats(dateRange.startDate, dateRange.endDate), getVarianceAnalysis(dateRange.startDate, dateRange.endDate)]);\n      setComparison(comparisonData);\n      setCompliance(complianceData);\n      setVariance(varianceData);\n    } catch (err) {\n      setError('Error cargando análisis: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatPercentage = value => {\n    return value ? `${(value * 100).toFixed(1)}%` : 'N/A';\n  };\n  const formatHours = value => {\n    return value ? `${value.toFixed(1)}h` : '0h';\n  };\n  const getVarianceBadge = variance => {\n    if (Math.abs(variance) <= 0.5) return 'success';\n    if (Math.abs(variance) <= 2) return 'warning';\n    return 'danger';\n  };\n  const getComplianceBadge = compliance => {\n    if (compliance >= 0.9) return 'success';\n    if (compliance >= 0.7) return 'warning';\n    return 'danger';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"danger\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        children: /*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-0\",\n          children: \"Per\\xEDodo de An\\xE1lisis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Form.Group, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Fecha inicio\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"date\",\n                value: dateRange.startDate,\n                onChange: e => setDateRange(prev => ({\n                  ...prev,\n                  startDate: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Form.Group, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Fecha fin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"date\",\n                value: dateRange.endDate,\n                onChange: e => setDateRange(prev => ({\n                  ...prev,\n                  endDate: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 4,\n            className: \"d-flex align-items-end\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              onClick: loadAnalysis,\n              disabled: loading || !dateRange.startDate || !dateRange.endDate,\n              children: loading ? 'Analizando...' : 'Actualizar Análisis'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), comparison && /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"text-primary\",\n              children: ((_comparison$summary = comparison.summary) === null || _comparison$summary === void 0 ? void 0 : _comparison$summary.realDaysCount) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: \"D\\xEDas Reales\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"text-secondary\",\n              children: ((_comparison$summary2 = comparison.summary) === null || _comparison$summary2 === void 0 ? void 0 : _comparison$summary2.plannedDaysCount) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: \"D\\xEDas Planificados\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"text-success\",\n              children: ((_comparison$summary3 = comparison.summary) === null || _comparison$summary3 === void 0 ? void 0 : _comparison$summary3.matchingDaysCount) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: \"D\\xEDas Coincidentes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                bg: getComplianceBadge(((_comparison$summary4 = comparison.summary) === null || _comparison$summary4 === void 0 ? void 0 : _comparison$summary4.averageCompliance) || 0),\n                children: formatPercentage((_comparison$summary5 = comparison.summary) === null || _comparison$summary5 === void 0 ? void 0 : _comparison$summary5.averageCompliance)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: \"Cumplimiento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 9\n    }, this), compliance && /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        children: /*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-0\",\n          children: \"Estad\\xEDsticas de Cumplimiento\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Horas planificadas:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 20\n              }, this), \" \", formatHours(compliance.totalPlannedHours)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Horas reales:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 20\n              }, this), \" \", formatHours(compliance.totalRealHours)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Diferencia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 20\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                bg: getVarianceBadge(compliance.totalRealHours - compliance.totalPlannedHours),\n                className: \"ms-2\",\n                children: formatHours(compliance.totalRealHours - compliance.totalPlannedHours)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"D\\xEDas cumplidos:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 20\n              }, this), \" \", compliance.compliantDays || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"D\\xEDas no cumplidos:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 20\n              }, this), \" \", compliance.nonCompliantDays || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tasa de cumplimiento:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 20\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                bg: getComplianceBadge(compliance.complianceRate),\n                className: \"ms-2\",\n                children: formatPercentage(compliance.complianceRate)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 9\n    }, this), variance && variance.dailyVariances && variance.dailyVariances.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        children: /*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-0\",\n          children: \"Varianzas Diarias\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxHeight: '300px',\n            overflowY: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            striped: true,\n            bordered: true,\n            hover: true,\n            size: \"sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Fecha\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Real\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Planificado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Varianza\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Estado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: variance.dailyVariances.map((day, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: new Date(day.date).toLocaleDateString('es-ES')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: formatHours(day.realHours)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: formatHours(day.plannedHours)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(Badge, {\n                    bg: getVarianceBadge(day.variance),\n                    children: formatHours(day.variance)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(Badge, {\n                    bg: day.compliant ? 'success' : 'warning',\n                    children: day.compliant ? 'Cumple' : 'No cumple'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 9\n    }, this), comparison && comparison.details && comparison.details.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        children: /*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-0\",\n          children: \"Detalles de Comparaci\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxHeight: '400px',\n            overflowY: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            striped: true,\n            bordered: true,\n            hover: true,\n            size: \"sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Fecha\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Tipo Real\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Horas Real\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Tipo Planificado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Horas Planificado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Coincide\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: comparison.details.map((detail, index) => {\n                var _detail$realDay, _detail$realDay2, _detail$plannedDay, _detail$plannedDay2;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: new Date(detail.date).toLocaleDateString('es-ES')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: ((_detail$realDay = detail.realDay) === null || _detail$realDay === void 0 ? void 0 : _detail$realDay.type) || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatHours(((_detail$realDay2 = detail.realDay) === null || _detail$realDay2 === void 0 ? void 0 : _detail$realDay2.hours) || 0)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: ((_detail$plannedDay = detail.plannedDay) === null || _detail$plannedDay === void 0 ? void 0 : _detail$plannedDay.type) || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatHours(((_detail$plannedDay2 = detail.plannedDay) === null || _detail$plannedDay2 === void 0 ? void 0 : _detail$plannedDay2.hours) || 0)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: detail.matches ? 'success' : 'warning',\n                      children: detail.matches ? 'Sí' : 'No'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 9\n    }, this), !comparison && !loading && dateRange.startDate && dateRange.endDate && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"info\",\n      children: \"Haz clic en \\\"Actualizar An\\xE1lisis\\\" para ver la comparaci\\xF3n entre calendarios.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n}\n_s(CalendarAnalysis, \"1WTADfNk99ylCP5zstOsS0RfTMs=\");\n_c = CalendarAnalysis;\nexport default CalendarAnalysis;\nvar _c;\n$RefreshReg$(_c, \"CalendarAnalysis\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Badge", "<PERSON><PERSON>", "<PERSON><PERSON>", "Form", "Table", "compareCalendars", "getComplianceStats", "getVarianceAnalysis", "jsxDEV", "_jsxDEV", "CalendarAnalysis", "selectedDate", "_s", "_comparison$summary", "_comparison$summary2", "_comparison$summary3", "_comparison$summary4", "_comparison$summary5", "comparison", "setComparison", "compliance", "setCompliance", "variance", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "error", "setError", "date<PERSON><PERSON><PERSON>", "setDateRange", "startDate", "endDate", "startOfMonth", "Date", "getFullYear", "getMonth", "endOfMonth", "toISOString", "split", "loadAnalysis", "comparisonData", "complianceData", "varianceData", "Promise", "all", "err", "message", "formatPercentage", "value", "toFixed", "formatHours", "getVarianceBadge", "Math", "abs", "getComplianceBadge", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "Header", "Body", "md", "Group", "Label", "Control", "type", "onChange", "e", "prev", "target", "onClick", "disabled", "summary", "realDaysCount", "plannedDaysCount", "matchingDaysCount", "bg", "averageCompliance", "totalPlannedHours", "totalRealHours", "compliantDays", "nonCompliantDays", "complianceRate", "dailyVariances", "length", "style", "maxHeight", "overflowY", "striped", "bordered", "hover", "size", "map", "day", "index", "date", "toLocaleDateString", "realHours", "plannedHours", "compliant", "details", "detail", "_detail$realDay", "_detail$realDay2", "_detail$plannedDay", "_detail$plannedDay2", "realDay", "hours", "plannedDay", "matches", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/components/CalendarAnalysis.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Badge, Alert, Button, Form, Table } from 'react-bootstrap';\nimport {\n  compareCalendars,\n  getComplianceStats,\n  getVarianceAnalysis\n} from '../services/api';\n\nfunction CalendarAnalysis({ selectedDate }) {\n  const [comparison, setComparison] = useState(null);\n  const [compliance, setCompliance] = useState(null);\n  const [variance, setVariance] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [dateRange, setDateRange] = useState({\n    startDate: '',\n    endDate: ''\n  });\n\n  useEffect(() => {\n    if (selectedDate) {\n      // Establecer rango por defecto al mes actual\n      const startOfMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1);\n      const endOfMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1, 0);\n      \n      setDateRange({\n        startDate: startOfMonth.toISOString().split('T')[0],\n        endDate: endOfMonth.toISOString().split('T')[0]\n      });\n    }\n  }, [selectedDate]);\n\n  useEffect(() => {\n    if (dateRange.startDate && dateRange.endDate) {\n      loadAnalysis();\n    }\n  }, [dateRange]);\n\n  const loadAnalysis = async () => {\n    if (!dateRange.startDate || !dateRange.endDate) return;\n    \n    setLoading(true);\n    setError('');\n    \n    try {\n      const [comparisonData, complianceData, varianceData] = await Promise.all([\n        compareCalendars(dateRange.startDate, dateRange.endDate),\n        getComplianceStats(dateRange.startDate, dateRange.endDate),\n        getVarianceAnalysis(dateRange.startDate, dateRange.endDate)\n      ]);\n      \n      setComparison(comparisonData);\n      setCompliance(complianceData);\n      setVariance(varianceData);\n    } catch (err) {\n      setError('Error cargando análisis: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatPercentage = (value) => {\n    return value ? `${(value * 100).toFixed(1)}%` : 'N/A';\n  };\n\n  const formatHours = (value) => {\n    return value ? `${value.toFixed(1)}h` : '0h';\n  };\n\n  const getVarianceBadge = (variance) => {\n    if (Math.abs(variance) <= 0.5) return 'success';\n    if (Math.abs(variance) <= 2) return 'warning';\n    return 'danger';\n  };\n\n  const getComplianceBadge = (compliance) => {\n    if (compliance >= 0.9) return 'success';\n    if (compliance >= 0.7) return 'warning';\n    return 'danger';\n  };\n\n  return (\n    <div>\n      {error && <Alert variant=\"danger\">{error}</Alert>}\n      \n      {/* Selector de rango de fechas */}\n      <Card className=\"mb-3\">\n        <Card.Header>\n          <h6 className=\"mb-0\">Período de Análisis</h6>\n        </Card.Header>\n        <Card.Body>\n          <Row>\n            <Col md={4}>\n              <Form.Group>\n                <Form.Label>Fecha inicio</Form.Label>\n                <Form.Control\n                  type=\"date\"\n                  value={dateRange.startDate}\n                  onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}\n                />\n              </Form.Group>\n            </Col>\n            <Col md={4}>\n              <Form.Group>\n                <Form.Label>Fecha fin</Form.Label>\n                <Form.Control\n                  type=\"date\"\n                  value={dateRange.endDate}\n                  onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}\n                />\n              </Form.Group>\n            </Col>\n            <Col md={4} className=\"d-flex align-items-end\">\n              <Button \n                variant=\"primary\" \n                onClick={loadAnalysis}\n                disabled={loading || !dateRange.startDate || !dateRange.endDate}\n              >\n                {loading ? 'Analizando...' : 'Actualizar Análisis'}\n              </Button>\n            </Col>\n          </Row>\n        </Card.Body>\n      </Card>\n\n      {/* Resumen general */}\n      {comparison && (\n        <Row className=\"mb-3\">\n          <Col md={3}>\n            <Card className=\"text-center\">\n              <Card.Body>\n                <h5 className=\"text-primary\">{comparison.summary?.realDaysCount || 0}</h5>\n                <small className=\"text-muted\">Días Reales</small>\n              </Card.Body>\n            </Card>\n          </Col>\n          <Col md={3}>\n            <Card className=\"text-center\">\n              <Card.Body>\n                <h5 className=\"text-secondary\">{comparison.summary?.plannedDaysCount || 0}</h5>\n                <small className=\"text-muted\">Días Planificados</small>\n              </Card.Body>\n            </Card>\n          </Col>\n          <Col md={3}>\n            <Card className=\"text-center\">\n              <Card.Body>\n                <h5 className=\"text-success\">{comparison.summary?.matchingDaysCount || 0}</h5>\n                <small className=\"text-muted\">Días Coincidentes</small>\n              </Card.Body>\n            </Card>\n          </Col>\n          <Col md={3}>\n            <Card className=\"text-center\">\n              <Card.Body>\n                <h5>\n                  <Badge bg={getComplianceBadge(comparison.summary?.averageCompliance || 0)}>\n                    {formatPercentage(comparison.summary?.averageCompliance)}\n                  </Badge>\n                </h5>\n                <small className=\"text-muted\">Cumplimiento</small>\n              </Card.Body>\n            </Card>\n          </Col>\n        </Row>\n      )}\n\n      {/* Estadísticas de cumplimiento */}\n      {compliance && (\n        <Card className=\"mb-3\">\n          <Card.Header>\n            <h6 className=\"mb-0\">Estadísticas de Cumplimiento</h6>\n          </Card.Header>\n          <Card.Body>\n            <Row>\n              <Col md={6}>\n                <p><strong>Horas planificadas:</strong> {formatHours(compliance.totalPlannedHours)}</p>\n                <p><strong>Horas reales:</strong> {formatHours(compliance.totalRealHours)}</p>\n                <p><strong>Diferencia:</strong> \n                  <Badge bg={getVarianceBadge(compliance.totalRealHours - compliance.totalPlannedHours)} className=\"ms-2\">\n                    {formatHours(compliance.totalRealHours - compliance.totalPlannedHours)}\n                  </Badge>\n                </p>\n              </Col>\n              <Col md={6}>\n                <p><strong>Días cumplidos:</strong> {compliance.compliantDays || 0}</p>\n                <p><strong>Días no cumplidos:</strong> {compliance.nonCompliantDays || 0}</p>\n                <p><strong>Tasa de cumplimiento:</strong> \n                  <Badge bg={getComplianceBadge(compliance.complianceRate)} className=\"ms-2\">\n                    {formatPercentage(compliance.complianceRate)}\n                  </Badge>\n                </p>\n              </Col>\n            </Row>\n          </Card.Body>\n        </Card>\n      )}\n\n      {/* Análisis de varianzas */}\n      {variance && variance.dailyVariances && variance.dailyVariances.length > 0 && (\n        <Card className=\"mb-3\">\n          <Card.Header>\n            <h6 className=\"mb-0\">Varianzas Diarias</h6>\n          </Card.Header>\n          <Card.Body>\n            <div style={{ maxHeight: '300px', overflowY: 'auto' }}>\n              <Table striped bordered hover size=\"sm\">\n                <thead>\n                  <tr>\n                    <th>Fecha</th>\n                    <th>Real</th>\n                    <th>Planificado</th>\n                    <th>Varianza</th>\n                    <th>Estado</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {variance.dailyVariances.map((day, index) => (\n                    <tr key={index}>\n                      <td>{new Date(day.date).toLocaleDateString('es-ES')}</td>\n                      <td>{formatHours(day.realHours)}</td>\n                      <td>{formatHours(day.plannedHours)}</td>\n                      <td>\n                        <Badge bg={getVarianceBadge(day.variance)}>\n                          {formatHours(day.variance)}\n                        </Badge>\n                      </td>\n                      <td>\n                        <Badge bg={day.compliant ? 'success' : 'warning'}>\n                          {day.compliant ? 'Cumple' : 'No cumple'}\n                        </Badge>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </Table>\n            </div>\n          </Card.Body>\n        </Card>\n      )}\n\n      {/* Detalles de comparación */}\n      {comparison && comparison.details && comparison.details.length > 0 && (\n        <Card>\n          <Card.Header>\n            <h6 className=\"mb-0\">Detalles de Comparación</h6>\n          </Card.Header>\n          <Card.Body>\n            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>\n              <Table striped bordered hover size=\"sm\">\n                <thead>\n                  <tr>\n                    <th>Fecha</th>\n                    <th>Tipo Real</th>\n                    <th>Horas Real</th>\n                    <th>Tipo Planificado</th>\n                    <th>Horas Planificado</th>\n                    <th>Coincide</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {comparison.details.map((detail, index) => (\n                    <tr key={index}>\n                      <td>{new Date(detail.date).toLocaleDateString('es-ES')}</td>\n                      <td>{detail.realDay?.type || '-'}</td>\n                      <td>{formatHours(detail.realDay?.hours || 0)}</td>\n                      <td>{detail.plannedDay?.type || '-'}</td>\n                      <td>{formatHours(detail.plannedDay?.hours || 0)}</td>\n                      <td>\n                        <Badge bg={detail.matches ? 'success' : 'warning'}>\n                          {detail.matches ? 'Sí' : 'No'}\n                        </Badge>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </Table>\n            </div>\n          </Card.Body>\n        </Card>\n      )}\n\n      {!comparison && !loading && dateRange.startDate && dateRange.endDate && (\n        <Alert variant=\"info\">\n          Haz clic en \"Actualizar Análisis\" para ver la comparación entre calendarios.\n        </Alert>\n      )}\n    </div>\n  );\n}\n\nexport default CalendarAnalysis;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,QAAQ,iBAAiB;AACnF,SACEC,gBAAgB,EAChBC,kBAAkB,EAClBC,mBAAmB,QACd,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,SAASC,gBAAgBA,CAAC;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA;EAC1C,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC;IACzCmC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE;EACX,CAAC,CAAC;EAEFnC,SAAS,CAAC,MAAM;IACd,IAAIe,YAAY,EAAE;MAChB;MACA,MAAMqB,YAAY,GAAG,IAAIC,IAAI,CAACtB,YAAY,CAACuB,WAAW,CAAC,CAAC,EAAEvB,YAAY,CAACwB,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACrF,MAAMC,UAAU,GAAG,IAAIH,IAAI,CAACtB,YAAY,CAACuB,WAAW,CAAC,CAAC,EAAEvB,YAAY,CAACwB,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;MAEvFN,YAAY,CAAC;QACXC,SAAS,EAAEE,YAAY,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnDP,OAAO,EAAEK,UAAU,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC3B,YAAY,CAAC,CAAC;EAElBf,SAAS,CAAC,MAAM;IACd,IAAIgC,SAAS,CAACE,SAAS,IAAIF,SAAS,CAACG,OAAO,EAAE;MAC5CQ,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACX,SAAS,CAAC,CAAC;EAEf,MAAMW,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACX,SAAS,CAACE,SAAS,IAAI,CAACF,SAAS,CAACG,OAAO,EAAE;IAEhDN,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAM,CAACa,cAAc,EAAEC,cAAc,EAAEC,YAAY,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACvEvC,gBAAgB,CAACuB,SAAS,CAACE,SAAS,EAAEF,SAAS,CAACG,OAAO,CAAC,EACxDzB,kBAAkB,CAACsB,SAAS,CAACE,SAAS,EAAEF,SAAS,CAACG,OAAO,CAAC,EAC1DxB,mBAAmB,CAACqB,SAAS,CAACE,SAAS,EAAEF,SAAS,CAACG,OAAO,CAAC,CAC5D,CAAC;MAEFZ,aAAa,CAACqB,cAAc,CAAC;MAC7BnB,aAAa,CAACoB,cAAc,CAAC;MAC7BlB,WAAW,CAACmB,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZlB,QAAQ,CAAC,2BAA2B,GAAGkB,GAAG,CAACC,OAAO,CAAC;IACrD,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsB,gBAAgB,GAAIC,KAAK,IAAK;IAClC,OAAOA,KAAK,GAAG,GAAG,CAACA,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK;EACvD,CAAC;EAED,MAAMC,WAAW,GAAIF,KAAK,IAAK;IAC7B,OAAOA,KAAK,GAAG,GAAGA,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI;EAC9C,CAAC;EAED,MAAME,gBAAgB,GAAI7B,QAAQ,IAAK;IACrC,IAAI8B,IAAI,CAACC,GAAG,CAAC/B,QAAQ,CAAC,IAAI,GAAG,EAAE,OAAO,SAAS;IAC/C,IAAI8B,IAAI,CAACC,GAAG,CAAC/B,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,SAAS;IAC7C,OAAO,QAAQ;EACjB,CAAC;EAED,MAAMgC,kBAAkB,GAAIlC,UAAU,IAAK;IACzC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,OAAO,QAAQ;EACjB,CAAC;EAED,oBACEX,OAAA;IAAA8C,QAAA,GACG7B,KAAK,iBAAIjB,OAAA,CAACR,KAAK;MAACuD,OAAO,EAAC,QAAQ;MAAAD,QAAA,EAAE7B;IAAK;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAGjDnD,OAAA,CAACZ,IAAI;MAACgE,SAAS,EAAC,MAAM;MAAAN,QAAA,gBACpB9C,OAAA,CAACZ,IAAI,CAACiE,MAAM;QAAAP,QAAA,eACV9C,OAAA;UAAIoD,SAAS,EAAC,MAAM;UAAAN,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACdnD,OAAA,CAACZ,IAAI,CAACkE,IAAI;QAAAR,QAAA,eACR9C,OAAA,CAACX,GAAG;UAAAyD,QAAA,gBACF9C,OAAA,CAACV,GAAG;YAACiE,EAAE,EAAE,CAAE;YAAAT,QAAA,eACT9C,OAAA,CAACN,IAAI,CAAC8D,KAAK;cAAAV,QAAA,gBACT9C,OAAA,CAACN,IAAI,CAAC+D,KAAK;gBAAAX,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCnD,OAAA,CAACN,IAAI,CAACgE,OAAO;gBACXC,IAAI,EAAC,MAAM;gBACXpB,KAAK,EAAEpB,SAAS,CAACE,SAAU;gBAC3BuC,QAAQ,EAAGC,CAAC,IAAKzC,YAAY,CAAC0C,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEzC,SAAS,EAAEwC,CAAC,CAACE,MAAM,CAACxB;gBAAM,CAAC,CAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNnD,OAAA,CAACV,GAAG;YAACiE,EAAE,EAAE,CAAE;YAAAT,QAAA,eACT9C,OAAA,CAACN,IAAI,CAAC8D,KAAK;cAAAV,QAAA,gBACT9C,OAAA,CAACN,IAAI,CAAC+D,KAAK;gBAAAX,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClCnD,OAAA,CAACN,IAAI,CAACgE,OAAO;gBACXC,IAAI,EAAC,MAAM;gBACXpB,KAAK,EAAEpB,SAAS,CAACG,OAAQ;gBACzBsC,QAAQ,EAAGC,CAAC,IAAKzC,YAAY,CAAC0C,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAExC,OAAO,EAAEuC,CAAC,CAACE,MAAM,CAACxB;gBAAM,CAAC,CAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNnD,OAAA,CAACV,GAAG;YAACiE,EAAE,EAAE,CAAE;YAACH,SAAS,EAAC,wBAAwB;YAAAN,QAAA,eAC5C9C,OAAA,CAACP,MAAM;cACLsD,OAAO,EAAC,SAAS;cACjBiB,OAAO,EAAElC,YAAa;cACtBmC,QAAQ,EAAElD,OAAO,IAAI,CAACI,SAAS,CAACE,SAAS,IAAI,CAACF,SAAS,CAACG,OAAQ;cAAAwB,QAAA,EAE/D/B,OAAO,GAAG,eAAe,GAAG;YAAqB;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAGN1C,UAAU,iBACTT,OAAA,CAACX,GAAG;MAAC+D,SAAS,EAAC,MAAM;MAAAN,QAAA,gBACnB9C,OAAA,CAACV,GAAG;QAACiE,EAAE,EAAE,CAAE;QAAAT,QAAA,eACT9C,OAAA,CAACZ,IAAI;UAACgE,SAAS,EAAC,aAAa;UAAAN,QAAA,eAC3B9C,OAAA,CAACZ,IAAI,CAACkE,IAAI;YAAAR,QAAA,gBACR9C,OAAA;cAAIoD,SAAS,EAAC,cAAc;cAAAN,QAAA,EAAE,EAAA1C,mBAAA,GAAAK,UAAU,CAACyD,OAAO,cAAA9D,mBAAA,uBAAlBA,mBAAA,CAAoB+D,aAAa,KAAI;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1EnD,OAAA;cAAOoD,SAAS,EAAC,YAAY;cAAAN,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnD,OAAA,CAACV,GAAG;QAACiE,EAAE,EAAE,CAAE;QAAAT,QAAA,eACT9C,OAAA,CAACZ,IAAI;UAACgE,SAAS,EAAC,aAAa;UAAAN,QAAA,eAC3B9C,OAAA,CAACZ,IAAI,CAACkE,IAAI;YAAAR,QAAA,gBACR9C,OAAA;cAAIoD,SAAS,EAAC,gBAAgB;cAAAN,QAAA,EAAE,EAAAzC,oBAAA,GAAAI,UAAU,CAACyD,OAAO,cAAA7D,oBAAA,uBAAlBA,oBAAA,CAAoB+D,gBAAgB,KAAI;YAAC;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/EnD,OAAA;cAAOoD,SAAS,EAAC,YAAY;cAAAN,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnD,OAAA,CAACV,GAAG;QAACiE,EAAE,EAAE,CAAE;QAAAT,QAAA,eACT9C,OAAA,CAACZ,IAAI;UAACgE,SAAS,EAAC,aAAa;UAAAN,QAAA,eAC3B9C,OAAA,CAACZ,IAAI,CAACkE,IAAI;YAAAR,QAAA,gBACR9C,OAAA;cAAIoD,SAAS,EAAC,cAAc;cAAAN,QAAA,EAAE,EAAAxC,oBAAA,GAAAG,UAAU,CAACyD,OAAO,cAAA5D,oBAAA,uBAAlBA,oBAAA,CAAoB+D,iBAAiB,KAAI;YAAC;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9EnD,OAAA;cAAOoD,SAAS,EAAC,YAAY;cAAAN,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnD,OAAA,CAACV,GAAG;QAACiE,EAAE,EAAE,CAAE;QAAAT,QAAA,eACT9C,OAAA,CAACZ,IAAI;UAACgE,SAAS,EAAC,aAAa;UAAAN,QAAA,eAC3B9C,OAAA,CAACZ,IAAI,CAACkE,IAAI;YAAAR,QAAA,gBACR9C,OAAA;cAAA8C,QAAA,eACE9C,OAAA,CAACT,KAAK;gBAAC+E,EAAE,EAAEzB,kBAAkB,CAAC,EAAAtC,oBAAA,GAAAE,UAAU,CAACyD,OAAO,cAAA3D,oBAAA,uBAAlBA,oBAAA,CAAoBgE,iBAAiB,KAAI,CAAC,CAAE;gBAAAzB,QAAA,EACvER,gBAAgB,EAAA9B,oBAAA,GAACC,UAAU,CAACyD,OAAO,cAAA1D,oBAAA,uBAAlBA,oBAAA,CAAoB+D,iBAAiB;cAAC;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACLnD,OAAA;cAAOoD,SAAS,EAAC,YAAY;cAAAN,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAxC,UAAU,iBACTX,OAAA,CAACZ,IAAI;MAACgE,SAAS,EAAC,MAAM;MAAAN,QAAA,gBACpB9C,OAAA,CAACZ,IAAI,CAACiE,MAAM;QAAAP,QAAA,eACV9C,OAAA;UAAIoD,SAAS,EAAC,MAAM;UAAAN,QAAA,EAAC;QAA4B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACdnD,OAAA,CAACZ,IAAI,CAACkE,IAAI;QAAAR,QAAA,eACR9C,OAAA,CAACX,GAAG;UAAAyD,QAAA,gBACF9C,OAAA,CAACV,GAAG;YAACiE,EAAE,EAAE,CAAE;YAAAT,QAAA,gBACT9C,OAAA;cAAA8C,QAAA,gBAAG9C,OAAA;gBAAA8C,QAAA,EAAQ;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACV,WAAW,CAAC9B,UAAU,CAAC6D,iBAAiB,CAAC;YAAA;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvFnD,OAAA;cAAA8C,QAAA,gBAAG9C,OAAA;gBAAA8C,QAAA,EAAQ;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACV,WAAW,CAAC9B,UAAU,CAAC8D,cAAc,CAAC;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9EnD,OAAA;cAAA8C,QAAA,gBAAG9C,OAAA;gBAAA8C,QAAA,EAAQ;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7BnD,OAAA,CAACT,KAAK;gBAAC+E,EAAE,EAAE5B,gBAAgB,CAAC/B,UAAU,CAAC8D,cAAc,GAAG9D,UAAU,CAAC6D,iBAAiB,CAAE;gBAACpB,SAAS,EAAC,MAAM;gBAAAN,QAAA,EACpGL,WAAW,CAAC9B,UAAU,CAAC8D,cAAc,GAAG9D,UAAU,CAAC6D,iBAAiB;cAAC;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNnD,OAAA,CAACV,GAAG;YAACiE,EAAE,EAAE,CAAE;YAAAT,QAAA,gBACT9C,OAAA;cAAA8C,QAAA,gBAAG9C,OAAA;gBAAA8C,QAAA,EAAQ;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxC,UAAU,CAAC+D,aAAa,IAAI,CAAC;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvEnD,OAAA;cAAA8C,QAAA,gBAAG9C,OAAA;gBAAA8C,QAAA,EAAQ;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxC,UAAU,CAACgE,gBAAgB,IAAI,CAAC;YAAA;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EnD,OAAA;cAAA8C,QAAA,gBAAG9C,OAAA;gBAAA8C,QAAA,EAAQ;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvCnD,OAAA,CAACT,KAAK;gBAAC+E,EAAE,EAAEzB,kBAAkB,CAAClC,UAAU,CAACiE,cAAc,CAAE;gBAACxB,SAAS,EAAC,MAAM;gBAAAN,QAAA,EACvER,gBAAgB,CAAC3B,UAAU,CAACiE,cAAc;cAAC;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACP,EAGAtC,QAAQ,IAAIA,QAAQ,CAACgE,cAAc,IAAIhE,QAAQ,CAACgE,cAAc,CAACC,MAAM,GAAG,CAAC,iBACxE9E,OAAA,CAACZ,IAAI;MAACgE,SAAS,EAAC,MAAM;MAAAN,QAAA,gBACpB9C,OAAA,CAACZ,IAAI,CAACiE,MAAM;QAAAP,QAAA,eACV9C,OAAA;UAAIoD,SAAS,EAAC,MAAM;UAAAN,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACdnD,OAAA,CAACZ,IAAI,CAACkE,IAAI;QAAAR,QAAA,eACR9C,OAAA;UAAK+E,KAAK,EAAE;YAAEC,SAAS,EAAE,OAAO;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAAnC,QAAA,eACpD9C,OAAA,CAACL,KAAK;YAACuF,OAAO;YAACC,QAAQ;YAACC,KAAK;YAACC,IAAI,EAAC,IAAI;YAAAvC,QAAA,gBACrC9C,OAAA;cAAA8C,QAAA,eACE9C,OAAA;gBAAA8C,QAAA,gBACE9C,OAAA;kBAAA8C,QAAA,EAAI;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACdnD,OAAA;kBAAA8C,QAAA,EAAI;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACbnD,OAAA;kBAAA8C,QAAA,EAAI;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpBnD,OAAA;kBAAA8C,QAAA,EAAI;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjBnD,OAAA;kBAAA8C,QAAA,EAAI;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRnD,OAAA;cAAA8C,QAAA,EACGjC,QAAQ,CAACgE,cAAc,CAACS,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACtCxF,OAAA;gBAAA8C,QAAA,gBACE9C,OAAA;kBAAA8C,QAAA,EAAK,IAAItB,IAAI,CAAC+D,GAAG,CAACE,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO;gBAAC;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzDnD,OAAA;kBAAA8C,QAAA,EAAKL,WAAW,CAAC8C,GAAG,CAACI,SAAS;gBAAC;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrCnD,OAAA;kBAAA8C,QAAA,EAAKL,WAAW,CAAC8C,GAAG,CAACK,YAAY;gBAAC;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxCnD,OAAA;kBAAA8C,QAAA,eACE9C,OAAA,CAACT,KAAK;oBAAC+E,EAAE,EAAE5B,gBAAgB,CAAC6C,GAAG,CAAC1E,QAAQ,CAAE;oBAAAiC,QAAA,EACvCL,WAAW,CAAC8C,GAAG,CAAC1E,QAAQ;kBAAC;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACLnD,OAAA;kBAAA8C,QAAA,eACE9C,OAAA,CAACT,KAAK;oBAAC+E,EAAE,EAAEiB,GAAG,CAACM,SAAS,GAAG,SAAS,GAAG,SAAU;oBAAA/C,QAAA,EAC9CyC,GAAG,CAACM,SAAS,GAAG,QAAQ,GAAG;kBAAW;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GAbEqC,KAAK;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAcV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACP,EAGA1C,UAAU,IAAIA,UAAU,CAACqF,OAAO,IAAIrF,UAAU,CAACqF,OAAO,CAAChB,MAAM,GAAG,CAAC,iBAChE9E,OAAA,CAACZ,IAAI;MAAA0D,QAAA,gBACH9C,OAAA,CAACZ,IAAI,CAACiE,MAAM;QAAAP,QAAA,eACV9C,OAAA;UAAIoD,SAAS,EAAC,MAAM;UAAAN,QAAA,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACdnD,OAAA,CAACZ,IAAI,CAACkE,IAAI;QAAAR,QAAA,eACR9C,OAAA;UAAK+E,KAAK,EAAE;YAAEC,SAAS,EAAE,OAAO;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAAnC,QAAA,eACpD9C,OAAA,CAACL,KAAK;YAACuF,OAAO;YAACC,QAAQ;YAACC,KAAK;YAACC,IAAI,EAAC,IAAI;YAAAvC,QAAA,gBACrC9C,OAAA;cAAA8C,QAAA,eACE9C,OAAA;gBAAA8C,QAAA,gBACE9C,OAAA;kBAAA8C,QAAA,EAAI;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACdnD,OAAA;kBAAA8C,QAAA,EAAI;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClBnD,OAAA;kBAAA8C,QAAA,EAAI;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnBnD,OAAA;kBAAA8C,QAAA,EAAI;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzBnD,OAAA;kBAAA8C,QAAA,EAAI;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1BnD,OAAA;kBAAA8C,QAAA,EAAI;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRnD,OAAA;cAAA8C,QAAA,EACGrC,UAAU,CAACqF,OAAO,CAACR,GAAG,CAAC,CAACS,MAAM,EAAEP,KAAK;gBAAA,IAAAQ,eAAA,EAAAC,gBAAA,EAAAC,kBAAA,EAAAC,mBAAA;gBAAA,oBACpCnG,OAAA;kBAAA8C,QAAA,gBACE9C,OAAA;oBAAA8C,QAAA,EAAK,IAAItB,IAAI,CAACuE,MAAM,CAACN,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO;kBAAC;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5DnD,OAAA;oBAAA8C,QAAA,EAAK,EAAAkD,eAAA,GAAAD,MAAM,CAACK,OAAO,cAAAJ,eAAA,uBAAdA,eAAA,CAAgBrC,IAAI,KAAI;kBAAG;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtCnD,OAAA;oBAAA8C,QAAA,EAAKL,WAAW,CAAC,EAAAwD,gBAAA,GAAAF,MAAM,CAACK,OAAO,cAAAH,gBAAA,uBAAdA,gBAAA,CAAgBI,KAAK,KAAI,CAAC;kBAAC;oBAAArD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAClDnD,OAAA;oBAAA8C,QAAA,EAAK,EAAAoD,kBAAA,GAAAH,MAAM,CAACO,UAAU,cAAAJ,kBAAA,uBAAjBA,kBAAA,CAAmBvC,IAAI,KAAI;kBAAG;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzCnD,OAAA;oBAAA8C,QAAA,EAAKL,WAAW,CAAC,EAAA0D,mBAAA,GAAAJ,MAAM,CAACO,UAAU,cAAAH,mBAAA,uBAAjBA,mBAAA,CAAmBE,KAAK,KAAI,CAAC;kBAAC;oBAAArD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrDnD,OAAA;oBAAA8C,QAAA,eACE9C,OAAA,CAACT,KAAK;sBAAC+E,EAAE,EAAEyB,MAAM,CAACQ,OAAO,GAAG,SAAS,GAAG,SAAU;sBAAAzD,QAAA,EAC/CiD,MAAM,CAACQ,OAAO,GAAG,IAAI,GAAG;oBAAI;sBAAAvD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA,GAVEqC,KAAK;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWV,CAAC;cAAA,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACP,EAEA,CAAC1C,UAAU,IAAI,CAACM,OAAO,IAAII,SAAS,CAACE,SAAS,IAAIF,SAAS,CAACG,OAAO,iBAClEtB,OAAA,CAACR,KAAK;MAACuD,OAAO,EAAC,MAAM;MAAAD,QAAA,EAAC;IAEtB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAChD,EAAA,CAzRQF,gBAAgB;AAAAuG,EAAA,GAAhBvG,gBAAgB;AA2RzB,eAAeA,gBAAgB;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}