.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Calendar pattern selection styles */
.selected-range {
  background-color: #a0d9b5 !important; /* Light green for selected range */
  border-radius: 0;
}

.selected-range-start {
  background-color: #4CAF50 !important; /* Darker green for start date */
  color: white;
  border-radius: 50%;
}

.react-calendar__tile--active {
  background-color: #0069d9 !important; /* Default active tile color */
}