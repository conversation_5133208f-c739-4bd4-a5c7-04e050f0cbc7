const { readData, writeData } = require('../data/dataHandler');
const shiftService = require('./shiftService'); // Import shiftService

const getAllDays = () => {
    const data = readData();
    return data.days;
};

const getDayByDate = (date) => {
    const data = readData();
    return data.days.find(day => day.date === date);
};

const createOrUpdateDay = (date, type, hours = 0, description = '', shiftId = null) => {
    const data = readData();
    let day = data.days.find(d => d.date === date);

    if (day) {
        day.type = type;
        day.hours = hours;
        day.description = description;
        day.shiftId = shiftId; // Store shiftId
    } else {
        day = { date, type, hours, description, shiftId }; // Store shiftId
        data.days.push(day);
    }
    writeData(data);
    return day;
};

module.exports = {
    getAllDays,
    getDayByDate,
    createOrUpdateDay
};
