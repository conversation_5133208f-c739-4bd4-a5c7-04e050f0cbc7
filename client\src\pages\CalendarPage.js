import React, { useState, useEffect } from 'react';
import { Con<PERSON>er, Row, Col, Button, Modal, Form, Alert, Badge, Tabs, Tab } from 'react-bootstrap';
import DualCalendarView from '../components/DualCalendarView';
import RealCalendarManager from '../components/RealCalendarManager';
import CalendarAnalysis from '../components/CalendarAnalysis';
import {
  getDayByDate,
  createOrUpdateDay,
  getAllShifts,
  applyShiftToDate,
  suggestShiftsForDate,
  applyPattern,
  getAllPatterns,
  clearPlannedCalendar
} from '../services/api';

function CalendarPage() {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [activeTab, setActiveTab] = useState('calendar');

  // Estados para modales
  const [showRealDayModal, setShowRealDayModal] = useState(false);
  const [showPatternModal, setShowPatternModal] = useState(false);

  // Estados para aplicación de patrones
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Estado para forzar actualización del calendario
  const [calendarKey, setCalendarKey] = useState(0);

  useEffect(() => {
    loadAllPatterns();
  }, []);

  const loadAllPatterns = async () => {
    try {
      const patterns = await getAllPatterns();
      setAllPatterns(patterns);
    } catch (error) {
      console.error('Error loading patterns:', error);
      setError('Error cargando patrones.');
    }
  };

  const handleDateSelect = (date) => {
    setSelectedDate(date);
  };

  const handleRealDayUpdated = () => {
    // Forzar actualización del calendario
    setCalendarKey(prev => prev + 1);
  };

  const handleClearPlannedCalendar = async () => {
    if (window.confirm('¿Estás seguro de que quieres limpiar todo el calendario planificado?')) {
      try {
        setLoading(true);
        await clearPlannedCalendar();
        setCalendarKey(prev => prev + 1);
        setError('');
      } catch (err) {
        setError('Error limpiando calendario planificado: ' + err.message);
      } finally {
        setLoading(false);
      }
    }
  };

  const handlePatternDateClick = (value) => {
    if (!selectionStartDate) {
      setSelectionStartDate(value);
      setSelectionEndDate(null);
    } else if (!selectionEndDate) {
      if (value < selectionStartDate) {
        setSelectionEndDate(selectionStartDate);
        setSelectionStartDate(value);
      } else {
        setSelectionEndDate(value);
      }
    } else {
      setSelectionStartDate(value);
      setSelectionEndDate(null);
    }
  };

  

  return (
    <Container fluid>
      <Row className="mb-3">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <h2>Sistema de Horarios Dual</h2>
            <div>
              <Button
                variant="outline-primary"
                size="sm"
                onClick={() => setShowRealDayModal(true)}
                disabled={!selectedDate}
                className="me-2"
              >
                Registrar Día Real
              </Button>
              <Button
                variant="outline-danger"
                size="sm"
                onClick={handleClearPlannedCalendar}
                disabled={loading}
              >
                Limpiar Planificado
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      <Row className="mb-3">
        <Col>
          <h3>Aplicar Patrón a Rango Seleccionado</h3>
          {error && (
            <Alert variant="danger" className="mb-3">
              {error}
            </Alert>
          )}
          <Form>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Fecha de inicio</Form.Label>
                  <Form.Control
                    type="date"
                    value={selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : ''}
                    onChange={(e) => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group controlId="patternEndDate">
                  <Form.Label>Fecha de fin</Form.Label>
                  <Form.Control
                    type="date"
                    value={selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : ''}
                    onChange={(e) => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Seleccionar Patrón</Form.Label>
              <Form.Select
                value={selectedPatternId}
                onChange={(e) => setSelectedPatternId(e.target.value)}
              >
                <option value="">Seleccionar patrón...</option>
                {allPatterns.map((pattern) => (
                  <option key={pattern.id} value={pattern.id}>
                    {pattern.name}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>

            <Button
              variant="primary"
              onClick={handleApplyPattern}
              disabled={loading || !selectedPatternId || !selectionStartDate || !selectionEndDate}
            >
              {loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'}
            </Button>
            <Button
              variant="secondary"
              onClick={() => {
                setSelectionStartDate(null);
                setSelectionEndDate(null);
                setSelectedPatternId('');
                setError('');
              }}
              className="ms-2"
            >
              Limpiar Selección
            </Button>
          </Form>
        </Col>
      </Row>

      <Row className="mb-3">
        <Col>
          <h3>Aplicar Patrón a Rango Seleccionado</h3>
          {error && (
            <Alert variant="danger" className="mb-3">
              {error}
            </Alert>
          )}
          <Form>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Fecha de inicio</Form.Label>
                  <Form.Control
                    type="date"
                    value={selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : ''}
                    onChange={(e) => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Fecha de fin</Form.Label>
                  <Form.Control
                    type="date"
                    value={selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : ''}
                    onChange={(e) => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Seleccionar Patrón</Form.Label>
              <Form.Select
                value={selectedPatternId}
                onChange={(e) => setSelectedPatternId(e.target.value)}
              >
                <option value="">Seleccionar patrón...</option>
                {allPatterns.map((pattern) => (
                  <option key={pattern.id} value={pattern.id}>
                    {pattern.name}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>

            <Button
              variant="primary"
              onClick={handleApplyPattern}
              disabled={loading || !selectedPatternId || !selectionStartDate || !selectionEndDate}
            >
              {loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'}
            </Button>
            <Button
              variant="secondary"
              onClick={() => {
                setSelectionStartDate(null);
                setSelectionEndDate(null);
                setSelectedPatternId('');
                setError('');
              }}
              className="ms-2"
            >
              Limpiar Selección
            </Button>
          </Form>
        </Col>
      </Row>

      {error && (
        <Row className="mb-3">
          <Col>
            <Alert variant="danger">{error}</Alert>
          </Col>
        </Row>
      )}

      <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-3">
        <Tab eventKey="calendar" title="📅 Calendario Dual">
          <div className="dual-calendar">
            <DualCalendarView
              key={calendarKey}
              onDateSelect={handleDateSelect}
              selectedDate={selectedDate}
            />
          </div>
        </Tab>

        <Tab eventKey="analysis" title="📊 Análisis">
          <CalendarAnalysis selectedDate={selectedDate} />
        </Tab>
      </Tabs>

      {/* Modal para gestión de días reales */}
      <RealCalendarManager
        show={showRealDayModal}
        onHide={() => setShowRealDayModal(false)}
        selectedDate={selectedDate}
        onDayUpdated={handleRealDayUpdated}
      />

      
    </Container>
  );
}

export default CalendarPage;
