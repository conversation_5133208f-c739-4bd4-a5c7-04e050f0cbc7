const { readData, writeData } = require('../data/dataHandler');

// Definición de turnos predefinidos
const PREDEFINED_SHIFTS = {
    morning: {
        id: 'morning',
        name: '<PERSON><PERSON><PERSON>',
        startTime: '06:00',
        endTime: '14:00',
        breakMinutes: 15,
    },
    afternoon: {
        id: 'afternoon',
        name: '<PERSON><PERSON>',
        startTime: '14:00',
        endTime: '22:00',
        breakMinutes: 15,
    },
    night: {
        id: 'night',
        name: 'Noche',
        startTime: '22:00',
        endTime: '06:00',
        breakMinutes: 15,
    },
    holidayMorning: {
        id: 'holidayMorning',
        name: 'Festivos Mañana',
        startTime: '06:00',
        endTime: '18:00',
        breakMinutes: 60,
    },
    holidayNight: {
        id: 'holidayNight',
        name: 'Festivos Noche',
        startTime: '18:00',
        endTime: '06:00',
        breakMinutes: 60,
    }
};

/**
 * <PERSON>cula las horas totales trabajadas, restando el tiempo de descanso.
 * @param {string} startTime - Hora de inicio en formato HH:MM.
 * @param {string} endTime - Hora de fin en formato HH:MM.
 * @param {number} breakMinutes - Minutos de descanso.
 * @returns {number} Horas totales trabajadas.
 */
const calculateTotalHours = (startTime, endTime, breakMinutes) => {
    const [startHour, startMinute] = startTime.split(':').map(Number);
    const [endHour, endMinute] = endTime.split(':').map(Number);

    let totalMinutes = (endHour * 60 + endMinute) - (startHour * 60 + startMinute);

    // Handle overnight shifts
    if (totalMinutes < 0) {
        totalMinutes += 24 * 60; // Add 24 hours in minutes
    }

    totalMinutes -= breakMinutes;

    return totalMinutes / 60;
};

/**
 * Obtiene todos los turnos predefinidos
 */
const getAllShifts = () => {
    return Object.values(PREDEFINED_SHIFTS).map(shift => ({
        ...shift,
        totalHours: calculateTotalHours(shift.startTime, shift.endTime, shift.breakMinutes),
        description: `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`
    }));
};

/**
 * Obtiene un turno específico por ID
 */
const getShiftById = (shiftId) => {
    const shift = PREDEFINED_SHIFTS[shiftId] || null;
    if (shift) {
        return {
            ...shift,
            totalHours: calculateTotalHours(shift.startTime, shift.endTime, shift.breakMinutes),
            description: `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`
        };
    }
    return null;
};

/**
 * Aplica un turno predefinido a una fecha específica
 */
const applyShiftToDate = (date, shiftId, customDescription = '') => {
    const shift = getShiftById(shiftId);
    if (!shift) {
        throw new Error(`Turno no encontrado: ${shiftId}`);
    }

    const data = readData();
    let day = data.days.find(d => d.date === date);

    const finalDescription = customDescription || 
        `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;

    if (day) {
        // Actualizar día existente
        day.type = 'worked';
        day.hours = shift.totalHours;
        day.description = finalDescription;
        day.shift = {
            id: shift.id,
            name: shift.name,
            startTime: shift.startTime,
            endTime: shift.endTime,
            breakMinutes: shift.breakMinutes
        };
    } else {
        // Crear nuevo día
        day = {
            date,
            type: 'worked',
            hours: shift.totalHours,
            description: finalDescription,
            shift: {
                id: shift.id,
                name: shift.name,
                startTime: shift.startTime,
                endTime: shift.endTime,
                breakMinutes: shift.breakMinutes
            }
        };
        data.days.push(day);
    }

    writeData(data);
    return day;
};

/**
 * Aplica un turno a múltiples fechas
 */
const applyShiftToMultipleDates = (dates, shiftId, customDescription = '') => {
    const shift = getShiftById(shiftId);
    if (!shift) {
        throw new Error(`Turno no encontrado: ${shiftId}`);
    }

    const results = [];
    for (const date of dates) {
        try {
            const result = applyShiftToDate(date, shiftId, customDescription);
            results.push(result);
        } catch (error) {
            console.error(`Error aplicando turno a fecha ${date}:`, error);
        }
    }

    return results;
};

/**
 * Obtiene estadísticas de turnos aplicados
 */
const getShiftStatistics = () => {
    const data = readData();
    const stats = {};

    // Inicializar estadísticas
    Object.keys(PREDEFINED_SHIFTS).forEach(shiftId => {
        stats[shiftId] = {
            name: PREDEFINED_SHIFTS[shiftId].name,
            count: 0,
            totalHours: 0
        };
    });

    // Contar días con turnos
    data.days.forEach(day => {
        if (day.shift && day.shift.id && stats[day.shift.id]) {
            stats[day.shift.id].count++;
            stats[day.shift.id].totalHours += day.hours || 0;
        }
    });

    return stats;
};

/**
 * Determina si una fecha es fin de semana
 */
const isWeekend = (dateString) => {
    const date = new Date(dateString);
    const dayOfWeek = date.getDay();
    return dayOfWeek === 0 || dayOfWeek === 6; // 0 = Domingo, 6 = Sábado
};

/**
 * Sugiere el turno más apropiado basado en la fecha
 */
const suggestShiftForDate = (dateString) => {
    if (isWeekend(dateString)) {
        // Para fines de semana, sugerir turnos festivos
        return [PREDEFINED_SHIFTS.holidayMorning, PREDEFINED_SHIFTS.holidayNight];
    } else {
        // Para días laborables, sugerir turnos normales
        return [PREDEFINED_SHIFTS.morning, PREDEFINED_SHIFTS.afternoon, PREDEFINED_SHIFTS.night];
    }
};

module.exports = {
    getAllShifts,
    getShiftById,
    applyShiftToDate,
    applyShiftToMultipleDates,
    getShiftStatistics,
    isWeekend,
    suggestShiftForDate
};
