{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\CalendarPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Row, Col, Tabs, Tab, Alert } from 'react-bootstrap';\nimport RealCalendarView from '../components/RealCalendarView';\nimport PlannedCalendarView from '../components/PlannedCalendarView';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CalendarPage() {\n  _s();\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('real');\n  const [calendarKey, setCalendarKey] = useState(0);\n  const handleDateSelect = date => {\n    setSelectedDate(date);\n  };\n  const handleCalendarUpdate = () => {\n    // Forzar actualización de todos los calendarios\n    setCalendarKey(prev => prev + 1);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Sistema de Horarios\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n      activeKey: activeTab,\n      onSelect: setActiveTab,\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"real\",\n        title: \"\\uD83D\\uDCC5 Calendario Real\",\n        children: /*#__PURE__*/_jsxDEV(RealCalendarView, {\n          onDateSelect: handleDateSelect,\n          selectedDate: selectedDate,\n          onCalendarUpdate: handleCalendarUpdate\n        }, `real-${calendarKey}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"planned\",\n        title: \"\\uD83D\\uDCCB Calendario Te\\xF3rico\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Calendario Te\\xF3rico (Planificado)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"success\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Funcionalidades del Calendario Te\\xF3rico:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2705 Aplicar patrones de horarios\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2705 Vista previa de patrones\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2705 Eliminar d\\xEDas planificados\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2705 Limpiar calendario completo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2705 Estad\\xEDsticas de planificaci\\xF3n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2705 Completamente separado del calendario real\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Estado:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 18\n              }, this), \" Implementaci\\xF3n en progreso\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"analysis\",\n        title: \"\\uD83D\\uDCCA An\\xE1lisis\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"An\\xE1lisis y Comparaci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"warning\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Funcionalidades de An\\xE1lisis:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2705 Comparar calendario real vs te\\xF3rico\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2705 Estad\\xEDsticas de cumplimiento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2705 An\\xE1lisis de variaciones\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2705 Reportes detallados\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Estado:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 18\n              }, this), \" Disponible (componente existente)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n}\n_s(CalendarPage, \"wzK0Ju2Td/fRUgmzeDqwaKhFGtc=\");\n_c = CalendarPage;\nexport default CalendarPage;\nvar _c;\n$RefreshReg$(_c, \"CalendarPage\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Row", "Col", "Tabs", "Tab", "<PERSON><PERSON>", "RealCalendarView", "PlannedCalendarView", "jsxDEV", "_jsxDEV", "CalendarPage", "_s", "selectedDate", "setSelectedDate", "Date", "activeTab", "setActiveTab", "calendarKey", "setCalendarKey", "handleDateSelect", "date", "handleCalendarUpdate", "prev", "fluid", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "active<PERSON><PERSON>", "onSelect", "eventKey", "title", "onDateSelect", "onCalendarUpdate", "variant", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/CalendarPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Container, Row, Col, Tabs, Tab, Alert } from 'react-bootstrap';\nimport RealCalendarView from '../components/RealCalendarView';\nimport PlannedCalendarView from '../components/PlannedCalendarView';\n\nfunction CalendarPage() {\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('real');\n  const [calendarKey, setCalendarKey] = useState(0);\n\n  const handleDateSelect = (date) => {\n    setSelectedDate(date);\n  };\n\n  const handleCalendarUpdate = () => {\n    // Forzar actualización de todos los calendarios\n    setCalendarKey(prev => prev + 1);\n  };\n\n  return (\n    <Container fluid>\n      <Row className=\"mb-3\">\n        <Col>\n          <h2>Sistema de Horarios</h2>\n        </Col>\n      </Row>\n\n      <Tabs activeKey={activeTab} onSelect={setActiveTab} className=\"mb-3\">\n        <Tab eventKey=\"real\" title=\"📅 Calendario Real\">\n          <RealCalendarView\n            key={`real-${calendarKey}`}\n            onDateSelect={handleDateSelect}\n            selectedDate={selectedDate}\n            onCalendarUpdate={handleCalendarUpdate}\n          />\n        </Tab>\n        <Tab eventKey=\"planned\" title=\"📋 Calendario Teórico\">\n          <div className=\"p-4\">\n            <h4>Calendario Teórico (Planificado)</h4>\n            <Alert variant=\"success\">\n              <h5>Funcionalidades del Calendario Teórico:</h5>\n              <ul>\n                <li>✅ Aplicar patrones de horarios</li>\n                <li>✅ Vista previa de patrones</li>\n                <li>✅ Eliminar días planificados</li>\n                <li>✅ Limpiar calendario completo</li>\n                <li>✅ Estadísticas de planificación</li>\n                <li>✅ Completamente separado del calendario real</li>\n              </ul>\n              <p><strong>Estado:</strong> Implementación en progreso</p>\n            </Alert>\n          </div>\n        </Tab>\n        <Tab eventKey=\"analysis\" title=\"📊 Análisis\">\n          <div className=\"p-4\">\n            <h4>Análisis y Comparación</h4>\n            <Alert variant=\"warning\">\n              <h5>Funcionalidades de Análisis:</h5>\n              <ul>\n                <li>✅ Comparar calendario real vs teórico</li>\n                <li>✅ Estadísticas de cumplimiento</li>\n                <li>✅ Análisis de variaciones</li>\n                <li>✅ Reportes detallados</li>\n              </ul>\n              <p><strong>Estado:</strong> Disponible (componente existente)</p>\n            </Alert>\n          </div>\n        </Tab>\n      </Tabs>\n    </Container>\n  );\n}\n\nexport default CalendarPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,QAAQ,iBAAiB;AACvE,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,mBAAmB,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,IAAIe,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EAEjD,MAAMoB,gBAAgB,GAAIC,IAAI,IAAK;IACjCP,eAAe,CAACO,IAAI,CAAC;EACvB,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAH,cAAc,CAACI,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;EAClC,CAAC;EAED,oBACEb,OAAA,CAACT,SAAS;IAACuB,KAAK;IAAAC,QAAA,gBACdf,OAAA,CAACR,GAAG;MAACwB,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBf,OAAA,CAACP,GAAG;QAAAsB,QAAA,eACFf,OAAA;UAAAe,QAAA,EAAI;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpB,OAAA,CAACN,IAAI;MAAC2B,SAAS,EAAEf,SAAU;MAACgB,QAAQ,EAAEf,YAAa;MAACS,SAAS,EAAC,MAAM;MAAAD,QAAA,gBAClEf,OAAA,CAACL,GAAG;QAAC4B,QAAQ,EAAC,MAAM;QAACC,KAAK,EAAC,8BAAoB;QAAAT,QAAA,eAC7Cf,OAAA,CAACH,gBAAgB;UAEf4B,YAAY,EAAEf,gBAAiB;UAC/BP,YAAY,EAAEA,YAAa;UAC3BuB,gBAAgB,EAAEd;QAAqB,GAHlC,QAAQJ,WAAW,EAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAI3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNpB,OAAA,CAACL,GAAG;QAAC4B,QAAQ,EAAC,SAAS;QAACC,KAAK,EAAC,oCAAuB;QAAAT,QAAA,eACnDf,OAAA;UAAKgB,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAClBf,OAAA;YAAAe,QAAA,EAAI;UAAgC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzCpB,OAAA,CAACJ,KAAK;YAAC+B,OAAO,EAAC,SAAS;YAAAZ,QAAA,gBACtBf,OAAA;cAAAe,QAAA,EAAI;YAAuC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDpB,OAAA;cAAAe,QAAA,gBACEf,OAAA;gBAAAe,QAAA,EAAI;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvCpB,OAAA;gBAAAe,QAAA,EAAI;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnCpB,OAAA;gBAAAe,QAAA,EAAI;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrCpB,OAAA;gBAAAe,QAAA,EAAI;cAA6B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtCpB,OAAA;gBAAAe,QAAA,EAAI;cAA+B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxCpB,OAAA;gBAAAe,QAAA,EAAI;cAA4C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACLpB,OAAA;cAAAe,QAAA,gBAAGf,OAAA;gBAAAe,QAAA,EAAQ;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,kCAA2B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNpB,OAAA,CAACL,GAAG;QAAC4B,QAAQ,EAAC,UAAU;QAACC,KAAK,EAAC,0BAAa;QAAAT,QAAA,eAC1Cf,OAAA;UAAKgB,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAClBf,OAAA;YAAAe,QAAA,EAAI;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/BpB,OAAA,CAACJ,KAAK;YAAC+B,OAAO,EAAC,SAAS;YAAAZ,QAAA,gBACtBf,OAAA;cAAAe,QAAA,EAAI;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrCpB,OAAA;cAAAe,QAAA,gBACEf,OAAA;gBAAAe,QAAA,EAAI;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9CpB,OAAA;gBAAAe,QAAA,EAAI;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvCpB,OAAA;gBAAAe,QAAA,EAAI;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClCpB,OAAA;gBAAAe,QAAA,EAAI;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACLpB,OAAA;cAAAe,QAAA,gBAAGf,OAAA;gBAAAe,QAAA,EAAQ;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,sCAAkC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB;AAAClB,EAAA,CAlEQD,YAAY;AAAA2B,EAAA,GAAZ3B,YAAY;AAoErB,eAAeA,YAAY;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}