import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Badge, Alert, Button, Form, Table } from 'react-bootstrap';
import {
  compareCalendars,
  getComplianceStats,
  getVarianceAnalysis
} from '../services/api';

function CalendarAnalysis({ selectedDate }) {
  const [comparison, setComparison] = useState(null);
  const [compliance, setCompliance] = useState(null);
  const [variance, setVariance] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: ''
  });

  useEffect(() => {
    if (selectedDate) {
      // Establecer rango por defecto al mes actual
      const startOfMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1);
      const endOfMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1, 0);
      
      setDateRange({
        startDate: startOfMonth.toISOString().split('T')[0],
        endDate: endOfMonth.toISOString().split('T')[0]
      });
    }
  }, [selectedDate]);

  useEffect(() => {
    if (dateRange.startDate && dateRange.endDate) {
      loadAnalysis();
    }
  }, [dateRange]);

  const loadAnalysis = async () => {
    if (!dateRange.startDate || !dateRange.endDate) return;
    
    setLoading(true);
    setError('');
    
    try {
      const [comparisonData, complianceData, varianceData] = await Promise.all([
        compareCalendars(dateRange.startDate, dateRange.endDate),
        getComplianceStats(dateRange.startDate, dateRange.endDate),
        getVarianceAnalysis(dateRange.startDate, dateRange.endDate)
      ]);
      
      setComparison(comparisonData);
      setCompliance(complianceData);
      setVariance(varianceData);
    } catch (err) {
      setError('Error cargando análisis: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const formatPercentage = (value) => {
    return value ? `${(value * 100).toFixed(1)}%` : 'N/A';
  };

  const formatHours = (value) => {
    return value ? `${value.toFixed(1)}h` : '0h';
  };

  const getVarianceBadge = (variance) => {
    if (Math.abs(variance) <= 0.5) return 'success';
    if (Math.abs(variance) <= 2) return 'warning';
    return 'danger';
  };

  const getComplianceBadge = (compliance) => {
    if (compliance >= 0.9) return 'success';
    if (compliance >= 0.7) return 'warning';
    return 'danger';
  };

  return (
    <div>
      {error && <Alert variant="danger">{error}</Alert>}
      
      {/* Selector de rango de fechas */}
      <Card className="mb-3">
        <Card.Header>
          <h6 className="mb-0">Período de Análisis</h6>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Fecha inicio</Form.Label>
                <Form.Control
                  type="date"
                  value={dateRange.startDate}
                  onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Fecha fin</Form.Label>
                <Form.Control
                  type="date"
                  value={dateRange.endDate}
                  onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                />
              </Form.Group>
            </Col>
            <Col md={4} className="d-flex align-items-end">
              <Button 
                variant="primary" 
                onClick={loadAnalysis}
                disabled={loading || !dateRange.startDate || !dateRange.endDate}
              >
                {loading ? 'Analizando...' : 'Actualizar Análisis'}
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Resumen general */}
      {comparison && (
        <Row className="mb-3">
          <Col md={3}>
            <Card className="text-center">
              <Card.Body>
                <h5 className="text-primary">{comparison.summary?.realDaysCount || 0}</h5>
                <small className="text-muted">Días Reales</small>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className="text-center">
              <Card.Body>
                <h5 className="text-secondary">{comparison.summary?.plannedDaysCount || 0}</h5>
                <small className="text-muted">Días Planificados</small>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className="text-center">
              <Card.Body>
                <h5 className="text-success">{comparison.summary?.matchingDaysCount || 0}</h5>
                <small className="text-muted">Días Coincidentes</small>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className="text-center">
              <Card.Body>
                <h5>
                  <Badge bg={getComplianceBadge(comparison.summary?.averageCompliance || 0)}>
                    {formatPercentage(comparison.summary?.averageCompliance)}
                  </Badge>
                </h5>
                <small className="text-muted">Cumplimiento</small>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {/* Estadísticas de cumplimiento */}
      {compliance && (
        <Card className="mb-3">
          <Card.Header>
            <h6 className="mb-0">Estadísticas de Cumplimiento</h6>
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={6}>
                <p><strong>Horas planificadas:</strong> {formatHours(compliance.totalPlannedHours)}</p>
                <p><strong>Horas reales:</strong> {formatHours(compliance.totalRealHours)}</p>
                <p><strong>Diferencia:</strong> 
                  <Badge bg={getVarianceBadge(compliance.totalRealHours - compliance.totalPlannedHours)} className="ms-2">
                    {formatHours(compliance.totalRealHours - compliance.totalPlannedHours)}
                  </Badge>
                </p>
              </Col>
              <Col md={6}>
                <p><strong>Días cumplidos:</strong> {compliance.compliantDays || 0}</p>
                <p><strong>Días no cumplidos:</strong> {compliance.nonCompliantDays || 0}</p>
                <p><strong>Tasa de cumplimiento:</strong> 
                  <Badge bg={getComplianceBadge(compliance.complianceRate)} className="ms-2">
                    {formatPercentage(compliance.complianceRate)}
                  </Badge>
                </p>
              </Col>
            </Row>
          </Card.Body>
        </Card>
      )}

      {/* Análisis de varianzas */}
      {variance && variance.dailyVariances && variance.dailyVariances.length > 0 && (
        <Card className="mb-3">
          <Card.Header>
            <h6 className="mb-0">Varianzas Diarias</h6>
          </Card.Header>
          <Card.Body>
            <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
              <Table striped bordered hover size="sm">
                <thead>
                  <tr>
                    <th>Fecha</th>
                    <th>Real</th>
                    <th>Planificado</th>
                    <th>Varianza</th>
                    <th>Estado</th>
                  </tr>
                </thead>
                <tbody>
                  {variance.dailyVariances.map((day, index) => (
                    <tr key={index}>
                      <td>{new Date(day.date).toLocaleDateString('es-ES')}</td>
                      <td>{formatHours(day.realHours)}</td>
                      <td>{formatHours(day.plannedHours)}</td>
                      <td>
                        <Badge bg={getVarianceBadge(day.variance)}>
                          {formatHours(day.variance)}
                        </Badge>
                      </td>
                      <td>
                        <Badge bg={day.compliant ? 'success' : 'warning'}>
                          {day.compliant ? 'Cumple' : 'No cumple'}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          </Card.Body>
        </Card>
      )}

      {/* Detalles de comparación */}
      {comparison && comparison.details && comparison.details.length > 0 && (
        <Card>
          <Card.Header>
            <h6 className="mb-0">Detalles de Comparación</h6>
          </Card.Header>
          <Card.Body>
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              <Table striped bordered hover size="sm">
                <thead>
                  <tr>
                    <th>Fecha</th>
                    <th>Tipo Real</th>
                    <th>Horas Real</th>
                    <th>Tipo Planificado</th>
                    <th>Horas Planificado</th>
                    <th>Coincide</th>
                  </tr>
                </thead>
                <tbody>
                  {comparison.details.map((detail, index) => (
                    <tr key={index}>
                      <td>{new Date(detail.date).toLocaleDateString('es-ES')}</td>
                      <td>{detail.realDay?.type || '-'}</td>
                      <td>{formatHours(detail.realDay?.hours || 0)}</td>
                      <td>{detail.plannedDay?.type || '-'}</td>
                      <td>{formatHours(detail.plannedDay?.hours || 0)}</td>
                      <td>
                        <Badge bg={detail.matches ? 'success' : 'warning'}>
                          {detail.matches ? 'Sí' : 'No'}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          </Card.Body>
        </Card>
      )}

      {!comparison && !loading && dateRange.startDate && dateRange.endDate && (
        <Alert variant="info">
          Haz clic en "Actualizar Análisis" para ver la comparación entre calendarios.
        </Alert>
      )}
    </div>
  );
}

export default CalendarAnalysis;
