const express = require('express');
const cors = require('cors');
const dayRoutes = require('./routes/dayRoutes');
const shiftRoutes = require('./routes/shiftRoutes');
const patternRoutes = require('./routes/patternRoutes');

const app = express();

app.use(cors());
app.use(express.json());

app.use('/api/days', dayRoutes);
app.use('/api/shifts', shiftRoutes);
app.use('/api/patterns', patternRoutes);

module.exports = app;
