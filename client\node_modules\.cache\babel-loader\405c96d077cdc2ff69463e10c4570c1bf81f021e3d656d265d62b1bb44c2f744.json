{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\components\\\\DualCalendarView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, <PERSON>, Col, Card, Button, Badge, Alert, Tabs, Tab } from 'react-bootstrap';\nimport Calendar from 'react-calendar';\nimport 'react-calendar/dist/Calendar.css';\nimport { getAllRealDays, getAllPlannedDays, getRealDayByDate, getPlannedDayByDate, compareCalendars } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction DualCalendarView({\n  onDateSelect,\n  selectedDate\n}) {\n  _s();\n  var _comparison$summary, _comparison$summary2, _comparison$summary3;\n  const [realDays, setRealDays] = useState([]);\n  const [plannedDays, setPlannedDays] = useState([]);\n  const [comparison, setComparison] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [activeTab, setActiveTab] = useState('real');\n  useEffect(() => {\n    loadCalendarData();\n  }, []);\n  useEffect(() => {\n    if (selectedDate) {\n      loadComparison();\n    }\n  }, [selectedDate, realDays, plannedDays]);\n  const loadCalendarData = async () => {\n    setLoading(true);\n    try {\n      const [realData, plannedData] = await Promise.all([getAllRealDays(), getAllPlannedDays()]);\n      setRealDays(realData);\n      setPlannedDays(plannedData);\n      setError('');\n    } catch (err) {\n      setError('Error cargando datos del calendario: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadComparison = async () => {\n    if (!selectedDate) return;\n    try {\n      const startOfMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1);\n      const endOfMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1, 0);\n      const comparisonData = await compareCalendars(startOfMonth.toISOString().split('T')[0], endOfMonth.toISOString().split('T')[0]);\n      setComparison(comparisonData);\n    } catch (err) {\n      console.error('Error loading comparison:', err);\n    }\n  };\n  const getDayData = (date, calendar) => {\n    const dateStr = date.toISOString().split('T')[0];\n    const days = calendar === 'real' ? realDays : plannedDays;\n    return days.find(day => day.date === dateStr);\n  };\n  const getTileContent = ({\n    date,\n    view\n  }) => {\n    if (view !== 'month') return null;\n    const realDay = getDayData(date, 'real');\n    const plannedDay = getDayData(date, 'planned');\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"calendar-tile-content\",\n      children: [realDay && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"real-indicator\",\n        children: /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"primary\",\n          size: \"sm\",\n          children: [realDay.hours, \"h\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 11\n      }, this), plannedDay && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"planned-indicator\",\n        children: /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"secondary\",\n          size: \"sm\",\n          children: [plannedDay.hours, \"h\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this);\n  };\n  const getTileClassName = ({\n    date,\n    view\n  }) => {\n    if (view !== 'month') return null;\n    const realDay = getDayData(date, 'real');\n    const plannedDay = getDayData(date, 'planned');\n    const classes = [];\n    if (realDay && realDay.type === 'worked') {\n      classes.push('has-real-work');\n    }\n    if (plannedDay && plannedDay.type === 'worked') {\n      classes.push('has-planned-work');\n    }\n    if (realDay && plannedDay) {\n      const variance = Math.abs(realDay.hours - plannedDay.hours);\n      if (variance > 1) {\n        classes.push('has-variance');\n      }\n    }\n    return classes.join(' ');\n  };\n  const formatDate = date => {\n    return date.toLocaleDateString('es-ES', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const getSelectedDayInfo = () => {\n    if (!selectedDate) return null;\n    const realDay = getDayData(selectedDate, 'real');\n    const plannedDay = getDayData(selectedDate, 'planned');\n    return {\n      realDay,\n      plannedDay\n    };\n  };\n  const selectedDayInfo = getSelectedDayInfo();\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"danger\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: \"Calendario Dual\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-primary\",\n                  size: \"sm\",\n                  onClick: loadCalendarData,\n                  disabled: loading,\n                  children: loading ? 'Cargando...' : 'Actualizar'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"calendar-legend mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: [/*#__PURE__*/_jsxDEV(Badge, {\n                  bg: \"primary\",\n                  className: \"me-2\",\n                  children: \"Real\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: \"secondary\",\n                  className: \"me-2\",\n                  children: \"Planificado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"calendar-legend-item has-variance me-2\",\n                  children: \"Varianza\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Calendar, {\n              onChange: onDateSelect,\n              value: selectedDate,\n              tileContent: getTileContent,\n              tileClassName: getTileClassName,\n              locale: \"es-ES\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 4,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mb-0\",\n              children: selectedDate ? formatDate(selectedDate) : 'Selecciona una fecha'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [selectedDate && selectedDayInfo && /*#__PURE__*/_jsxDEV(Tabs, {\n              activeKey: activeTab,\n              onSelect: setActiveTab,\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Tab, {\n                eventKey: \"real\",\n                title: \"Real\",\n                children: selectedDayInfo.realDay ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tipo:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 191,\n                      columnNumber: 28\n                    }, this), \" \", selectedDayInfo.realDay.type || 'No definido']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Horas:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 192,\n                      columnNumber: 28\n                    }, this), \" \", selectedDayInfo.realDay.hours || 0]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Descripci\\xF3n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 193,\n                      columnNumber: 28\n                    }, this), \" \", selectedDayInfo.realDay.description || 'Sin descripción']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 25\n                  }, this), selectedDayInfo.realDay.shift && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Turno:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 30\n                    }, this), \" \", selectedDayInfo.realDay.shift.name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted\",\n                  children: \"No hay datos reales para esta fecha\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                eventKey: \"planned\",\n                title: \"Planificado\",\n                children: selectedDayInfo.plannedDay ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tipo:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 28\n                    }, this), \" \", selectedDayInfo.plannedDay.type || 'No definido']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Horas:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 28\n                    }, this), \" \", selectedDayInfo.plannedDay.hours || 0]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Descripci\\xF3n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 28\n                    }, this), \" \", selectedDayInfo.plannedDay.description || 'Sin descripción']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 25\n                  }, this), selectedDayInfo.plannedDay.shift && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Turno:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 210,\n                      columnNumber: 30\n                    }, this), \" \", selectedDayInfo.plannedDay.shift.name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted\",\n                  children: \"No hay datos planificados para esta fecha\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                eventKey: \"comparison\",\n                title: \"Comparaci\\xF3n\",\n                children: selectedDayInfo.realDay && selectedDayInfo.plannedDay ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Varianza de horas:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 28\n                    }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: Math.abs(selectedDayInfo.realDay.hours - selectedDayInfo.plannedDay.hours) > 1 ? 'warning' : 'success',\n                      children: [(selectedDayInfo.realDay.hours - selectedDayInfo.plannedDay.hours).toFixed(2), \"h\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Cumplimiento:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 226,\n                      columnNumber: 28\n                    }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: selectedDayInfo.realDay.type === selectedDayInfo.plannedDay.type ? 'success' : 'warning',\n                      children: selectedDayInfo.realDay.type === selectedDayInfo.plannedDay.type ? 'Cumple' : 'No cumple'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted\",\n                  children: \"Necesitas datos en ambos calendarios para comparar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), !selectedDate && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"Haz clic en una fecha del calendario para ver los detalles\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), comparison && /*#__PURE__*/_jsxDEV(Card, {\n          className: \"mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mb-0\",\n              children: \"Resumen del Mes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"D\\xEDas con datos reales:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 20\n              }, this), \" \", ((_comparison$summary = comparison.summary) === null || _comparison$summary === void 0 ? void 0 : _comparison$summary.realDaysCount) || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"D\\xEDas planificados:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 20\n              }, this), \" \", ((_comparison$summary2 = comparison.summary) === null || _comparison$summary2 === void 0 ? void 0 : _comparison$summary2.plannedDaysCount) || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Cumplimiento promedio:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 20\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                bg: \"info\",\n                className: \"ms-2\",\n                children: (_comparison$summary3 = comparison.summary) !== null && _comparison$summary3 !== void 0 && _comparison$summary3.averageCompliance ? `${(comparison.summary.averageCompliance * 100).toFixed(1)}%` : 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 137,\n    columnNumber: 5\n  }, this);\n}\n_s(DualCalendarView, \"lbY5MMiMEXeyv7NF05ntd/T1klU=\");\n_c = DualCalendarView;\nexport default DualCalendarView;\nvar _c;\n$RefreshReg$(_c, \"DualCalendarView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Badge", "<PERSON><PERSON>", "Tabs", "Tab", "Calendar", "getAllRealDays", "getAllPlannedDays", "getRealDayByDate", "getPlannedDayByDate", "compareCalendars", "jsxDEV", "_jsxDEV", "DualCalendarView", "onDateSelect", "selectedDate", "_s", "_comparison$summary", "_comparison$summary2", "_comparison$summary3", "realDays", "setRealDays", "plannedDays", "setPlannedDays", "comparison", "setComparison", "loading", "setLoading", "error", "setError", "activeTab", "setActiveTab", "loadCalendarData", "loadComparison", "realData", "plannedData", "Promise", "all", "err", "message", "startOfMonth", "Date", "getFullYear", "getMonth", "endOfMonth", "comparisonData", "toISOString", "split", "console", "getDayData", "date", "calendar", "dateStr", "days", "find", "day", "getTileContent", "view", "realDay", "plannedDay", "className", "children", "bg", "size", "hours", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getTileClassName", "classes", "type", "push", "variance", "Math", "abs", "join", "formatDate", "toLocaleDateString", "weekday", "year", "month", "getSelectedDayInfo", "selectedDayInfo", "fluid", "variant", "lg", "Header", "onClick", "disabled", "Body", "onChange", "value", "tileContent", "tileClassName", "locale", "active<PERSON><PERSON>", "onSelect", "eventKey", "title", "description", "shift", "name", "toFixed", "summary", "realDaysCount", "plannedDaysCount", "averageCompliance", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/components/DualCalendarView.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, <PERSON>, Col, Card, Button, Badge, Alert, Tabs, Tab } from 'react-bootstrap';\nimport Calendar from 'react-calendar';\nimport 'react-calendar/dist/Calendar.css';\nimport {\n  getAllRealDays,\n  getAllPlannedDays,\n  getRealDayByDate,\n  getPlannedDayByDate,\n  compareCalendars\n} from '../services/api';\n\nfunction DualCalendarView({ onDateSelect, selectedDate }) {\n  const [realDays, setRealDays] = useState([]);\n  const [plannedDays, setPlannedDays] = useState([]);\n  const [comparison, setComparison] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [activeTab, setActiveTab] = useState('real');\n\n  useEffect(() => {\n    loadCalendarData();\n  }, []);\n\n  useEffect(() => {\n    if (selectedDate) {\n      loadComparison();\n    }\n  }, [selectedDate, realDays, plannedDays]);\n\n  const loadCalendarData = async () => {\n    setLoading(true);\n    try {\n      const [realData, plannedData] = await Promise.all([\n        getAllRealDays(),\n        getAllPlannedDays()\n      ]);\n      setRealDays(realData);\n      setPlannedDays(plannedData);\n      setError('');\n    } catch (err) {\n      setError('Error cargando datos del calendario: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadComparison = async () => {\n    if (!selectedDate) return;\n    \n    try {\n      const startOfMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1);\n      const endOfMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1, 0);\n      \n      const comparisonData = await compareCalendars(\n        startOfMonth.toISOString().split('T')[0],\n        endOfMonth.toISOString().split('T')[0]\n      );\n      setComparison(comparisonData);\n    } catch (err) {\n      console.error('Error loading comparison:', err);\n    }\n  };\n\n  const getDayData = (date, calendar) => {\n    const dateStr = date.toISOString().split('T')[0];\n    const days = calendar === 'real' ? realDays : plannedDays;\n    return days.find(day => day.date === dateStr);\n  };\n\n  const getTileContent = ({ date, view }) => {\n    if (view !== 'month') return null;\n\n    const realDay = getDayData(date, 'real');\n    const plannedDay = getDayData(date, 'planned');\n\n    return (\n      <div className=\"calendar-tile-content\">\n        {realDay && (\n          <div className=\"real-indicator\">\n            <Badge bg=\"primary\" size=\"sm\">{realDay.hours}h</Badge>\n          </div>\n        )}\n        {plannedDay && (\n          <div className=\"planned-indicator\">\n            <Badge bg=\"secondary\" size=\"sm\">{plannedDay.hours}h</Badge>\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  const getTileClassName = ({ date, view }) => {\n    if (view !== 'month') return null;\n\n    const realDay = getDayData(date, 'real');\n    const plannedDay = getDayData(date, 'planned');\n    const classes = [];\n\n    if (realDay && realDay.type === 'worked') {\n      classes.push('has-real-work');\n    }\n    if (plannedDay && plannedDay.type === 'worked') {\n      classes.push('has-planned-work');\n    }\n    if (realDay && plannedDay) {\n      const variance = Math.abs(realDay.hours - plannedDay.hours);\n      if (variance > 1) {\n        classes.push('has-variance');\n      }\n    }\n\n    return classes.join(' ');\n  };\n\n  const formatDate = (date) => {\n    return date.toLocaleDateString('es-ES', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getSelectedDayInfo = () => {\n    if (!selectedDate) return null;\n\n    const realDay = getDayData(selectedDate, 'real');\n    const plannedDay = getDayData(selectedDate, 'planned');\n\n    return { realDay, plannedDay };\n  };\n\n  const selectedDayInfo = getSelectedDayInfo();\n\n  return (\n    <Container fluid>\n      {error && <Alert variant=\"danger\">{error}</Alert>}\n      \n      <Row>\n        <Col lg={8}>\n          <Card>\n            <Card.Header>\n              <div className=\"d-flex justify-content-between align-items-center\">\n                <h5 className=\"mb-0\">Calendario Dual</h5>\n                <div>\n                  <Button \n                    variant=\"outline-primary\" \n                    size=\"sm\" \n                    onClick={loadCalendarData}\n                    disabled={loading}\n                  >\n                    {loading ? 'Cargando...' : 'Actualizar'}\n                  </Button>\n                </div>\n              </div>\n            </Card.Header>\n            <Card.Body>\n              <div className=\"calendar-legend mb-3\">\n                <small className=\"text-muted\">\n                  <Badge bg=\"primary\" className=\"me-2\">Real</Badge>\n                  <Badge bg=\"secondary\" className=\"me-2\">Planificado</Badge>\n                  <span className=\"calendar-legend-item has-variance me-2\">Varianza</span>\n                </small>\n              </div>\n              \n              <Calendar\n                onChange={onDateSelect}\n                value={selectedDate}\n                tileContent={getTileContent}\n                tileClassName={getTileClassName}\n                locale=\"es-ES\"\n              />\n            </Card.Body>\n          </Card>\n        </Col>\n\n        <Col lg={4}>\n          <Card>\n            <Card.Header>\n              <h6 className=\"mb-0\">\n                {selectedDate ? formatDate(selectedDate) : 'Selecciona una fecha'}\n              </h6>\n            </Card.Header>\n            <Card.Body>\n              {selectedDate && selectedDayInfo && (\n                <Tabs activeKey={activeTab} onSelect={setActiveTab} className=\"mb-3\">\n                  <Tab eventKey=\"real\" title=\"Real\">\n                    {selectedDayInfo.realDay ? (\n                      <div>\n                        <p><strong>Tipo:</strong> {selectedDayInfo.realDay.type || 'No definido'}</p>\n                        <p><strong>Horas:</strong> {selectedDayInfo.realDay.hours || 0}</p>\n                        <p><strong>Descripción:</strong> {selectedDayInfo.realDay.description || 'Sin descripción'}</p>\n                        {selectedDayInfo.realDay.shift && (\n                          <p><strong>Turno:</strong> {selectedDayInfo.realDay.shift.name}</p>\n                        )}\n                      </div>\n                    ) : (\n                      <p className=\"text-muted\">No hay datos reales para esta fecha</p>\n                    )}\n                  </Tab>\n                  \n                  <Tab eventKey=\"planned\" title=\"Planificado\">\n                    {selectedDayInfo.plannedDay ? (\n                      <div>\n                        <p><strong>Tipo:</strong> {selectedDayInfo.plannedDay.type || 'No definido'}</p>\n                        <p><strong>Horas:</strong> {selectedDayInfo.plannedDay.hours || 0}</p>\n                        <p><strong>Descripción:</strong> {selectedDayInfo.plannedDay.description || 'Sin descripción'}</p>\n                        {selectedDayInfo.plannedDay.shift && (\n                          <p><strong>Turno:</strong> {selectedDayInfo.plannedDay.shift.name}</p>\n                        )}\n                      </div>\n                    ) : (\n                      <p className=\"text-muted\">No hay datos planificados para esta fecha</p>\n                    )}\n                  </Tab>\n                  \n                  <Tab eventKey=\"comparison\" title=\"Comparación\">\n                    {selectedDayInfo.realDay && selectedDayInfo.plannedDay ? (\n                      <div>\n                        <p><strong>Varianza de horas:</strong> \n                          <Badge bg={Math.abs(selectedDayInfo.realDay.hours - selectedDayInfo.plannedDay.hours) > 1 ? 'warning' : 'success'}>\n                            {(selectedDayInfo.realDay.hours - selectedDayInfo.plannedDay.hours).toFixed(2)}h\n                          </Badge>\n                        </p>\n                        <p><strong>Cumplimiento:</strong> \n                          <Badge bg={selectedDayInfo.realDay.type === selectedDayInfo.plannedDay.type ? 'success' : 'warning'}>\n                            {selectedDayInfo.realDay.type === selectedDayInfo.plannedDay.type ? 'Cumple' : 'No cumple'}\n                          </Badge>\n                        </p>\n                      </div>\n                    ) : (\n                      <p className=\"text-muted\">Necesitas datos en ambos calendarios para comparar</p>\n                    )}\n                  </Tab>\n                </Tabs>\n              )}\n              \n              {!selectedDate && (\n                <p className=\"text-muted\">Haz clic en una fecha del calendario para ver los detalles</p>\n              )}\n            </Card.Body>\n          </Card>\n\n          {comparison && (\n            <Card className=\"mt-3\">\n              <Card.Header>\n                <h6 className=\"mb-0\">Resumen del Mes</h6>\n              </Card.Header>\n              <Card.Body>\n                <p><strong>Días con datos reales:</strong> {comparison.summary?.realDaysCount || 0}</p>\n                <p><strong>Días planificados:</strong> {comparison.summary?.plannedDaysCount || 0}</p>\n                <p><strong>Cumplimiento promedio:</strong> \n                  <Badge bg=\"info\" className=\"ms-2\">\n                    {comparison.summary?.averageCompliance ? `${(comparison.summary.averageCompliance * 100).toFixed(1)}%` : 'N/A'}\n                  </Badge>\n                </p>\n              </Card.Body>\n            </Card>\n          )}\n        </Col>\n      </Row>\n    </Container>\n  );\n}\n\nexport default DualCalendarView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,QAAQ,iBAAiB;AAC5F,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAO,kCAAkC;AACzC,SACEC,cAAc,EACdC,iBAAiB,EACjBC,gBAAgB,EAChBC,mBAAmB,EACnBC,gBAAgB,QACX,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,SAASC,gBAAgBA,CAAC;EAAEC,YAAY;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA;EACxD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,MAAM,CAAC;EAElDC,SAAS,CAAC,MAAM;IACdqC,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAENrC,SAAS,CAAC,MAAM;IACd,IAAIoB,YAAY,EAAE;MAChBkB,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAClB,YAAY,EAAEK,QAAQ,EAAEE,WAAW,CAAC,CAAC;EAEzC,MAAMU,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnCL,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChD/B,cAAc,CAAC,CAAC,EAChBC,iBAAiB,CAAC,CAAC,CACpB,CAAC;MACFc,WAAW,CAACa,QAAQ,CAAC;MACrBX,cAAc,CAACY,WAAW,CAAC;MAC3BN,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZT,QAAQ,CAAC,uCAAuC,GAAGS,GAAG,CAACC,OAAO,CAAC;IACjE,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMM,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAAClB,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMyB,YAAY,GAAG,IAAIC,IAAI,CAAC1B,YAAY,CAAC2B,WAAW,CAAC,CAAC,EAAE3B,YAAY,CAAC4B,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACrF,MAAMC,UAAU,GAAG,IAAIH,IAAI,CAAC1B,YAAY,CAAC2B,WAAW,CAAC,CAAC,EAAE3B,YAAY,CAAC4B,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;MAEvF,MAAME,cAAc,GAAG,MAAMnC,gBAAgB,CAC3C8B,YAAY,CAACM,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EACxCH,UAAU,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACvC,CAAC;MACDtB,aAAa,CAACoB,cAAc,CAAC;IAC/B,CAAC,CAAC,OAAOP,GAAG,EAAE;MACZU,OAAO,CAACpB,KAAK,CAAC,2BAA2B,EAAEU,GAAG,CAAC;IACjD;EACF,CAAC;EAED,MAAMW,UAAU,GAAGA,CAACC,IAAI,EAAEC,QAAQ,KAAK;IACrC,MAAMC,OAAO,GAAGF,IAAI,CAACJ,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAChD,MAAMM,IAAI,GAAGF,QAAQ,KAAK,MAAM,GAAG/B,QAAQ,GAAGE,WAAW;IACzD,OAAO+B,IAAI,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACL,IAAI,KAAKE,OAAO,CAAC;EAC/C,CAAC;EAED,MAAMI,cAAc,GAAGA,CAAC;IAAEN,IAAI;IAAEO;EAAK,CAAC,KAAK;IACzC,IAAIA,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;IAEjC,MAAMC,OAAO,GAAGT,UAAU,CAACC,IAAI,EAAE,MAAM,CAAC;IACxC,MAAMS,UAAU,GAAGV,UAAU,CAACC,IAAI,EAAE,SAAS,CAAC;IAE9C,oBACEtC,OAAA;MAAKgD,SAAS,EAAC,uBAAuB;MAAAC,QAAA,GACnCH,OAAO,iBACN9C,OAAA;QAAKgD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BjD,OAAA,CAACX,KAAK;UAAC6D,EAAE,EAAC,SAAS;UAACC,IAAI,EAAC,IAAI;UAAAF,QAAA,GAAEH,OAAO,CAACM,KAAK,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CACN,EACAT,UAAU,iBACT/C,OAAA;QAAKgD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCjD,OAAA,CAACX,KAAK;UAAC6D,EAAE,EAAC,WAAW;UAACC,IAAI,EAAC,IAAI;UAAAF,QAAA,GAAEF,UAAU,CAACK,KAAK,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAC;IAAEnB,IAAI;IAAEO;EAAK,CAAC,KAAK;IAC3C,IAAIA,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;IAEjC,MAAMC,OAAO,GAAGT,UAAU,CAACC,IAAI,EAAE,MAAM,CAAC;IACxC,MAAMS,UAAU,GAAGV,UAAU,CAACC,IAAI,EAAE,SAAS,CAAC;IAC9C,MAAMoB,OAAO,GAAG,EAAE;IAElB,IAAIZ,OAAO,IAAIA,OAAO,CAACa,IAAI,KAAK,QAAQ,EAAE;MACxCD,OAAO,CAACE,IAAI,CAAC,eAAe,CAAC;IAC/B;IACA,IAAIb,UAAU,IAAIA,UAAU,CAACY,IAAI,KAAK,QAAQ,EAAE;MAC9CD,OAAO,CAACE,IAAI,CAAC,kBAAkB,CAAC;IAClC;IACA,IAAId,OAAO,IAAIC,UAAU,EAAE;MACzB,MAAMc,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACjB,OAAO,CAACM,KAAK,GAAGL,UAAU,CAACK,KAAK,CAAC;MAC3D,IAAIS,QAAQ,GAAG,CAAC,EAAE;QAChBH,OAAO,CAACE,IAAI,CAAC,cAAc,CAAC;MAC9B;IACF;IAEA,OAAOF,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC;EAC1B,CAAC;EAED,MAAMC,UAAU,GAAI3B,IAAI,IAAK;IAC3B,OAAOA,IAAI,CAAC4B,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACb1B,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAM2B,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAACnE,YAAY,EAAE,OAAO,IAAI;IAE9B,MAAM2C,OAAO,GAAGT,UAAU,CAAClC,YAAY,EAAE,MAAM,CAAC;IAChD,MAAM4C,UAAU,GAAGV,UAAU,CAAClC,YAAY,EAAE,SAAS,CAAC;IAEtD,OAAO;MAAE2C,OAAO;MAAEC;IAAW,CAAC;EAChC,CAAC;EAED,MAAMwB,eAAe,GAAGD,kBAAkB,CAAC,CAAC;EAE5C,oBACEtE,OAAA,CAAChB,SAAS;IAACwF,KAAK;IAAAvB,QAAA,GACbjC,KAAK,iBAAIhB,OAAA,CAACV,KAAK;MAACmF,OAAO,EAAC,QAAQ;MAAAxB,QAAA,EAAEjC;IAAK;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEjDxD,OAAA,CAACf,GAAG;MAAAgE,QAAA,gBACFjD,OAAA,CAACd,GAAG;QAACwF,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACTjD,OAAA,CAACb,IAAI;UAAA8D,QAAA,gBACHjD,OAAA,CAACb,IAAI,CAACwF,MAAM;YAAA1B,QAAA,eACVjD,OAAA;cAAKgD,SAAS,EAAC,mDAAmD;cAAAC,QAAA,gBAChEjD,OAAA;gBAAIgD,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAe;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzCxD,OAAA;gBAAAiD,QAAA,eACEjD,OAAA,CAACZ,MAAM;kBACLqF,OAAO,EAAC,iBAAiB;kBACzBtB,IAAI,EAAC,IAAI;kBACTyB,OAAO,EAAExD,gBAAiB;kBAC1ByD,QAAQ,EAAE/D,OAAQ;kBAAAmC,QAAA,EAEjBnC,OAAO,GAAG,aAAa,GAAG;gBAAY;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACdxD,OAAA,CAACb,IAAI,CAAC2F,IAAI;YAAA7B,QAAA,gBACRjD,OAAA;cAAKgD,SAAS,EAAC,sBAAsB;cAAAC,QAAA,eACnCjD,OAAA;gBAAOgD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBAC3BjD,OAAA,CAACX,KAAK;kBAAC6D,EAAE,EAAC,SAAS;kBAACF,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAI;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjDxD,OAAA,CAACX,KAAK;kBAAC6D,EAAE,EAAC,WAAW;kBAACF,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAW;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1DxD,OAAA;kBAAMgD,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENxD,OAAA,CAACP,QAAQ;cACPsF,QAAQ,EAAE7E,YAAa;cACvB8E,KAAK,EAAE7E,YAAa;cACpB8E,WAAW,EAAErC,cAAe;cAC5BsC,aAAa,EAAEzB,gBAAiB;cAChC0B,MAAM,EAAC;YAAO;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENxD,OAAA,CAACd,GAAG;QAACwF,EAAE,EAAE,CAAE;QAAAzB,QAAA,gBACTjD,OAAA,CAACb,IAAI;UAAA8D,QAAA,gBACHjD,OAAA,CAACb,IAAI,CAACwF,MAAM;YAAA1B,QAAA,eACVjD,OAAA;cAAIgD,SAAS,EAAC,MAAM;cAAAC,QAAA,EACjB9C,YAAY,GAAG8D,UAAU,CAAC9D,YAAY,CAAC,GAAG;YAAsB;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdxD,OAAA,CAACb,IAAI,CAAC2F,IAAI;YAAA7B,QAAA,GACP9C,YAAY,IAAIoE,eAAe,iBAC9BvE,OAAA,CAACT,IAAI;cAAC6F,SAAS,EAAElE,SAAU;cAACmE,QAAQ,EAAElE,YAAa;cAAC6B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAClEjD,OAAA,CAACR,GAAG;gBAAC8F,QAAQ,EAAC,MAAM;gBAACC,KAAK,EAAC,MAAM;gBAAAtC,QAAA,EAC9BsB,eAAe,CAACzB,OAAO,gBACtB9C,OAAA;kBAAAiD,QAAA,gBACEjD,OAAA;oBAAAiD,QAAA,gBAAGjD,OAAA;sBAAAiD,QAAA,EAAQ;oBAAK;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACe,eAAe,CAACzB,OAAO,CAACa,IAAI,IAAI,aAAa;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7ExD,OAAA;oBAAAiD,QAAA,gBAAGjD,OAAA;sBAAAiD,QAAA,EAAQ;oBAAM;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACe,eAAe,CAACzB,OAAO,CAACM,KAAK,IAAI,CAAC;kBAAA;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnExD,OAAA;oBAAAiD,QAAA,gBAAGjD,OAAA;sBAAAiD,QAAA,EAAQ;oBAAY;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACe,eAAe,CAACzB,OAAO,CAAC0C,WAAW,IAAI,iBAAiB;kBAAA;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAC9Fe,eAAe,CAACzB,OAAO,CAAC2C,KAAK,iBAC5BzF,OAAA;oBAAAiD,QAAA,gBAAGjD,OAAA;sBAAAiD,QAAA,EAAQ;oBAAM;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACe,eAAe,CAACzB,OAAO,CAAC2C,KAAK,CAACC,IAAI;kBAAA;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CACnE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,gBAENxD,OAAA;kBAAGgD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAmC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cACjE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENxD,OAAA,CAACR,GAAG;gBAAC8F,QAAQ,EAAC,SAAS;gBAACC,KAAK,EAAC,aAAa;gBAAAtC,QAAA,EACxCsB,eAAe,CAACxB,UAAU,gBACzB/C,OAAA;kBAAAiD,QAAA,gBACEjD,OAAA;oBAAAiD,QAAA,gBAAGjD,OAAA;sBAAAiD,QAAA,EAAQ;oBAAK;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACe,eAAe,CAACxB,UAAU,CAACY,IAAI,IAAI,aAAa;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChFxD,OAAA;oBAAAiD,QAAA,gBAAGjD,OAAA;sBAAAiD,QAAA,EAAQ;oBAAM;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACe,eAAe,CAACxB,UAAU,CAACK,KAAK,IAAI,CAAC;kBAAA;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtExD,OAAA;oBAAAiD,QAAA,gBAAGjD,OAAA;sBAAAiD,QAAA,EAAQ;oBAAY;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACe,eAAe,CAACxB,UAAU,CAACyC,WAAW,IAAI,iBAAiB;kBAAA;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACjGe,eAAe,CAACxB,UAAU,CAAC0C,KAAK,iBAC/BzF,OAAA;oBAAAiD,QAAA,gBAAGjD,OAAA;sBAAAiD,QAAA,EAAQ;oBAAM;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACe,eAAe,CAACxB,UAAU,CAAC0C,KAAK,CAACC,IAAI;kBAAA;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CACtE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,gBAENxD,OAAA;kBAAGgD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAyC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cACvE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENxD,OAAA,CAACR,GAAG;gBAAC8F,QAAQ,EAAC,YAAY;gBAACC,KAAK,EAAC,gBAAa;gBAAAtC,QAAA,EAC3CsB,eAAe,CAACzB,OAAO,IAAIyB,eAAe,CAACxB,UAAU,gBACpD/C,OAAA;kBAAAiD,QAAA,gBACEjD,OAAA;oBAAAiD,QAAA,gBAAGjD,OAAA;sBAAAiD,QAAA,EAAQ;oBAAkB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCxD,OAAA,CAACX,KAAK;sBAAC6D,EAAE,EAAEY,IAAI,CAACC,GAAG,CAACQ,eAAe,CAACzB,OAAO,CAACM,KAAK,GAAGmB,eAAe,CAACxB,UAAU,CAACK,KAAK,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,SAAU;sBAAAH,QAAA,GAC/G,CAACsB,eAAe,CAACzB,OAAO,CAACM,KAAK,GAAGmB,eAAe,CAACxB,UAAU,CAACK,KAAK,EAAEuC,OAAO,CAAC,CAAC,CAAC,EAAC,GACjF;oBAAA;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACJxD,OAAA;oBAAAiD,QAAA,gBAAGjD,OAAA;sBAAAiD,QAAA,EAAQ;oBAAa;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC/BxD,OAAA,CAACX,KAAK;sBAAC6D,EAAE,EAAEqB,eAAe,CAACzB,OAAO,CAACa,IAAI,KAAKY,eAAe,CAACxB,UAAU,CAACY,IAAI,GAAG,SAAS,GAAG,SAAU;sBAAAV,QAAA,EACjGsB,eAAe,CAACzB,OAAO,CAACa,IAAI,KAAKY,eAAe,CAACxB,UAAU,CAACY,IAAI,GAAG,QAAQ,GAAG;oBAAW;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,gBAENxD,OAAA;kBAAGgD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAkD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAChF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACP,EAEA,CAACrD,YAAY,iBACZH,OAAA;cAAGgD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAA0D;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACxF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAEN5C,UAAU,iBACTZ,OAAA,CAACb,IAAI;UAAC6D,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACpBjD,OAAA,CAACb,IAAI,CAACwF,MAAM;YAAA1B,QAAA,eACVjD,OAAA;cAAIgD,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACdxD,OAAA,CAACb,IAAI,CAAC2F,IAAI;YAAA7B,QAAA,gBACRjD,OAAA;cAAAiD,QAAA,gBAAGjD,OAAA;gBAAAiD,QAAA,EAAQ;cAAsB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,EAAAnD,mBAAA,GAAAO,UAAU,CAACgF,OAAO,cAAAvF,mBAAA,uBAAlBA,mBAAA,CAAoBwF,aAAa,KAAI,CAAC;YAAA;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvFxD,OAAA;cAAAiD,QAAA,gBAAGjD,OAAA;gBAAAiD,QAAA,EAAQ;cAAkB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,EAAAlD,oBAAA,GAAAM,UAAU,CAACgF,OAAO,cAAAtF,oBAAA,uBAAlBA,oBAAA,CAAoBwF,gBAAgB,KAAI,CAAC;YAAA;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtFxD,OAAA;cAAAiD,QAAA,gBAAGjD,OAAA;gBAAAiD,QAAA,EAAQ;cAAsB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCxD,OAAA,CAACX,KAAK;gBAAC6D,EAAE,EAAC,MAAM;gBAACF,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAC9B,CAAA1C,oBAAA,GAAAK,UAAU,CAACgF,OAAO,cAAArF,oBAAA,eAAlBA,oBAAA,CAAoBwF,iBAAiB,GAAG,GAAG,CAACnF,UAAU,CAACgF,OAAO,CAACG,iBAAiB,GAAG,GAAG,EAAEJ,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;cAAK;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB;AAACpD,EAAA,CA5PQH,gBAAgB;AAAA+F,EAAA,GAAhB/F,gBAAgB;AA8PzB,eAAeA,gBAAgB;AAAC,IAAA+F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}