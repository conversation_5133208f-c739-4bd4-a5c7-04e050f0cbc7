const { readData, writeData } = require('../data/dataHandler');
const { v4: uuidv4 } = require('uuid');

const getAllPatterns = () => {
    const data = readData();
    return data.patterns || [];
};

const getPatternById = (id) => {
    const data = readData();
    return (data.patterns || []).find(pattern => pattern.id === id);
};

const createPattern = (name, basePattern, overrides = []) => {
    const data = readData();
    const newPattern = { id: uuidv4(), name, basePattern, overrides };
    if (!data.patterns) {
        data.patterns = [];
    }
    data.patterns.push(newPattern);
    writeData(data);
    return newPattern;
};

const updatePattern = (id, name, basePattern, overrides = []) => {
    const data = readData();
    const patternIndex = (data.patterns || []).findIndex(pattern => pattern.id === id);
    if (patternIndex === -1) {
        return null;
    }
    data.patterns[patternIndex] = { ...data.patterns[patternIndex], name, basePattern, overrides };
    writeData(data);
    return data.patterns[patternIndex];
};

const deletePattern = (id) => {
    const data = readData();
    const initialLength = (data.patterns || []).length;
    data.patterns = (data.patterns || []).filter(pattern => pattern.id !== id);
    if (data.patterns.length === initialLength) {
        return false; // No pattern was deleted
    }
    writeData(data);
    return true;
};

const copyPattern = (id, newName, newYear) => {
    const data = readData();
    const originalPattern = (data.patterns || []).find(pattern => pattern.id === id);

    if (!originalPattern) {
        return null;
    }

    const currentYear = new Date().getFullYear();
    const yearDifference = newYear - currentYear;

    const copiedOverrides = originalPattern.overrides.map(override => {
        const startDate = new Date(override.startDate);
        startDate.setFullYear(startDate.getFullYear() + yearDifference);
        const endDate = new Date(override.endDate);
        endDate.setFullYear(endDate.getFullYear() + yearDifference);

        return {
            ...override,
            startDate: startDate.toISOString().split('T')[0],
            endDate: endDate.toISOString().split('T')[0],
        };
    });

    const newPattern = {
        ...originalPattern,
        id: uuidv4(),
        name: newName,
        overrides: copiedOverrides,
    };

    data.patterns.push(newPattern);
    writeData(data);
    return newPattern;
};

const applyPatternToDates = (startDate, endDate, patternId) => {
    // Use dynamic imports to avoid circular dependencies
    const calendarService = require('./calendarService');
    const shiftService = require('./shiftService');

    const pattern = getPatternById(patternId);

    if (!pattern) {
        throw new Error('Pattern not found.');
    }

    const start = new Date(startDate);
    const end = new Date(endDate);
    let currentDate = new Date(start);

    while (currentDate <= end) {
        const formattedDate = currentDate.toISOString().split('T')[0];
        let dayDefinition = null;

        // Check for overrides first
        for (const override of pattern.overrides) {
            const overrideStartDate = new Date(override.startDate);
            const overrideEndDate = new Date(override.endDate);
            if (currentDate >= overrideStartDate && currentDate <= overrideEndDate) {
                const dayIndex = (currentDate.getTime() - overrideStartDate.getTime()) / (1000 * 60 * 60 * 24);
                dayDefinition = override.overridePattern[dayIndex % override.overridePattern.length];
                break;
            }
        }

        // If no override, use base pattern
        if (!dayDefinition) {
            const dayOfWeek = currentDate.getDay(); // 0 for Sunday, 1 for Monday, ..., 6 for Saturday
            dayDefinition = pattern.basePattern[dayOfWeek];
        }

        if (dayDefinition) {
            let type = dayDefinition.type;
            let hours = dayDefinition.hours || 0;
            let description = dayDefinition.description || '';
            let shiftId = dayDefinition.shiftId || null;

            if (shiftId) {
                const shift = shiftService.getShiftById(shiftId);
                if (shift) {
                    type = 'worked'; // Assuming a shift means a worked day
                    hours = shift.totalHours;
                    description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;
                }
            }
            // CAMBIO CRÍTICO: Ahora genera el calendario PLANIFICADO, no el real
            calendarService.createOrUpdatePlannedDay(formattedDate, type, hours, description, shiftId);
        }

        currentDate.setDate(currentDate.getDate() + 1);
    }
    return { message: 'Patrón aplicado al calendario planificado exitosamente.' };
};

module.exports = {
    getAllPatterns,
    getPatternById,
    createPattern,
    updatePattern,
    deletePattern,
    copyPattern,
    applyPatternToDates
};