import React, { useState, useEffect } from 'react';
import { Modal, Form, Button, Alert, Row, Col, Card, Badge } from 'react-bootstrap';
import {
  createOrUpdateRealDay,
  getRealDayByDate,
  getAllShifts,
  applyShiftToDate,
  suggestShiftsForDate
} from '../services/api';

function RealCalendarManager({ show, onHide, selectedDate, onDayUpdated }) {
  const [dayType, setDayType] = useState('');
  const [hours, setHours] = useState(0);
  const [description, setDescription] = useState('');
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [selectedShift, setSelectedShift] = useState('');
  const [useShift, setUseShift] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [availableShifts, setAvailableShifts] = useState([]);
  const [suggestedShifts, setSuggestedShifts] = useState([]);
  const [existingDay, setExistingDay] = useState(null);

  useEffect(() => {
    if (show) {
      loadAvailableShifts();
      if (selectedDate) {
        loadExistingDay();
        loadSuggestedShifts();
      }
    }
  }, [show, selectedDate]);

  const loadAvailableShifts = async () => {
    try {
      const shifts = await getAllShifts();
      setAvailableShifts(shifts);
    } catch (err) {
      console.error('Error loading shifts:', err);
    }
  };

  const loadExistingDay = async () => {
    if (!selectedDate) return;
    
    try {
      const dateStr = selectedDate.toISOString().split('T')[0];
      const day = await getRealDayByDate(dateStr);
      
      if (day) {
        setExistingDay(day);
        setDayType(day.type || '');
        setHours(day.hours || 0);
        setDescription(day.description || '');
        if (day.shiftId) {
          setSelectedShift(day.shiftId);
          setUseShift(true);
          setStartTime(day.startTime || '');
          setEndTime(day.endTime || '');
        } else {
          setStartTime('');
          setEndTime('');
        }
      } else {
        resetForm();
      }
    } catch (err) {
      console.error('Error loading existing day:', err);
      resetForm();
    }
  };

  const loadSuggestedShifts = async () => {
    if (!selectedDate) return;
    
    try {
      const dateStr = selectedDate.toISOString().split('T')[0];
      const suggestions = await suggestShiftsForDate(dateStr);
      setSuggestedShifts(suggestions);
    } catch (err) {
      console.error('Error loading suggested shifts:', err);
    }
  };

  const resetForm = () => {
    setExistingDay(null);
    setDayType('');
    setHours(0);
    setDescription('');
    setSelectedShift('');
    setUseShift(false);
    setStartTime('');
    setEndTime('');
    setError('');
  };

  const handleShiftSelect = (shift) => {
    setSelectedShift(shift.id);
    setUseShift(true);
    setDayType('worked');
    setHours(shift.totalHours); // Use totalHours from shift
    setDescription(`${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`);
    setStartTime(shift.startTime);
    setEndTime(shift.endTime);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const dateStr = selectedDate.toISOString().split('T')[0];
      
      if (useShift && selectedShift) {
        // Usar turno predefinido
        await createOrUpdateRealDay(dateStr, dayType, hours, description, selectedShift, startTime, endTime);
      } else {
        // Entrada manual
        await createOrUpdateRealDay(dateStr, dayType, hours, description, null, startTime, endTime);
      }

      if (onDayUpdated) {
        onDayUpdated();
      }
      onHide();
    } catch (err) {
      setError('Error guardando el día: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!existingDay || !window.confirm('¿Estás seguro de que quieres eliminar este día?')) {
      return;
    }
    setLoading(true);
    setError('');
    try {
      const dateStr = selectedDate.toISOString().split('T')[0];
      await deleteRealDay(dateStr);
      if (onDayUpdated) {
        onDayUpdated();
      }
      onHide();
    } catch (err) {
      setError('Error eliminando el día: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (date) => {
    return date?.toLocaleDateString('es-ES', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Modal show={show} onHide={onHide} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>
          {existingDay ? 'Editar' : 'Registrar'} Día Real
          {selectedDate && (
            <div className="text-muted fs-6 mt-1">
              {formatDate(selectedDate)}
            </div>
          )}
        </Modal.Title>
      </Modal.Header>

      <Modal.Body>
        {error && <Alert variant="danger">{error}</Alert>}

        <Form onSubmit={handleSubmit}>
          {/* Sección de turnos sugeridos */}
          {suggestedShifts.length > 0 && (
            <div className="mb-4">
              <h6>Turnos Sugeridos</h6>
              <Row>
                {suggestedShifts.map((shift) => (
                  <Col md={6} key={shift.id} className="mb-2">
                    <Card 
                      className={`shift-suggestion-card ${selectedShift === shift.id ? 'selected' : ''}`}
                      onClick={() => handleShiftSelect(shift)}
                    >
                      <Card.Body className="p-2">
                        <div className="d-flex justify-content-between align-items-center">
                          <div>
                            <strong>{shift.name}</strong>
                            <div className="text-muted small">
                              {shift.startTime} - {shift.endTime} ({shift.breakMinutes}min)
                            </div>
                          </div>
                          <Badge bg="primary">{shift.totalHours}h</Badge>
                        </div>
                      </Card.Body>
                    </Card>
                  </Col>
                ))}
              </Row>
            </div>
          )}

          {/* Opción de usar turno predefinido */}
          <Form.Group className="mb-3">
            <Form.Check
              type="checkbox"
              id="useShift"
              label="Usar turno predefinido"
              checked={useShift}
              onChange={(e) => setUseShift(e.target.checked)}
            />
          </Form.Group>

          {useShift && (
            <Form.Group className="mb-3">
              <Form.Label>Turno</Form.Label>
              <Form.Select
                value={selectedShift}
                onChange={(e) => {
                  const shift = availableShifts.find(s => s.id === e.target.value);
                  if (shift) {
                    handleShiftSelect(shift);
                  } else {
                    setSelectedShift('');
                    setStartTime('');
                    setEndTime('');
                    setHours(0);
                    setDescription('');
                  }
                }}
                required
              >
                <option value="">Selecciona un turno</option>
                {availableShifts.map((shift) => (
                  <option key={shift.id} value={shift.id}>
                    {shift.name} ({shift.startTime} - {shift.endTime}) - {shift.totalHours}h
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
          )}

          {/* Campos de hora de inicio y fin (visibles si se usa turno o entrada manual) */}
          {(useShift || dayType === 'worked') && (
            <Row className="mb-3">
              <Col>
                <Form.Group controlId="startTime">
                  <Form.Label>Hora de Inicio</Form.Label>
                  <Form.Control
                    type="time"
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                    required={useShift || dayType === 'worked'}
                  />
                </Form.Group>
              </Col>
              <Col>
                <Form.Group controlId="endTime">
                  <Form.Label>Hora de Fin</Form.Label>
                  <Form.Control
                    type="time"
                    value={endTime}
                    onChange={(e) => setEndTime(e.target.value)}
                    required={useShift || dayType === 'worked'}
                  />
                </Form.Group>
              </Col>
            </Row>
          )}

          {!useShift && (
            <>
              <Form.Group className="mb-3">
                <Form.Label>Tipo de día</Form.Label>
                <Form.Select
                  value={dayType}
                  onChange={(e) => setDayType(e.target.value)}
                  required
                >
                  <option value="">Selecciona el tipo</option>
                  <option value="worked">Trabajado</option>
                  <option value="rest">Descanso</option>
                  <option value="vacation">Vacaciones</option>
                  <option value="sick">Baja médica</option>
                  <option value="holiday">Festivo</option>
                </Form.Select>
              </Form.Group>

              {dayType === 'worked' && (
                <Form.Group className="mb-3">
                  <Form.Label>Horas trabajadas (calculado si hay horas de inicio/fin)</Form.Label>
                  <Form.Control
                    type="number"
                    step="0.5"
                    min="0"
                    max="24"
                    value={hours}
                    onChange={(e) => setHours(parseFloat(e.target.value) || 0)}
                    required
                  />
                </Form.Group>
              )}
            </>
          )}

          <Form.Group className="mb-3">
            <Form.Label>Descripción</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Descripción opcional del día..."
            />
          </Form.Group>

          {existingDay && (
            <Alert variant="info">
              <strong>Día existente:</strong> {existingDay.type} - {existingDay.hours}h
              <br />
              <small>{existingDay.description}</small>
            </Alert>
          )}
        </Form>
      </Modal.Body>

      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Cancelar
        </Button>
        {existingDay && (
          <Button variant="danger" onClick={handleDelete} disabled={loading} className="me-2">
            Eliminar
          </Button>
        )}
        <Button 
          variant="primary" 
          onClick={handleSubmit}
          disabled={loading || (!useShift && !dayType)}
        >
          {loading ? 'Guardando...' : (existingDay ? 'Actualizar' : 'Guardar')}
        </Button>
      </Modal.Footer>
    </Modal>
  );
}

export default RealCalendarManager;
