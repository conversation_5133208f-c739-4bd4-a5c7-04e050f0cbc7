import React, { useState } from 'react';
import { Container, Row, Col, Tabs, Tab, Alert } from 'react-bootstrap';

function CalendarPage() {
  const [activeTab, setActiveTab] = useState('real');

  return (
    <Container fluid>
      <Row className="mb-3">
        <Col>
          <h2>Sistema de Horarios</h2>
        </Col>
      </Row>

      <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-3">
        <Tab eventKey="real" title="📅 Calendario Real">
          <div className="p-4">
            <h4>Calendario de Trabajo Real</h4>
            <Alert variant="info">
              <h5>Funcionalidades del Calendario Real:</h5>
              <ul>
                <li>✅ Registrar días trabajados</li>
                <li>✅ Editar registros existentes</li>
                <li>✅ Eliminar días registrados</li>
                <li>✅ Definir horas de entrada y salida específicas</li>
                <li>✅ Soporte para turnos parciales</li>
                <li>✅ Estadísticas en tiempo real</li>
              </ul>
              <p><strong>Estado:</strong> Implementación en progreso</p>
            </Alert>
          </div>
        </Tab>
        <Tab eventKey="planned" title="📋 Calendario Teórico">
          <div className="p-4">
            <h4>Calendario Teórico (Planificado)</h4>
            <Alert variant="success">
              <h5>Funcionalidades del Calendario Teórico:</h5>
              <ul>
                <li>✅ Aplicar patrones de horarios</li>
                <li>✅ Vista previa de patrones</li>
                <li>✅ Eliminar días planificados</li>
                <li>✅ Limpiar calendario completo</li>
                <li>✅ Estadísticas de planificación</li>
                <li>✅ Completamente separado del calendario real</li>
              </ul>
              <p><strong>Estado:</strong> Implementación en progreso</p>
            </Alert>
          </div>
        </Tab>
        <Tab eventKey="analysis" title="📊 Análisis">
          <div className="p-4">
            <h4>Análisis y Comparación</h4>
            <Alert variant="warning">
              <h5>Funcionalidades de Análisis:</h5>
              <ul>
                <li>✅ Comparar calendario real vs teórico</li>
                <li>✅ Estadísticas de cumplimiento</li>
                <li>✅ Análisis de variaciones</li>
                <li>✅ Reportes detallados</li>
              </ul>
              <p><strong>Estado:</strong> Disponible (componente existente)</p>
            </Alert>
          </div>
        </Tab>
      </Tabs>
    </Container>
  );
}

export default CalendarPage;
