import React, { useState, useEffect } from 'react';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css';
import { Container, Row, Col, Button, Modal, Form, Alert, Badge } from 'react-bootstrap';
import {
  getDayByDate,
  createOrUpdateDay,
  getAllShifts,
  applyShiftToDate,
  suggestShiftsForDate,
  applyPattern,
  getAllPatterns
} from '../services/api';

function CalendarPage() {
  const [date, setDate] = useState(new Date());
  const [showModal, setShowModal] = useState(false);
  const [selectedDay, setSelectedDay] = useState(null);
  const [dayType, setDayType] = useState('');
  const [hours, setHours] = useState(0);
  const [description, setDescription] = useState('');

  // Estados para turnos predefinidos
  const [availableShifts, setAvailableShifts] = useState([]);
  const [selectedShift, setSelectedShift] = useState('');
  const [suggestedShifts, setSuggestedShifts] = useState([]);
  const [useShift, setUseShift] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Estados para el calendario patrón
  const [selectionStartDate, setSelectionStartDate] = useState(null);
  const [selectionEndDate, setSelectionEndDate] = useState(null);
  const [allPatterns, setAllPatterns] = useState([]);
  const [selectedPatternId, setSelectedPatternId] = useState('');

  useEffect(() => {
    // Cargar turnos disponibles al montar el componente
    loadAvailableShifts();
    loadAllPatterns(); // Load all patterns
  }, []);

  useEffect(() => {
    if (selectedDay) {
      fetchDayDetails(selectedDay);
      loadSuggestedShifts(selectedDay);
    }
  }, [selectedDay]);

  const loadAllPatterns = async () => {
    try {
      const patterns = await getAllPatterns();
      setAllPatterns(patterns);
    } catch (error) {
      console.error('Error loading patterns:', error);
      setError('Error cargando patrones.');
    }
  };

  const loadAvailableShifts = async () => {
    try {
      const shifts = await getAllShifts();
      setAvailableShifts(shifts);
    } catch (error) {
      console.error('Error loading shifts:', error);
      setError('Error cargando turnos predefinidos');
    }
  };

  const loadSuggestedShifts = async (date) => {
    try {
      const formattedDate = date.toISOString().split('T')[0];
      const suggestions = await suggestShiftsForDate(formattedDate);
      setSuggestedShifts(suggestions.suggestions || []);
    } catch (error) {
      console.error('Error loading suggested shifts:', error);
      setSuggestedShifts([]);
    }
  };

  const fetchDayDetails = async (date) => {
    try {
      const formattedDate = date.toISOString().split('T')[0];
      const response = await getDayByDate(formattedDate);
      if (response) {
        setDayType(response.type || '');
        setHours(response.hours || 0);
        setDescription(response.description || '');
        // Si el día tiene un turno asignado, mostrar esa información
        if (response.shift) {
          setSelectedShift(response.shift.id);
          setUseShift(true);
        } else {
          setSelectedShift('');
          setUseShift(false);
        }
      } else {
        resetForm();
      }
    } catch (error) {
      console.error('Error fetching day details:', error);
      resetForm();
    }
  };

  const resetForm = () => {
    setDayType('');
    setHours(0);
    setDescription('');
    setSelectedShift('');
    setUseShift(false);
    setError('');
  };

  const handleDateClick = (value) => {
    if (!selectionStartDate) {
      setSelectionStartDate(value);
      setSelectionEndDate(null);
    } else if (!selectionEndDate) {
      if (value < selectionStartDate) {
        setSelectionEndDate(selectionStartDate);
        setSelectionStartDate(value);
      } else {
        setSelectionEndDate(value);
      }
    } else {
      setSelectionStartDate(value);
      setSelectionEndDate(null);
    }
    setSelectedDay(value);
    setShowModal(true);
    setError('');
  };

  const handleShiftChange = (shiftId) => {
    setSelectedShift(shiftId);
    if (shiftId && availableShifts.length > 0) {
      const shift = availableShifts.find(s => s.id === shiftId);
      if (shift) {
        setDayType('worked');
        setHours(shift.totalHours);
        setDescription(`${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`);
      }
    }
  };

  const handleSave = async () => {
    if (!selectedDay) return;

    const formattedDate = selectedDay.toISOString().split('T')[0];
    setLoading(true);
    setError('');

    try {
      if (useShift && selectedShift) {
        // Usar turno predefinido
        await applyShiftToDate(formattedDate, selectedShift, description);
      } else {
        // Usar entrada manual
        await createOrUpdateDay(formattedDate, dayType, hours, description);
      }
      setShowModal(false);
      resetForm();
    } catch (error) {
      console.error('Error saving day:', error);
      setError('Error guardando los datos. Por favor, inténtalo de nuevo.');
    } finally {
      setLoading(false);
    }
  };

  const handleApplyPattern = async () => {
    setLoading(true);
    setError('');
    try {
      const formattedStartDate = selectionStartDate.toISOString().split('T')[0];
      const formattedEndDate = selectionEndDate.toISOString().split('T')[0];
      await applyPattern(formattedStartDate, formattedEndDate, selectedPatternId);
      setSelectionStartDate(null);
      setSelectionEndDate(null);
      setSelectedPatternId('');
      // Optionally, refresh calendar data here if needed
      alert('Patrón aplicado exitosamente!');
    } catch (error) {
      console.error('Error applying pattern:', error);
      setError('Error aplicando el patrón. Por favor, inténtalo de nuevo.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container>
      <Row className="justify-content-md-center">
        <Col md="auto">
          <h2>Calendario de Días Trabajados</h2>
          <Calendar
            onChange={setDate}
            value={date}
            onClickDay={handleDateClick}
            tileClassName={({ date, view }) => {
              if (view === 'month') {
                if (selectionStartDate && selectionEndDate) {
                  if (date >= selectionStartDate && date <= selectionEndDate) {
                    return 'selected-range';
                  }
                } else if (selectionStartDate && date.toDateString() === selectionStartDate.toDateString()) {
                  return 'selected-range-start';
                }
              }
              return null;
            }}
          />
          
        </Col>
      </Row>

      <Row className="justify-content-md-center mt-3">
        <Col md="auto">
          <h3>Aplicar Patrón a Rango Seleccionado</h3>
          {error && (
            <Alert variant="danger" className="mb-3">
              {error}
            </Alert>
          )}
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Fechas Seleccionadas:</Form.Label>
              <Form.Control
                type="text"
                value={
                  selectionStartDate && selectionEndDate
                    ? `${selectionStartDate.toDateString()} - ${selectionEndDate.toDateString()}`
                    : selectionStartDate
                    ? selectionStartDate.toDateString()
                    : 'Ninguna'
                }
                readOnly
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Seleccionar Patrón</Form.Label>
              <Form.Select
                value={selectedPatternId}
                onChange={(e) => setSelectedPatternId(e.target.value)}
              >
                <option value="">Seleccionar patrón...</option>
                {allPatterns.map((pattern) => (
                  <option key={pattern.id} value={pattern.id}>
                    {pattern.name}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>

            <Button
              variant="primary"
              onClick={handleApplyPattern}
              disabled={loading || !selectedPatternId || !selectionStartDate || !selectionEndDate}
            >
              {loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'}
            </Button>
            <Button
              variant="secondary"
              onClick={() => {
                setSelectionStartDate(null);
                setSelectionEndDate(null);
                setSelectedPatternId('');
                setError('');
              }}
              className="ms-2"
            >
              Limpiar Selección
            </Button>
          </Form>
        </Col>
      </Row>

      <Modal show={showModal} onHide={() => setShowModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Detalles del Día: {selectedDay && selectedDay.toDateString()}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {error && (
            <Alert variant="danger" className="mb-3">
              {error}
            </Alert>
          )}

          <Form>
            {/* Selector de método de entrada */}
            <Form.Group className="mb-3">
              <Form.Check
                type="radio"
                id="manual-entry"
                name="entry-method"
                label="Entrada manual"
                checked={!useShift}
                onChange={() => setUseShift(false)}
              />
              <Form.Check
                type="radio"
                id="shift-entry"
                name="entry-method"
                label="Usar turno predefinido"
                checked={useShift}
                onChange={() => setUseShift(true)}
              />
            </Form.Group>

            {/* Sección de turnos predefinidos */}
            {useShift && (
              <>
                {suggestedShifts.length > 0 && (
                  <Form.Group className="mb-3">
                    <Form.Label>Turnos sugeridos para esta fecha:</Form.Label>
                    <div className="d-flex flex-wrap gap-2 mb-2">
                      {suggestedShifts.map((shift) => (
                        <Badge
                          key={shift.id}
                          bg={selectedShift === shift.id ? "primary" : "secondary"}
                          style={{ cursor: 'pointer' }}
                          onClick={() => handleShiftChange(shift.id)}
                        >
                          {shift.name} ({shift.startTime} - {shift.endTime})
                        </Badge>
                      ))}
                    </div>
                  </Form.Group>
                )}

                <Form.Group className="mb-3">
                  <Form.Label>Seleccionar Turno</Form.Label>
                  <Form.Select
                    value={selectedShift}
                    onChange={(e) => handleShiftChange(e.target.value)}
                  >
                    <option value="">Seleccionar turno...</option>
                    {availableShifts.map((shift) => (
                      <option key={shift.id} value={shift.id}>
                        {shift.name} - {shift.startTime} a {shift.endTime} ({shift.totalHours}h)
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </>
            )}

            {/* Sección de entrada manual */}
            {!useShift && (
              <>
                <Form.Group className="mb-3">
                  <Form.Label>Tipo de Día</Form.Label>
                  <Form.Select value={dayType} onChange={(e) => setDayType(e.target.value)}>
                    <option value="">Seleccionar</option>
                    <option value="worked">Trabajado</option>
                    <option value="holiday">Vacaciones</option>
                    <option value="permit">Permiso</option>
                    <option value="negative">Cómputo Negativo</option>
                  </Form.Select>
                </Form.Group>
                <Form.Group className="mb-3">
                  <Form.Label>Horas (Cómputo Positivo/Negativo)</Form.Label>
                  <Form.Control
                    type="number"
                    value={hours}
                    onChange={(e) => setHours(parseFloat(e.target.value))}
                    placeholder="Introduce horas"
                  />
                </Form.Group>
              </>
            )}

            {/* Descripción (común para ambos métodos) */}
            <Form.Group className="mb-3">
              <Form.Label>Descripción</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Añade una descripción (opcional)"
                disabled={useShift && selectedShift && !description.includes('Personalizado:')}
              />
              {useShift && selectedShift && (
                <Form.Text className="text-muted">
                  La descripción se genera automáticamente. Puedes editarla si es necesario.
                </Form.Text>
              )}
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="secondary"
            onClick={() => {
              setShowModal(false);
              resetForm();
            }}
            disabled={loading}
          >
            Cerrar
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            disabled={loading || (!useShift && !dayType) || (useShift && !selectedShift)}
          >
            {loading ? 'Guardando...' : 'Guardar Cambios'}
          </Button>
        </Modal.Footer>
      </Modal>

      
    </Container>
  );
}

export default CalendarPage;
