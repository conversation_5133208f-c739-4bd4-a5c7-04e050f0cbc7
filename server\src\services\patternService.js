const { readData, writeData } = require('../data/dataHandler');
const { v4: uuidv4 } = require('uuid');

const getAllPatterns = () => {
    const data = readData();
    return data.patterns || [];
};

const getPatternById = (id) => {
    const data = readData();
    return (data.patterns || []).find(pattern => pattern.id === id);
};

const createPattern = (name, basePattern, overrides = []) => {
    const data = readData();
    const newPattern = { id: uuidv4(), name, basePattern, overrides };
    if (!data.patterns) {
        data.patterns = [];
    }
    data.patterns.push(newPattern);
    writeData(data);
    return newPattern;
};

const updatePattern = (id, name, basePattern, overrides = []) => {
    const data = readData();
    const patternIndex = (data.patterns || []).findIndex(pattern => pattern.id === id);
    if (patternIndex === -1) {
        return null;
    }
    data.patterns[patternIndex] = { ...data.patterns[patternIndex], name, basePattern, overrides };
    writeData(data);
    return data.patterns[patternIndex];
};

const deletePattern = (id) => {
    const data = readData();
    const initialLength = (data.patterns || []).length;
    data.patterns = (data.patterns || []).filter(pattern => pattern.id !== id);
    if (data.patterns.length === initialLength) {
        return false; // No pattern was deleted
    }
    writeData(data);
    return true;
};

const copyPattern = (id, newName, newYear) => {
    const data = readData();
    const originalPattern = (data.patterns || []).find(pattern => pattern.id === id);

    if (!originalPattern) {
        return null;
    }

    const currentYear = new Date().getFullYear();
    const yearDifference = newYear - currentYear;

    const copiedOverrides = originalPattern.overrides.map(override => {
        const startDate = new Date(override.startDate);
        startDate.setFullYear(startDate.getFullYear() + yearDifference);
        const endDate = new Date(override.endDate);
        endDate.setFullYear(endDate.getFullYear() + yearDifference);

        return {
            ...override,
            startDate: startDate.toISOString().split('T')[0],
            endDate: endDate.toISOString().split('T')[0],
        };
    });

    const newPattern = {
        ...originalPattern,
        id: uuidv4(),
        name: newName,
        overrides: copiedOverrides,
    };

    data.patterns.push(newPattern);
    writeData(data);
    return newPattern;
};

module.exports = {
    getAllPatterns,
    getPatternById,
    createPattern,
    updatePattern,
    deletePattern,
    copyPattern
};