const calendarService = require('./calendarService');

/**
 * Compara el calendario real con el planificado para un rango de fechas
 */
const compareCalendars = (startDate, endDate) => {
    const realDays = calendarService.getAllRealDays();
    const plannedDays = calendarService.getAllPlannedDays();
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    const comparison = [];
    
    let currentDate = new Date(start);
    while (currentDate <= end) {
        const formattedDate = currentDate.toISOString().split('T')[0];
        
        const realDay = realDays.find(day => day.date === formattedDate);
        const plannedDay = plannedDays.find(day => day.date === formattedDate);
        
        const dayComparison = {
            date: formattedDate,
            real: realDay || { date: formattedDate, type: '', hours: 0, description: '', shiftId: null },
            planned: plannedDay || { date: formattedDate, type: '', hours: 0, description: '', shiftId: null },
            variance: {
                hours: 0,
                status: 'no-data'
            }
        };
        
        // Calcular varianza
        if (realDay && plannedDay) {
            dayComparison.variance.hours = realDay.hours - plannedDay.hours;
            
            if (realDay.hours === plannedDay.hours && realDay.type === plannedDay.type) {
                dayComparison.variance.status = 'match';
            } else if (realDay.hours > plannedDay.hours) {
                dayComparison.variance.status = 'overtime';
            } else if (realDay.hours < plannedDay.hours) {
                dayComparison.variance.status = 'undertime';
            } else {
                dayComparison.variance.status = 'different';
            }
        } else if (realDay && !plannedDay) {
            dayComparison.variance.status = 'unplanned-work';
            dayComparison.variance.hours = realDay.hours;
        } else if (!realDay && plannedDay) {
            dayComparison.variance.status = 'missed-work';
            dayComparison.variance.hours = -plannedDay.hours;
        }
        
        comparison.push(dayComparison);
        currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return comparison;
};

/**
 * Genera estadísticas de cumplimiento para un período
 */
const generateComplianceStats = (startDate, endDate) => {
    const comparison = compareCalendars(startDate, endDate);
    
    const stats = {
        totalDays: comparison.length,
        matchingDays: 0,
        overtimeDays: 0,
        undertimeDays: 0,
        unplannedWorkDays: 0,
        missedWorkDays: 0,
        totalRealHours: 0,
        totalPlannedHours: 0,
        totalVarianceHours: 0,
        compliancePercentage: 0
    };
    
    comparison.forEach(day => {
        stats.totalRealHours += day.real.hours || 0;
        stats.totalPlannedHours += day.planned.hours || 0;
        stats.totalVarianceHours += day.variance.hours;
        
        switch (day.variance.status) {
            case 'match':
                stats.matchingDays++;
                break;
            case 'overtime':
                stats.overtimeDays++;
                break;
            case 'undertime':
                stats.undertimeDays++;
                break;
            case 'unplanned-work':
                stats.unplannedWorkDays++;
                break;
            case 'missed-work':
                stats.missedWorkDays++;
                break;
        }
    });
    
    // Calcular porcentaje de cumplimiento
    if (stats.totalDays > 0) {
        stats.compliancePercentage = Math.round((stats.matchingDays / stats.totalDays) * 100);
    }
    
    return stats;
};

/**
 * Obtiene resumen de desviaciones por tipo
 */
const getVarianceSummary = (startDate, endDate) => {
    const comparison = compareCalendars(startDate, endDate);
    
    const summary = {
        overtime: {
            days: [],
            totalHours: 0
        },
        undertime: {
            days: [],
            totalHours: 0
        },
        unplanned: {
            days: [],
            totalHours: 0
        },
        missed: {
            days: [],
            totalHours: 0
        }
    };
    
    comparison.forEach(day => {
        switch (day.variance.status) {
            case 'overtime':
                summary.overtime.days.push(day);
                summary.overtime.totalHours += day.variance.hours;
                break;
            case 'undertime':
                summary.undertime.days.push(day);
                summary.undertime.totalHours += Math.abs(day.variance.hours);
                break;
            case 'unplanned-work':
                summary.unplanned.days.push(day);
                summary.unplanned.totalHours += day.variance.hours;
                break;
            case 'missed-work':
                summary.missed.days.push(day);
                summary.missed.totalHours += Math.abs(day.variance.hours);
                break;
        }
    });
    
    return summary;
};

module.exports = {
    compareCalendars,
    generateComplianceStats,
    getVarianceSummary
};
