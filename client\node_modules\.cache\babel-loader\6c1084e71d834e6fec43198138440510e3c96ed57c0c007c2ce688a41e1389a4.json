{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\components\\\\RealDayModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Modal, Form, Button, Row, Col, Alert, ButtonGroup } from 'react-bootstrap';\nimport { createOrUpdateRealDay, deleteRealDay, getAllShifts, suggestShiftsForDate } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction RealDayModal({\n  show,\n  onHide,\n  selectedDate,\n  existingDay,\n  onDayUpdated\n}) {\n  _s();\n  const [formData, setFormData] = useState({\n    type: 'trabajo',\n    hours: '',\n    entryTime: '',\n    exitTime: '',\n    description: '',\n    shiftId: ''\n  });\n  const [useShift, setUseShift] = useState(false);\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [suggestedShifts, setSuggestedShifts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [mode, setMode] = useState('create'); // 'create', 'edit', 'delete'\n\n  useEffect(() => {\n    if (show) {\n      loadShifts();\n      loadSuggestedShifts();\n      if (existingDay) {\n        setMode('edit');\n        setFormData({\n          type: existingDay.type || 'trabajo',\n          hours: existingDay.hours || '',\n          entryTime: existingDay.entryTime || '',\n          exitTime: existingDay.exitTime || '',\n          description: existingDay.description || '',\n          shiftId: existingDay.shiftId || ''\n        });\n        setUseShift(!!existingDay.shiftId);\n      } else {\n        setMode('create');\n        resetForm();\n      }\n    }\n  }, [show, existingDay]);\n  const loadShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (err) {\n      console.error('Error loading shifts:', err);\n    }\n  };\n  const loadSuggestedShifts = async () => {\n    if (selectedDate) {\n      try {\n        const suggestions = await suggestShiftsForDate(selectedDate.toISOString().split('T')[0]);\n        setSuggestedShifts(suggestions);\n      } catch (err) {\n        console.error('Error loading suggested shifts:', err);\n      }\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      type: 'trabajo',\n      hours: '',\n      entryTime: '',\n      exitTime: '',\n      description: '',\n      shiftId: ''\n    });\n    setUseShift(false);\n    setError('');\n  };\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Auto-calcular horas si se especifican entrada y salida\n    if (field === 'entryTime' || field === 'exitTime') {\n      const entry = field === 'entryTime' ? value : formData.entryTime;\n      const exit = field === 'exitTime' ? value : formData.exitTime;\n      if (entry && exit) {\n        const hours = calculateHours(entry, exit);\n        setFormData(prev => ({\n          ...prev,\n          hours: hours.toString()\n        }));\n      }\n    }\n  };\n  const calculateHours = (entryTime, exitTime) => {\n    if (!entryTime || !exitTime) return 0;\n    const [entryHour, entryMin] = entryTime.split(':').map(Number);\n    const [exitHour, exitMin] = exitTime.split(':').map(Number);\n    let entryMinutes = entryHour * 60 + entryMin;\n    let exitMinutes = exitHour * 60 + exitMin;\n\n    // Si la salida es menor que la entrada, asumimos que es del día siguiente\n    if (exitMinutes < entryMinutes) {\n      exitMinutes += 24 * 60;\n    }\n    const diffMinutes = exitMinutes - entryMinutes;\n    return Math.round(diffMinutes / 60 * 100) / 100; // Redondear a 2 decimales\n  };\n  const handleShiftSelect = shift => {\n    setFormData(prev => ({\n      ...prev,\n      shiftId: shift.id,\n      entryTime: shift.entryTime || '',\n      exitTime: shift.exitTime || '',\n      hours: shift.hours.toString(),\n      description: shift.description || prev.description\n    }));\n    setUseShift(true);\n  };\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Validaciones\n      if (!formData.hours || parseFloat(formData.hours) <= 0) {\n        setError('Las horas deben ser un número mayor que 0');\n        return;\n      }\n      if (formData.entryTime && formData.exitTime) {\n        const calculatedHours = calculateHours(formData.entryTime, formData.exitTime);\n        if (Math.abs(calculatedHours - parseFloat(formData.hours)) > 0.1) {\n          if (!window.confirm(`Las horas calculadas (${calculatedHours}h) no coinciden con las especificadas (${formData.hours}h). ¿Continuar?`)) {\n            return;\n          }\n        }\n      }\n      const dayData = {\n        date: selectedDate.toISOString().split('T')[0],\n        type: formData.type,\n        hours: parseFloat(formData.hours),\n        entryTime: formData.entryTime || null,\n        exitTime: formData.exitTime || null,\n        description: formData.description || null,\n        shiftId: useShift ? formData.shiftId : null\n      };\n      await createOrUpdateRealDay(dayData);\n      onDayUpdated();\n      onHide();\n    } catch (err) {\n      setError('Error guardando el día: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDelete = async () => {\n    if (!existingDay) return;\n    if (window.confirm('¿Estás seguro de que quieres eliminar este registro?')) {\n      try {\n        setLoading(true);\n        await deleteRealDay(existingDay.id);\n        onDayUpdated();\n        onHide();\n      } catch (err) {\n        setError('Error eliminando el día: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    show: show,\n    onHide: onHide,\n    size: \"lg\",\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n        children: [mode === 'edit' ? 'Editar' : 'Registrar', \" D\\xEDa Real\", selectedDate && ` - ${selectedDate.toLocaleDateString('es-ES')}`]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        className: \"mb-3\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this), suggestedShifts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n          children: \"Turnos sugeridos para este d\\xEDa:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex flex-wrap gap-2\",\n          children: suggestedShifts.map(shift => /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline-info\",\n            size: \"sm\",\n            onClick: () => handleShiftSelect(shift),\n            children: [shift.name, \" (\", shift.hours, \"h)\"]\n          }, shift.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Tipo de d\\xEDa\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n            value: formData.type,\n            onChange: e => handleInputChange('type', e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"trabajo\",\n              children: \"Trabajo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"vacaciones\",\n              children: \"Vacaciones\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"baja\",\n              children: \"Baja m\\xE9dica\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"festivo\",\n              children: \"Festivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"libre\",\n              children: \"D\\xEDa libre\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: /*#__PURE__*/_jsxDEV(Form.Check, {\n            type: \"checkbox\",\n            label: \"Usar turno predefinido\",\n            checked: useShift,\n            onChange: e => setUseShift(e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), useShift && /*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Seleccionar turno\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n            value: formData.shiftId,\n            onChange: e => {\n              const shift = availableShifts.find(s => s.id === e.target.value);\n              if (shift) {\n                handleShiftSelect(shift);\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Seleccionar turno...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this), availableShifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: shift.id,\n              children: [shift.name, \" - \", shift.hours, \"h (\", shift.entryTime, \"-\", shift.exitTime, \")\"]\n            }, shift.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Horas trabajadas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n            type: \"number\",\n            step: \"0.25\",\n            min: \"0\",\n            max: \"24\",\n            value: formData.hours,\n            onChange: e => handleInputChange('hours', e.target.value),\n            placeholder: \"8.0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Hora de entrada (opcional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"time\",\n                value: formData.entryTime,\n                onChange: e => handleInputChange('entryTime', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Hora de salida (opcional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"time\",\n                value: formData.exitTime,\n                onChange: e => handleInputChange('exitTime', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Descripci\\xF3n (opcional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n            as: \"textarea\",\n            rows: 2,\n            value: formData.description,\n            onChange: e => handleInputChange('description', e.target.value),\n            placeholder: \"Detalles adicionales del d\\xEDa...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between w-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: mode === 'edit' && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"danger\",\n            onClick: handleDelete,\n            disabled: loading,\n            children: \"\\uD83D\\uDDD1\\uFE0F Eliminar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: onHide,\n            disabled: loading,\n            children: \"Cancelar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleSave,\n            disabled: loading,\n            className: \"ms-2\",\n            children: loading ? 'Guardando...' : mode === 'edit' ? 'Actualizar' : 'Guardar'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 186,\n    columnNumber: 5\n  }, this);\n}\n_s(RealDayModal, \"igQXUWAGsCoKhA/DOq3A0SyddcA=\");\n_c = RealDayModal;\nexport default RealDayModal;\nvar _c;\n$RefreshReg$(_c, \"RealDayModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Modal", "Form", "<PERSON><PERSON>", "Row", "Col", "<PERSON><PERSON>", "ButtonGroup", "createOrUpdateRealDay", "deleteRealDay", "getAllShifts", "suggestShiftsForDate", "jsxDEV", "_jsxDEV", "RealDayModal", "show", "onHide", "selectedDate", "existingDay", "onDayUpdated", "_s", "formData", "setFormData", "type", "hours", "entryTime", "exitTime", "description", "shiftId", "useShift", "setUseShift", "availableShifts", "setAvailableShifts", "suggestedShifts", "setSuggestedShifts", "loading", "setLoading", "error", "setError", "mode", "setMode", "loadShifts", "loadSuggestedShifts", "resetForm", "shifts", "err", "console", "suggestions", "toISOString", "split", "handleInputChange", "field", "value", "prev", "entry", "exit", "calculateHours", "toString", "entryHour", "entryMin", "map", "Number", "exitHour", "exitMin", "entryMinutes", "exitMinutes", "diffMinutes", "Math", "round", "handleShiftSelect", "shift", "id", "handleSave", "parseFloat", "calculatedHours", "abs", "window", "confirm", "dayData", "date", "message", "handleDelete", "size", "children", "Header", "closeButton", "Title", "toLocaleDateString", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "variant", "className", "length", "Label", "onClick", "name", "Group", "Select", "onChange", "e", "target", "Check", "label", "checked", "find", "s", "Control", "step", "min", "max", "placeholder", "md", "as", "rows", "Footer", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/components/RealDayModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Modal, <PERSON>, Button, Row, Col, Alert, ButtonGroup } from 'react-bootstrap';\nimport { \n  createOrUpdateRealDay, \n  deleteRealDay, \n  getAllShifts, \n  suggestShiftsForDate \n} from '../services/api';\n\nfunction RealDayModal({ show, onHide, selectedDate, existingDay, onDayUpdated }) {\n  const [formData, setFormData] = useState({\n    type: 'trabajo',\n    hours: '',\n    entryTime: '',\n    exitTime: '',\n    description: '',\n    shiftId: ''\n  });\n  \n  const [useShift, setUseShift] = useState(false);\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [suggestedShifts, setSuggestedShifts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [mode, setMode] = useState('create'); // 'create', 'edit', 'delete'\n\n  useEffect(() => {\n    if (show) {\n      loadShifts();\n      loadSuggestedShifts();\n      \n      if (existingDay) {\n        setMode('edit');\n        setFormData({\n          type: existingDay.type || 'trabajo',\n          hours: existingDay.hours || '',\n          entryTime: existingDay.entryTime || '',\n          exitTime: existingDay.exitTime || '',\n          description: existingDay.description || '',\n          shiftId: existingDay.shiftId || ''\n        });\n        setUseShift(!!existingDay.shiftId);\n      } else {\n        setMode('create');\n        resetForm();\n      }\n    }\n  }, [show, existingDay]);\n\n  const loadShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (err) {\n      console.error('Error loading shifts:', err);\n    }\n  };\n\n  const loadSuggestedShifts = async () => {\n    if (selectedDate) {\n      try {\n        const suggestions = await suggestShiftsForDate(selectedDate.toISOString().split('T')[0]);\n        setSuggestedShifts(suggestions);\n      } catch (err) {\n        console.error('Error loading suggested shifts:', err);\n      }\n    }\n  };\n\n  const resetForm = () => {\n    setFormData({\n      type: 'trabajo',\n      hours: '',\n      entryTime: '',\n      exitTime: '',\n      description: '',\n      shiftId: ''\n    });\n    setUseShift(false);\n    setError('');\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    \n    // Auto-calcular horas si se especifican entrada y salida\n    if (field === 'entryTime' || field === 'exitTime') {\n      const entry = field === 'entryTime' ? value : formData.entryTime;\n      const exit = field === 'exitTime' ? value : formData.exitTime;\n      \n      if (entry && exit) {\n        const hours = calculateHours(entry, exit);\n        setFormData(prev => ({ ...prev, hours: hours.toString() }));\n      }\n    }\n  };\n\n  const calculateHours = (entryTime, exitTime) => {\n    if (!entryTime || !exitTime) return 0;\n    \n    const [entryHour, entryMin] = entryTime.split(':').map(Number);\n    const [exitHour, exitMin] = exitTime.split(':').map(Number);\n    \n    let entryMinutes = entryHour * 60 + entryMin;\n    let exitMinutes = exitHour * 60 + exitMin;\n    \n    // Si la salida es menor que la entrada, asumimos que es del día siguiente\n    if (exitMinutes < entryMinutes) {\n      exitMinutes += 24 * 60;\n    }\n    \n    const diffMinutes = exitMinutes - entryMinutes;\n    return Math.round((diffMinutes / 60) * 100) / 100; // Redondear a 2 decimales\n  };\n\n  const handleShiftSelect = (shift) => {\n    setFormData(prev => ({\n      ...prev,\n      shiftId: shift.id,\n      entryTime: shift.entryTime || '',\n      exitTime: shift.exitTime || '',\n      hours: shift.hours.toString(),\n      description: shift.description || prev.description\n    }));\n    setUseShift(true);\n  };\n\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Validaciones\n      if (!formData.hours || parseFloat(formData.hours) <= 0) {\n        setError('Las horas deben ser un número mayor que 0');\n        return;\n      }\n\n      if (formData.entryTime && formData.exitTime) {\n        const calculatedHours = calculateHours(formData.entryTime, formData.exitTime);\n        if (Math.abs(calculatedHours - parseFloat(formData.hours)) > 0.1) {\n          if (!window.confirm(`Las horas calculadas (${calculatedHours}h) no coinciden con las especificadas (${formData.hours}h). ¿Continuar?`)) {\n            return;\n          }\n        }\n      }\n\n      const dayData = {\n        date: selectedDate.toISOString().split('T')[0],\n        type: formData.type,\n        hours: parseFloat(formData.hours),\n        entryTime: formData.entryTime || null,\n        exitTime: formData.exitTime || null,\n        description: formData.description || null,\n        shiftId: useShift ? formData.shiftId : null\n      };\n\n      await createOrUpdateRealDay(dayData);\n      onDayUpdated();\n      onHide();\n    } catch (err) {\n      setError('Error guardando el día: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDelete = async () => {\n    if (!existingDay) return;\n    \n    if (window.confirm('¿Estás seguro de que quieres eliminar este registro?')) {\n      try {\n        setLoading(true);\n        await deleteRealDay(existingDay.id);\n        onDayUpdated();\n        onHide();\n      } catch (err) {\n        setError('Error eliminando el día: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  return (\n    <Modal show={show} onHide={onHide} size=\"lg\">\n      <Modal.Header closeButton>\n        <Modal.Title>\n          {mode === 'edit' ? 'Editar' : 'Registrar'} Día Real\n          {selectedDate && ` - ${selectedDate.toLocaleDateString('es-ES')}`}\n        </Modal.Title>\n      </Modal.Header>\n      \n      <Modal.Body>\n        {error && (\n          <Alert variant=\"danger\" className=\"mb-3\">\n            {error}\n          </Alert>\n        )}\n\n        {/* Turnos sugeridos */}\n        {suggestedShifts.length > 0 && (\n          <div className=\"mb-3\">\n            <Form.Label>Turnos sugeridos para este día:</Form.Label>\n            <div className=\"d-flex flex-wrap gap-2\">\n              {suggestedShifts.map(shift => (\n                <Button\n                  key={shift.id}\n                  variant=\"outline-info\"\n                  size=\"sm\"\n                  onClick={() => handleShiftSelect(shift)}\n                >\n                  {shift.name} ({shift.hours}h)\n                </Button>\n              ))}\n            </div>\n          </div>\n        )}\n\n        <Form>\n          {/* Tipo de día */}\n          <Form.Group className=\"mb-3\">\n            <Form.Label>Tipo de día</Form.Label>\n            <Form.Select\n              value={formData.type}\n              onChange={(e) => handleInputChange('type', e.target.value)}\n            >\n              <option value=\"trabajo\">Trabajo</option>\n              <option value=\"vacaciones\">Vacaciones</option>\n              <option value=\"baja\">Baja médica</option>\n              <option value=\"festivo\">Festivo</option>\n              <option value=\"libre\">Día libre</option>\n            </Form.Select>\n          </Form.Group>\n\n          {/* Usar turno predefinido */}\n          <Form.Group className=\"mb-3\">\n            <Form.Check\n              type=\"checkbox\"\n              label=\"Usar turno predefinido\"\n              checked={useShift}\n              onChange={(e) => setUseShift(e.target.checked)}\n            />\n          </Form.Group>\n\n          {useShift && (\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Seleccionar turno</Form.Label>\n              <Form.Select\n                value={formData.shiftId}\n                onChange={(e) => {\n                  const shift = availableShifts.find(s => s.id === e.target.value);\n                  if (shift) {\n                    handleShiftSelect(shift);\n                  }\n                }}\n              >\n                <option value=\"\">Seleccionar turno...</option>\n                {availableShifts.map(shift => (\n                  <option key={shift.id} value={shift.id}>\n                    {shift.name} - {shift.hours}h ({shift.entryTime}-{shift.exitTime})\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n          )}\n\n          {/* Horas trabajadas */}\n          <Form.Group className=\"mb-3\">\n            <Form.Label>Horas trabajadas</Form.Label>\n            <Form.Control\n              type=\"number\"\n              step=\"0.25\"\n              min=\"0\"\n              max=\"24\"\n              value={formData.hours}\n              onChange={(e) => handleInputChange('hours', e.target.value)}\n              placeholder=\"8.0\"\n            />\n          </Form.Group>\n\n          {/* Horarios específicos */}\n          <Row>\n            <Col md={6}>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Hora de entrada (opcional)</Form.Label>\n                <Form.Control\n                  type=\"time\"\n                  value={formData.entryTime}\n                  onChange={(e) => handleInputChange('entryTime', e.target.value)}\n                />\n              </Form.Group>\n            </Col>\n            <Col md={6}>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Hora de salida (opcional)</Form.Label>\n                <Form.Control\n                  type=\"time\"\n                  value={formData.exitTime}\n                  onChange={(e) => handleInputChange('exitTime', e.target.value)}\n                />\n              </Form.Group>\n            </Col>\n          </Row>\n\n          {/* Descripción */}\n          <Form.Group className=\"mb-3\">\n            <Form.Label>Descripción (opcional)</Form.Label>\n            <Form.Control\n              as=\"textarea\"\n              rows={2}\n              value={formData.description}\n              onChange={(e) => handleInputChange('description', e.target.value)}\n              placeholder=\"Detalles adicionales del día...\"\n            />\n          </Form.Group>\n        </Form>\n      </Modal.Body>\n      \n      <Modal.Footer>\n        <div className=\"d-flex justify-content-between w-100\">\n          <div>\n            {mode === 'edit' && (\n              <Button\n                variant=\"danger\"\n                onClick={handleDelete}\n                disabled={loading}\n              >\n                🗑️ Eliminar\n              </Button>\n            )}\n          </div>\n          <div>\n            <Button variant=\"secondary\" onClick={onHide} disabled={loading}>\n              Cancelar\n            </Button>\n            <Button\n              variant=\"primary\"\n              onClick={handleSave}\n              disabled={loading}\n              className=\"ms-2\"\n            >\n              {loading ? 'Guardando...' : (mode === 'edit' ? 'Actualizar' : 'Guardar')}\n            </Button>\n          </div>\n        </div>\n      </Modal.Footer>\n    </Modal>\n  );\n}\n\nexport default RealDayModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAEC,WAAW,QAAQ,iBAAiB;AACnF,SACEC,qBAAqB,EACrBC,aAAa,EACbC,YAAY,EACZC,oBAAoB,QACf,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,SAASC,YAAYA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,YAAY;EAAEC,WAAW;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EAC/E,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC;IACvCwB,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwC,IAAI,EAAEC,OAAO,CAAC,GAAGzC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;;EAE5CC,SAAS,CAAC,MAAM;IACd,IAAIe,IAAI,EAAE;MACR0B,UAAU,CAAC,CAAC;MACZC,mBAAmB,CAAC,CAAC;MAErB,IAAIxB,WAAW,EAAE;QACfsB,OAAO,CAAC,MAAM,CAAC;QACflB,WAAW,CAAC;UACVC,IAAI,EAAEL,WAAW,CAACK,IAAI,IAAI,SAAS;UACnCC,KAAK,EAAEN,WAAW,CAACM,KAAK,IAAI,EAAE;UAC9BC,SAAS,EAAEP,WAAW,CAACO,SAAS,IAAI,EAAE;UACtCC,QAAQ,EAAER,WAAW,CAACQ,QAAQ,IAAI,EAAE;UACpCC,WAAW,EAAET,WAAW,CAACS,WAAW,IAAI,EAAE;UAC1CC,OAAO,EAAEV,WAAW,CAACU,OAAO,IAAI;QAClC,CAAC,CAAC;QACFE,WAAW,CAAC,CAAC,CAACZ,WAAW,CAACU,OAAO,CAAC;MACpC,CAAC,MAAM;QACLY,OAAO,CAAC,QAAQ,CAAC;QACjBG,SAAS,CAAC,CAAC;MACb;IACF;EACF,CAAC,EAAE,CAAC5B,IAAI,EAAEG,WAAW,CAAC,CAAC;EAEvB,MAAMuB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMG,MAAM,GAAG,MAAMlC,YAAY,CAAC,CAAC;MACnCsB,kBAAkB,CAACY,MAAM,CAAC;IAC5B,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACT,KAAK,CAAC,uBAAuB,EAAEQ,GAAG,CAAC;IAC7C;EACF,CAAC;EAED,MAAMH,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAIzB,YAAY,EAAE;MAChB,IAAI;QACF,MAAM8B,WAAW,GAAG,MAAMpC,oBAAoB,CAACM,YAAY,CAAC+B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACxFf,kBAAkB,CAACa,WAAW,CAAC;MACjC,CAAC,CAAC,OAAOF,GAAG,EAAE;QACZC,OAAO,CAACT,KAAK,CAAC,iCAAiC,EAAEQ,GAAG,CAAC;MACvD;IACF;EACF,CAAC;EAED,MAAMF,SAAS,GAAGA,CAAA,KAAM;IACtBrB,WAAW,CAAC;MACVC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE;IACX,CAAC,CAAC;IACFE,WAAW,CAAC,KAAK,CAAC;IAClBQ,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMY,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1C9B,WAAW,CAAC+B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAElD;IACA,IAAID,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,UAAU,EAAE;MACjD,MAAMG,KAAK,GAAGH,KAAK,KAAK,WAAW,GAAGC,KAAK,GAAG/B,QAAQ,CAACI,SAAS;MAChE,MAAM8B,IAAI,GAAGJ,KAAK,KAAK,UAAU,GAAGC,KAAK,GAAG/B,QAAQ,CAACK,QAAQ;MAE7D,IAAI4B,KAAK,IAAIC,IAAI,EAAE;QACjB,MAAM/B,KAAK,GAAGgC,cAAc,CAACF,KAAK,EAAEC,IAAI,CAAC;QACzCjC,WAAW,CAAC+B,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE7B,KAAK,EAAEA,KAAK,CAACiC,QAAQ,CAAC;QAAE,CAAC,CAAC,CAAC;MAC7D;IACF;EACF,CAAC;EAED,MAAMD,cAAc,GAAGA,CAAC/B,SAAS,EAAEC,QAAQ,KAAK;IAC9C,IAAI,CAACD,SAAS,IAAI,CAACC,QAAQ,EAAE,OAAO,CAAC;IAErC,MAAM,CAACgC,SAAS,EAAEC,QAAQ,CAAC,GAAGlC,SAAS,CAACwB,KAAK,CAAC,GAAG,CAAC,CAACW,GAAG,CAACC,MAAM,CAAC;IAC9D,MAAM,CAACC,QAAQ,EAAEC,OAAO,CAAC,GAAGrC,QAAQ,CAACuB,KAAK,CAAC,GAAG,CAAC,CAACW,GAAG,CAACC,MAAM,CAAC;IAE3D,IAAIG,YAAY,GAAGN,SAAS,GAAG,EAAE,GAAGC,QAAQ;IAC5C,IAAIM,WAAW,GAAGH,QAAQ,GAAG,EAAE,GAAGC,OAAO;;IAEzC;IACA,IAAIE,WAAW,GAAGD,YAAY,EAAE;MAC9BC,WAAW,IAAI,EAAE,GAAG,EAAE;IACxB;IAEA,MAAMC,WAAW,GAAGD,WAAW,GAAGD,YAAY;IAC9C,OAAOG,IAAI,CAACC,KAAK,CAAEF,WAAW,GAAG,EAAE,GAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;EACrD,CAAC;EAED,MAAMG,iBAAiB,GAAIC,KAAK,IAAK;IACnChD,WAAW,CAAC+B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPzB,OAAO,EAAE0C,KAAK,CAACC,EAAE;MACjB9C,SAAS,EAAE6C,KAAK,CAAC7C,SAAS,IAAI,EAAE;MAChCC,QAAQ,EAAE4C,KAAK,CAAC5C,QAAQ,IAAI,EAAE;MAC9BF,KAAK,EAAE8C,KAAK,CAAC9C,KAAK,CAACiC,QAAQ,CAAC,CAAC;MAC7B9B,WAAW,EAAE2C,KAAK,CAAC3C,WAAW,IAAI0B,IAAI,CAAC1B;IACzC,CAAC,CAAC,CAAC;IACHG,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAM0C,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFpC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA,IAAI,CAACjB,QAAQ,CAACG,KAAK,IAAIiD,UAAU,CAACpD,QAAQ,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;QACtDc,QAAQ,CAAC,2CAA2C,CAAC;QACrD;MACF;MAEA,IAAIjB,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACK,QAAQ,EAAE;QAC3C,MAAMgD,eAAe,GAAGlB,cAAc,CAACnC,QAAQ,CAACI,SAAS,EAAEJ,QAAQ,CAACK,QAAQ,CAAC;QAC7E,IAAIyC,IAAI,CAACQ,GAAG,CAACD,eAAe,GAAGD,UAAU,CAACpD,QAAQ,CAACG,KAAK,CAAC,CAAC,GAAG,GAAG,EAAE;UAChE,IAAI,CAACoD,MAAM,CAACC,OAAO,CAAC,yBAAyBH,eAAe,0CAA0CrD,QAAQ,CAACG,KAAK,iBAAiB,CAAC,EAAE;YACtI;UACF;QACF;MACF;MAEA,MAAMsD,OAAO,GAAG;QACdC,IAAI,EAAE9D,YAAY,CAAC+B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9C1B,IAAI,EAAEF,QAAQ,CAACE,IAAI;QACnBC,KAAK,EAAEiD,UAAU,CAACpD,QAAQ,CAACG,KAAK,CAAC;QACjCC,SAAS,EAAEJ,QAAQ,CAACI,SAAS,IAAI,IAAI;QACrCC,QAAQ,EAAEL,QAAQ,CAACK,QAAQ,IAAI,IAAI;QACnCC,WAAW,EAAEN,QAAQ,CAACM,WAAW,IAAI,IAAI;QACzCC,OAAO,EAAEC,QAAQ,GAAGR,QAAQ,CAACO,OAAO,GAAG;MACzC,CAAC;MAED,MAAMpB,qBAAqB,CAACsE,OAAO,CAAC;MACpC3D,YAAY,CAAC,CAAC;MACdH,MAAM,CAAC,CAAC;IACV,CAAC,CAAC,OAAO6B,GAAG,EAAE;MACZP,QAAQ,CAAC,0BAA0B,GAAGO,GAAG,CAACmC,OAAO,CAAC;IACpD,CAAC,SAAS;MACR5C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6C,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAC/D,WAAW,EAAE;IAElB,IAAI0D,MAAM,CAACC,OAAO,CAAC,sDAAsD,CAAC,EAAE;MAC1E,IAAI;QACFzC,UAAU,CAAC,IAAI,CAAC;QAChB,MAAM3B,aAAa,CAACS,WAAW,CAACqD,EAAE,CAAC;QACnCpD,YAAY,CAAC,CAAC;QACdH,MAAM,CAAC,CAAC;MACV,CAAC,CAAC,OAAO6B,GAAG,EAAE;QACZP,QAAQ,CAAC,2BAA2B,GAAGO,GAAG,CAACmC,OAAO,CAAC;MACrD,CAAC,SAAS;QACR5C,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;EAED,oBACEvB,OAAA,CAACZ,KAAK;IAACc,IAAI,EAAEA,IAAK;IAACC,MAAM,EAAEA,MAAO;IAACkE,IAAI,EAAC,IAAI;IAAAC,QAAA,gBAC1CtE,OAAA,CAACZ,KAAK,CAACmF,MAAM;MAACC,WAAW;MAAAF,QAAA,eACvBtE,OAAA,CAACZ,KAAK,CAACqF,KAAK;QAAAH,QAAA,GACT5C,IAAI,KAAK,MAAM,GAAG,QAAQ,GAAG,WAAW,EAAC,cAC1C,EAACtB,YAAY,IAAI,MAAMA,YAAY,CAACsE,kBAAkB,CAAC,OAAO,CAAC,EAAE;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEf9E,OAAA,CAACZ,KAAK,CAAC2F,IAAI;MAAAT,QAAA,GACR9C,KAAK,iBACJxB,OAAA,CAACP,KAAK;QAACuF,OAAO,EAAC,QAAQ;QAACC,SAAS,EAAC,MAAM;QAAAX,QAAA,EACrC9C;MAAK;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAGA1D,eAAe,CAAC8D,MAAM,GAAG,CAAC,iBACzBlF,OAAA;QAAKiF,SAAS,EAAC,MAAM;QAAAX,QAAA,gBACnBtE,OAAA,CAACX,IAAI,CAAC8F,KAAK;UAAAb,QAAA,EAAC;QAA+B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACxD9E,OAAA;UAAKiF,SAAS,EAAC,wBAAwB;UAAAX,QAAA,EACpClD,eAAe,CAAC2B,GAAG,CAACU,KAAK,iBACxBzD,OAAA,CAACV,MAAM;YAEL0F,OAAO,EAAC,cAAc;YACtBX,IAAI,EAAC,IAAI;YACTe,OAAO,EAAEA,CAAA,KAAM5B,iBAAiB,CAACC,KAAK,CAAE;YAAAa,QAAA,GAEvCb,KAAK,CAAC4B,IAAI,EAAC,IAAE,EAAC5B,KAAK,CAAC9C,KAAK,EAAC,IAC7B;UAAA,GANO8C,KAAK,CAACC,EAAE;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMP,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAED9E,OAAA,CAACX,IAAI;QAAAiF,QAAA,gBAEHtE,OAAA,CAACX,IAAI,CAACiG,KAAK;UAACL,SAAS,EAAC,MAAM;UAAAX,QAAA,gBAC1BtE,OAAA,CAACX,IAAI,CAAC8F,KAAK;YAAAb,QAAA,EAAC;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACpC9E,OAAA,CAACX,IAAI,CAACkG,MAAM;YACVhD,KAAK,EAAE/B,QAAQ,CAACE,IAAK;YACrB8E,QAAQ,EAAGC,CAAC,IAAKpD,iBAAiB,CAAC,MAAM,EAAEoD,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;YAAA+B,QAAA,gBAE3DtE,OAAA;cAAQuC,KAAK,EAAC,SAAS;cAAA+B,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC9E,OAAA;cAAQuC,KAAK,EAAC,YAAY;cAAA+B,QAAA,EAAC;YAAU;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9C9E,OAAA;cAAQuC,KAAK,EAAC,MAAM;cAAA+B,QAAA,EAAC;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzC9E,OAAA;cAAQuC,KAAK,EAAC,SAAS;cAAA+B,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC9E,OAAA;cAAQuC,KAAK,EAAC,OAAO;cAAA+B,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGb9E,OAAA,CAACX,IAAI,CAACiG,KAAK;UAACL,SAAS,EAAC,MAAM;UAAAX,QAAA,eAC1BtE,OAAA,CAACX,IAAI,CAACsG,KAAK;YACTjF,IAAI,EAAC,UAAU;YACfkF,KAAK,EAAC,wBAAwB;YAC9BC,OAAO,EAAE7E,QAAS;YAClBwE,QAAQ,EAAGC,CAAC,IAAKxE,WAAW,CAACwE,CAAC,CAACC,MAAM,CAACG,OAAO;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,EAEZ9D,QAAQ,iBACPhB,OAAA,CAACX,IAAI,CAACiG,KAAK;UAACL,SAAS,EAAC,MAAM;UAAAX,QAAA,gBAC1BtE,OAAA,CAACX,IAAI,CAAC8F,KAAK;YAAAb,QAAA,EAAC;UAAiB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC1C9E,OAAA,CAACX,IAAI,CAACkG,MAAM;YACVhD,KAAK,EAAE/B,QAAQ,CAACO,OAAQ;YACxByE,QAAQ,EAAGC,CAAC,IAAK;cACf,MAAMhC,KAAK,GAAGvC,eAAe,CAAC4E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrC,EAAE,KAAK+B,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAC;cAChE,IAAIkB,KAAK,EAAE;gBACTD,iBAAiB,CAACC,KAAK,CAAC;cAC1B;YACF,CAAE;YAAAa,QAAA,gBAEFtE,OAAA;cAAQuC,KAAK,EAAC,EAAE;cAAA+B,QAAA,EAAC;YAAoB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAC7C5D,eAAe,CAAC6B,GAAG,CAACU,KAAK,iBACxBzD,OAAA;cAAuBuC,KAAK,EAAEkB,KAAK,CAACC,EAAG;cAAAY,QAAA,GACpCb,KAAK,CAAC4B,IAAI,EAAC,KAAG,EAAC5B,KAAK,CAAC9C,KAAK,EAAC,KAAG,EAAC8C,KAAK,CAAC7C,SAAS,EAAC,GAAC,EAAC6C,KAAK,CAAC5C,QAAQ,EAAC,GACnE;YAAA,GAFa4C,KAAK,CAACC,EAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACb,eAGD9E,OAAA,CAACX,IAAI,CAACiG,KAAK;UAACL,SAAS,EAAC,MAAM;UAAAX,QAAA,gBAC1BtE,OAAA,CAACX,IAAI,CAAC8F,KAAK;YAAAb,QAAA,EAAC;UAAgB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACzC9E,OAAA,CAACX,IAAI,CAAC2G,OAAO;YACXtF,IAAI,EAAC,QAAQ;YACbuF,IAAI,EAAC,MAAM;YACXC,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,IAAI;YACR5D,KAAK,EAAE/B,QAAQ,CAACG,KAAM;YACtB6E,QAAQ,EAAGC,CAAC,IAAKpD,iBAAiB,CAAC,OAAO,EAAEoD,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;YAC5D6D,WAAW,EAAC;UAAK;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eAGb9E,OAAA,CAACT,GAAG;UAAA+E,QAAA,gBACFtE,OAAA,CAACR,GAAG;YAAC6G,EAAE,EAAE,CAAE;YAAA/B,QAAA,eACTtE,OAAA,CAACX,IAAI,CAACiG,KAAK;cAACL,SAAS,EAAC,MAAM;cAAAX,QAAA,gBAC1BtE,OAAA,CAACX,IAAI,CAAC8F,KAAK;gBAAAb,QAAA,EAAC;cAA0B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnD9E,OAAA,CAACX,IAAI,CAAC2G,OAAO;gBACXtF,IAAI,EAAC,MAAM;gBACX6B,KAAK,EAAE/B,QAAQ,CAACI,SAAU;gBAC1B4E,QAAQ,EAAGC,CAAC,IAAKpD,iBAAiB,CAAC,WAAW,EAAEoD,CAAC,CAACC,MAAM,CAACnD,KAAK;cAAE;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN9E,OAAA,CAACR,GAAG;YAAC6G,EAAE,EAAE,CAAE;YAAA/B,QAAA,eACTtE,OAAA,CAACX,IAAI,CAACiG,KAAK;cAACL,SAAS,EAAC,MAAM;cAAAX,QAAA,gBAC1BtE,OAAA,CAACX,IAAI,CAAC8F,KAAK;gBAAAb,QAAA,EAAC;cAAyB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClD9E,OAAA,CAACX,IAAI,CAAC2G,OAAO;gBACXtF,IAAI,EAAC,MAAM;gBACX6B,KAAK,EAAE/B,QAAQ,CAACK,QAAS;gBACzB2E,QAAQ,EAAGC,CAAC,IAAKpD,iBAAiB,CAAC,UAAU,EAAEoD,CAAC,CAACC,MAAM,CAACnD,KAAK;cAAE;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9E,OAAA,CAACX,IAAI,CAACiG,KAAK;UAACL,SAAS,EAAC,MAAM;UAAAX,QAAA,gBAC1BtE,OAAA,CAACX,IAAI,CAAC8F,KAAK;YAAAb,QAAA,EAAC;UAAsB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/C9E,OAAA,CAACX,IAAI,CAAC2G,OAAO;YACXM,EAAE,EAAC,UAAU;YACbC,IAAI,EAAE,CAAE;YACRhE,KAAK,EAAE/B,QAAQ,CAACM,WAAY;YAC5B0E,QAAQ,EAAGC,CAAC,IAAKpD,iBAAiB,CAAC,aAAa,EAAEoD,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;YAClE6D,WAAW,EAAC;UAAiC;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEb9E,OAAA,CAACZ,KAAK,CAACoH,MAAM;MAAAlC,QAAA,eACXtE,OAAA;QAAKiF,SAAS,EAAC,sCAAsC;QAAAX,QAAA,gBACnDtE,OAAA;UAAAsE,QAAA,EACG5C,IAAI,KAAK,MAAM,iBACd1B,OAAA,CAACV,MAAM;YACL0F,OAAO,EAAC,QAAQ;YAChBI,OAAO,EAAEhB,YAAa;YACtBqC,QAAQ,EAAEnF,OAAQ;YAAAgD,QAAA,EACnB;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN9E,OAAA;UAAAsE,QAAA,gBACEtE,OAAA,CAACV,MAAM;YAAC0F,OAAO,EAAC,WAAW;YAACI,OAAO,EAAEjF,MAAO;YAACsG,QAAQ,EAAEnF,OAAQ;YAAAgD,QAAA,EAAC;UAEhE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9E,OAAA,CAACV,MAAM;YACL0F,OAAO,EAAC,SAAS;YACjBI,OAAO,EAAEzB,UAAW;YACpB8C,QAAQ,EAAEnF,OAAQ;YAClB2D,SAAS,EAAC,MAAM;YAAAX,QAAA,EAEfhD,OAAO,GAAG,cAAc,GAAII,IAAI,KAAK,MAAM,GAAG,YAAY,GAAG;UAAU;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEZ;AAACvE,EAAA,CApVQN,YAAY;AAAAyG,EAAA,GAAZzG,YAAY;AAsVrB,eAAeA,YAAY;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}