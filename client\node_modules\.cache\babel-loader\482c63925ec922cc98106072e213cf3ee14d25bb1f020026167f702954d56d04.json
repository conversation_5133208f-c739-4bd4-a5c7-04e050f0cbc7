{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:5000/api/days';\nconst SHIFTS_API_URL = 'http://localhost:5000/api/shifts';\nconst PATTERNS_API_URL = 'http://localhost:5000/api/patterns';\nconst ANALYSIS_API_URL = 'http://localhost:5000/api/analysis';\nexport const getAllDays = async () => {\n  try {\n    const response = await axios.get(API_URL);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all days:', error);\n    throw error;\n  }\n};\nexport const getDayByDate = async date => {\n  try {\n    const response = await axios.get(`${API_URL}/${date}`);\n    return response.data;\n  } catch (error) {\n    if (error.response && error.response.status === 404) {\n      return null; // Day not found, return null instead of throwing error\n    }\n    console.error(`Error fetching day for ${date}:`, error);\n    throw error;\n  }\n};\nexport const createOrUpdateDay = async (date, type, hours, description) => {\n  try {\n    const response = await axios.post(API_URL, {\n      date,\n      type,\n      hours,\n      description\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error creating or updating day:', error);\n    throw error;\n  }\n};\n\n// ===== FUNCIONES PARA TURNOS PREDEFINIDOS =====\n\n/**\n * Obtiene todos los turnos predefinidos\n */\nexport const getAllShifts = async () => {\n  try {\n    const response = await axios.get(SHIFTS_API_URL);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all shifts:', error);\n    throw error;\n  }\n};\n\n/**\n * Obtiene un turno específico por ID\n */\nexport const getShiftById = async shiftId => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/${shiftId}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error fetching shift ${shiftId}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un turno predefinido a una fecha específica\n */\nexport const applyShiftToDate = async (date, shiftId, description = '') => {\n  try {\n    const response = await axios.post(`${SHIFTS_API_URL}/apply`, {\n      date,\n      shiftId,\n      description\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying shift to date:', error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un turno a múltiples fechas\n */\nexport const applyShiftToMultipleDates = async (dates, shiftId, description = '') => {\n  try {\n    const response = await axios.post(`${SHIFTS_API_URL}/apply-multiple`, {\n      dates,\n      shiftId,\n      description\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying shift to multiple dates:', error);\n    throw error;\n  }\n};\n\n/**\n * Obtiene estadísticas de turnos aplicados\n */\nexport const getShiftStatistics = async () => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/statistics`);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching shift statistics:', error);\n    throw error;\n  }\n};\n\n/**\n * Sugiere turnos apropiados para una fecha\n */\nexport const suggestShiftsForDate = async date => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/suggest/${date}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error suggesting shifts for date ${date}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un patrón de días a un rango de fechas usando un ID de patrón.\n */\nexport const applyPattern = async (startDate, endDate, patternId) => {\n  try {\n    const response = await axios.post(`${API_URL}/apply-pattern`, {\n      startDate,\n      endDate,\n      patternId\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying pattern:', error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un patrón de días a un rango de fechas usando un array de patrón (legacy).\n */\nexport const applyPatternArray = async (startDate, endDate, pattern) => {\n  try {\n    const response = await axios.post(`${API_URL}/apply-pattern`, {\n      startDate,\n      endDate,\n      pattern\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying pattern array:', error);\n    throw error;\n  }\n};\n\n// ===== FUNCIONES PARA PATRONES =====\n\nexport const getAllPatterns = async () => {\n  try {\n    const response = await axios.get(PATTERNS_API_URL);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all patterns:', error);\n    throw error;\n  }\n};\nexport const getPatternById = async id => {\n  try {\n    const response = await axios.get(`${PATTERNS_API_URL}/${id}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error fetching pattern ${id}:`, error);\n    throw error;\n  }\n};\nexport const createPattern = async (name, basePattern, overrides = []) => {\n  try {\n    const response = await axios.post(PATTERNS_API_URL, {\n      name,\n      basePattern,\n      overrides\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error creating pattern:', error);\n    throw error;\n  }\n};\nexport const updatePattern = async (id, name, basePattern, overrides = []) => {\n  try {\n    const response = await axios.put(`${PATTERNS_API_URL}/${id}`, {\n      name,\n      basePattern,\n      overrides\n    });\n    return response.data;\n  } catch (error) {\n    console.error(`Error updating pattern ${id}:`, error);\n    throw error;\n  }\n};\nexport const copyPattern = async (id, newName, newYear) => {\n  try {\n    const response = await axios.post(`${PATTERNS_API_URL}/${id}/copy`, {\n      newName,\n      newYear\n    });\n    return response.data;\n  } catch (error) {\n    console.error(`Error copying pattern ${id}:`, error);\n    throw error;\n  }\n};\nexport const deletePattern = async id => {\n  try {\n    await axios.delete(`${PATTERNS_API_URL}/${id}`);\n    return {\n      message: 'Pattern deleted successfully'\n    };\n  } catch (error) {\n    console.error(`Error deleting pattern ${id}:`, error);\n    throw error;\n  }\n};\n\n// ===== FUNCIONES PARA SISTEMA DUAL DE CALENDARIOS =====\n\n// ***** CALENDARIO REAL *****\nexport const getAllRealDays = async () => {\n  try {\n    const response = await axios.get(`${API_URL}/real`);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all real days:', error);\n    throw error;\n  }\n};\nexport const getRealDayByDate = async date => {\n  try {\n    const response = await axios.get(`${API_URL}/real/${date}`);\n    return response.data;\n  } catch (error) {\n    if (error.response && error.response.status === 404) {\n      return null;\n    }\n    console.error(`Error fetching real day for ${date}:`, error);\n    throw error;\n  }\n};\nexport const createOrUpdateRealDay = async (date, type, hours, description, shiftId = null) => {\n  try {\n    const response = await axios.post(`${API_URL}/real`, {\n      date,\n      type,\n      hours,\n      description,\n      shiftId\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error creating or updating real day:', error);\n    throw error;\n  }\n};\n\n// ***** CALENDARIO PLANIFICADO *****\nexport const getAllPlannedDays = async () => {\n  try {\n    const response = await axios.get(`${API_URL}/planned`);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all planned days:', error);\n    throw error;\n  }\n};\nexport const getPlannedDayByDate = async date => {\n  try {\n    const response = await axios.get(`${API_URL}/planned/${date}`);\n    return response.data;\n  } catch (error) {\n    if (error.response && error.response.status === 404) {\n      return null;\n    }\n    console.error(`Error fetching planned day for ${date}:`, error);\n    throw error;\n  }\n};\nexport const clearPlannedCalendar = async () => {\n  try {\n    const response = await axios.delete(`${API_URL}/planned`);\n    return response.data;\n  } catch (error) {\n    console.error('Error clearing planned calendar:', error);\n    throw error;\n  }\n};\n\n// ***** ANÁLISIS Y COMPARACIÓN *****\nexport const compareCalendars = async (startDate, endDate) => {\n  try {\n    const response = await axios.get(`${ANALYSIS_API_URL}/compare`, {\n      params: {\n        startDate,\n        endDate\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error comparing calendars:', error);\n    throw error;\n  }\n};\nexport const getComplianceStats = async (startDate, endDate) => {\n  try {\n    const response = await axios.get(`${ANALYSIS_API_URL}/compliance`, {\n      params: {\n        startDate,\n        endDate\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching compliance stats:', error);\n    throw error;\n  }\n};\nexport const getVarianceAnalysis = async (startDate, endDate) => {\n  try {\n    const response = await axios.get(`${ANALYSIS_API_URL}/variance`, {\n      params: {\n        startDate,\n        endDate\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching variance analysis:', error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["axios", "API_URL", "SHIFTS_API_URL", "PATTERNS_API_URL", "ANALYSIS_API_URL", "getAllDays", "response", "get", "data", "error", "console", "getDayByDate", "date", "status", "createOrUpdateDay", "type", "hours", "description", "post", "getAllShifts", "getShiftById", "shiftId", "applyShiftToDate", "applyShiftToMultipleDates", "dates", "getShiftStatistics", "suggestShiftsForDate", "applyPattern", "startDate", "endDate", "patternId", "applyPatternArray", "pattern", "getAllPatterns", "getPatternById", "id", "createPattern", "name", "basePattern", "overrides", "updatePattern", "put", "copyPattern", "newName", "newYear", "deletePattern", "delete", "message", "getAllRealDays", "getRealDayByDate", "createOrUpdateRealDay", "getAllPlannedDays", "getPlannedDayByDate", "clearPlannedCalendar", "compareCalendars", "params", "getComplianceStats", "getVarianceAnalysis"], "sources": ["D:/Proyectos Python/Horario/client/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:5000/api/days';\nconst SHIFTS_API_URL = 'http://localhost:5000/api/shifts';\nconst PATTERNS_API_URL = 'http://localhost:5000/api/patterns';\nconst ANALYSIS_API_URL = 'http://localhost:5000/api/analysis';\n\nexport const getAllDays = async () => {\n  try {\n    const response = await axios.get(API_URL);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all days:', error);\n    throw error;\n  }\n};\n\nexport const getDayByDate = async (date) => {\n  try {\n    const response = await axios.get(`${API_URL}/${date}`);\n    return response.data;\n  } catch (error) {\n    if (error.response && error.response.status === 404) {\n      return null; // Day not found, return null instead of throwing error\n    }\n    console.error(`Error fetching day for ${date}:`, error);\n    throw error;\n  }\n};\n\nexport const createOrUpdateDay = async (date, type, hours, description) => {\n  try {\n    const response = await axios.post(API_URL, { date, type, hours, description });\n    return response.data;\n  } catch (error) {\n    console.error('Error creating or updating day:', error);\n    throw error;\n  }\n};\n\n// ===== FUNCIONES PARA TURNOS PREDEFINIDOS =====\n\n/**\n * Obtiene todos los turnos predefinidos\n */\nexport const getAllShifts = async () => {\n  try {\n    const response = await axios.get(SHIFTS_API_URL);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all shifts:', error);\n    throw error;\n  }\n};\n\n/**\n * Obtiene un turno específico por ID\n */\nexport const getShiftById = async (shiftId) => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/${shiftId}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error fetching shift ${shiftId}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un turno predefinido a una fecha específica\n */\nexport const applyShiftToDate = async (date, shiftId, description = '') => {\n  try {\n    const response = await axios.post(`${SHIFTS_API_URL}/apply`, {\n      date,\n      shiftId,\n      description\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying shift to date:', error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un turno a múltiples fechas\n */\nexport const applyShiftToMultipleDates = async (dates, shiftId, description = '') => {\n  try {\n    const response = await axios.post(`${SHIFTS_API_URL}/apply-multiple`, {\n      dates,\n      shiftId,\n      description\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying shift to multiple dates:', error);\n    throw error;\n  }\n};\n\n/**\n * Obtiene estadísticas de turnos aplicados\n */\nexport const getShiftStatistics = async () => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/statistics`);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching shift statistics:', error);\n    throw error;\n  }\n};\n\n/**\n * Sugiere turnos apropiados para una fecha\n */\nexport const suggestShiftsForDate = async (date) => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/suggest/${date}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error suggesting shifts for date ${date}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un patrón de días a un rango de fechas usando un ID de patrón.\n */\nexport const applyPattern = async (startDate, endDate, patternId) => {\n  try {\n    const response = await axios.post(`${API_URL}/apply-pattern`, { startDate, endDate, patternId });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying pattern:', error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un patrón de días a un rango de fechas usando un array de patrón (legacy).\n */\nexport const applyPatternArray = async (startDate, endDate, pattern) => {\n  try {\n    const response = await axios.post(`${API_URL}/apply-pattern`, { startDate, endDate, pattern });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying pattern array:', error);\n    throw error;\n  }\n};\n\n// ===== FUNCIONES PARA PATRONES =====\n\nexport const getAllPatterns = async () => {\n  try {\n    const response = await axios.get(PATTERNS_API_URL);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all patterns:', error);\n    throw error;\n  }\n};\n\nexport const getPatternById = async (id) => {\n  try {\n    const response = await axios.get(`${PATTERNS_API_URL}/${id}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error fetching pattern ${id}:`, error);\n    throw error;\n  }\n};\n\nexport const createPattern = async (name, basePattern, overrides = []) => {\n  try {\n    const response = await axios.post(PATTERNS_API_URL, { name, basePattern, overrides });\n    return response.data;\n  } catch (error) {\n    console.error('Error creating pattern:', error);\n    throw error;\n  }\n};\n\nexport const updatePattern = async (id, name, basePattern, overrides = []) => {\n  try {\n    const response = await axios.put(`${PATTERNS_API_URL}/${id}`, { name, basePattern, overrides });\n    return response.data;\n  } catch (error) {\n    console.error(`Error updating pattern ${id}:`, error);\n    throw error;\n  }\n};\n\nexport const copyPattern = async (id, newName, newYear) => {\n  try {\n    const response = await axios.post(`${PATTERNS_API_URL}/${id}/copy`, { newName, newYear });\n    return response.data;\n  } catch (error) {\n    console.error(`Error copying pattern ${id}:`, error);\n    throw error;\n  }\n};\n\nexport const deletePattern = async (id) => {\n  try {\n    await axios.delete(`${PATTERNS_API_URL}/${id}`);\n    return { message: 'Pattern deleted successfully' };\n  } catch (error) {\n    console.error(`Error deleting pattern ${id}:`, error);\n    throw error;\n  }\n};\n\n// ===== FUNCIONES PARA SISTEMA DUAL DE CALENDARIOS =====\n\n// ***** CALENDARIO REAL *****\nexport const getAllRealDays = async () => {\n  try {\n    const response = await axios.get(`${API_URL}/real`);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all real days:', error);\n    throw error;\n  }\n};\n\nexport const getRealDayByDate = async (date) => {\n  try {\n    const response = await axios.get(`${API_URL}/real/${date}`);\n    return response.data;\n  } catch (error) {\n    if (error.response && error.response.status === 404) {\n      return null;\n    }\n    console.error(`Error fetching real day for ${date}:`, error);\n    throw error;\n  }\n};\n\nexport const createOrUpdateRealDay = async (date, type, hours, description, shiftId = null) => {\n  try {\n    const response = await axios.post(`${API_URL}/real`, { date, type, hours, description, shiftId });\n    return response.data;\n  } catch (error) {\n    console.error('Error creating or updating real day:', error);\n    throw error;\n  }\n};\n\n// ***** CALENDARIO PLANIFICADO *****\nexport const getAllPlannedDays = async () => {\n  try {\n    const response = await axios.get(`${API_URL}/planned`);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all planned days:', error);\n    throw error;\n  }\n};\n\nexport const getPlannedDayByDate = async (date) => {\n  try {\n    const response = await axios.get(`${API_URL}/planned/${date}`);\n    return response.data;\n  } catch (error) {\n    if (error.response && error.response.status === 404) {\n      return null;\n    }\n    console.error(`Error fetching planned day for ${date}:`, error);\n    throw error;\n  }\n};\n\nexport const clearPlannedCalendar = async () => {\n  try {\n    const response = await axios.delete(`${API_URL}/planned`);\n    return response.data;\n  } catch (error) {\n    console.error('Error clearing planned calendar:', error);\n    throw error;\n  }\n};\n\n// ***** ANÁLISIS Y COMPARACIÓN *****\nexport const compareCalendars = async (startDate, endDate) => {\n  try {\n    const response = await axios.get(`${ANALYSIS_API_URL}/compare`, {\n      params: { startDate, endDate }\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error comparing calendars:', error);\n    throw error;\n  }\n};\n\nexport const getComplianceStats = async (startDate, endDate) => {\n  try {\n    const response = await axios.get(`${ANALYSIS_API_URL}/compliance`, {\n      params: { startDate, endDate }\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching compliance stats:', error);\n    throw error;\n  }\n};\n\nexport const getVarianceAnalysis = async (startDate, endDate) => {\n  try {\n    const response = await axios.get(`${ANALYSIS_API_URL}/variance`, {\n      params: { startDate, endDate }\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching variance analysis:', error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,gCAAgC;AAChD,MAAMC,cAAc,GAAG,kCAAkC;AACzD,MAAMC,gBAAgB,GAAG,oCAAoC;AAC7D,MAAMC,gBAAgB,GAAG,oCAAoC;AAE7D,OAAO,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;EACpC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAACN,OAAO,CAAC;IACzC,OAAOK,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAME,YAAY,GAAG,MAAOC,IAAI,IAAK;EAC1C,IAAI;IACF,MAAMN,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,OAAO,IAAIW,IAAI,EAAE,CAAC;IACtD,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,IAAIA,KAAK,CAACH,QAAQ,IAAIG,KAAK,CAACH,QAAQ,CAACO,MAAM,KAAK,GAAG,EAAE;MACnD,OAAO,IAAI,CAAC,CAAC;IACf;IACAH,OAAO,CAACD,KAAK,CAAC,0BAA0BG,IAAI,GAAG,EAAEH,KAAK,CAAC;IACvD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMK,iBAAiB,GAAG,MAAAA,CAAOF,IAAI,EAAEG,IAAI,EAAEC,KAAK,EAAEC,WAAW,KAAK;EACzE,IAAI;IACF,MAAMX,QAAQ,GAAG,MAAMN,KAAK,CAACkB,IAAI,CAACjB,OAAO,EAAE;MAAEW,IAAI;MAAEG,IAAI;MAAEC,KAAK;MAAEC;IAAY,CAAC,CAAC;IAC9E,OAAOX,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACvD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;;AAEA;AACA;AACA;AACA,OAAO,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;EACtC,IAAI;IACF,MAAMb,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAACL,cAAc,CAAC;IAChD,OAAOI,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMW,YAAY,GAAG,MAAOC,OAAO,IAAK;EAC7C,IAAI;IACF,MAAMf,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGL,cAAc,IAAImB,OAAO,EAAE,CAAC;IAChE,OAAOf,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,wBAAwBY,OAAO,GAAG,EAAEZ,KAAK,CAAC;IACxD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMa,gBAAgB,GAAG,MAAAA,CAAOV,IAAI,EAAES,OAAO,EAAEJ,WAAW,GAAG,EAAE,KAAK;EACzE,IAAI;IACF,MAAMX,QAAQ,GAAG,MAAMN,KAAK,CAACkB,IAAI,CAAC,GAAGhB,cAAc,QAAQ,EAAE;MAC3DU,IAAI;MACJS,OAAO;MACPJ;IACF,CAAC,CAAC;IACF,OAAOX,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMc,yBAAyB,GAAG,MAAAA,CAAOC,KAAK,EAAEH,OAAO,EAAEJ,WAAW,GAAG,EAAE,KAAK;EACnF,IAAI;IACF,MAAMX,QAAQ,GAAG,MAAMN,KAAK,CAACkB,IAAI,CAAC,GAAGhB,cAAc,iBAAiB,EAAE;MACpEsB,KAAK;MACLH,OAAO;MACPJ;IACF,CAAC,CAAC;IACF,OAAOX,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IAC/D,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMgB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;EAC5C,IAAI;IACF,MAAMnB,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGL,cAAc,aAAa,CAAC;IAChE,OAAOI,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMiB,oBAAoB,GAAG,MAAOd,IAAI,IAAK;EAClD,IAAI;IACF,MAAMN,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGL,cAAc,YAAYU,IAAI,EAAE,CAAC;IACrE,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,oCAAoCG,IAAI,GAAG,EAAEH,KAAK,CAAC;IACjE,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMkB,YAAY,GAAG,MAAAA,CAAOC,SAAS,EAAEC,OAAO,EAAEC,SAAS,KAAK;EACnE,IAAI;IACF,MAAMxB,QAAQ,GAAG,MAAMN,KAAK,CAACkB,IAAI,CAAC,GAAGjB,OAAO,gBAAgB,EAAE;MAAE2B,SAAS;MAAEC,OAAO;MAAEC;IAAU,CAAC,CAAC;IAChG,OAAOxB,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMsB,iBAAiB,GAAG,MAAAA,CAAOH,SAAS,EAAEC,OAAO,EAAEG,OAAO,KAAK;EACtE,IAAI;IACF,MAAM1B,QAAQ,GAAG,MAAMN,KAAK,CAACkB,IAAI,CAAC,GAAGjB,OAAO,gBAAgB,EAAE;MAAE2B,SAAS;MAAEC,OAAO;MAAEG;IAAQ,CAAC,CAAC;IAC9F,OAAO1B,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;;AAEA,OAAO,MAAMwB,cAAc,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAM3B,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAACJ,gBAAgB,CAAC;IAClD,OAAOG,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMyB,cAAc,GAAG,MAAOC,EAAE,IAAK;EAC1C,IAAI;IACF,MAAM7B,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGJ,gBAAgB,IAAIgC,EAAE,EAAE,CAAC;IAC7D,OAAO7B,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B0B,EAAE,GAAG,EAAE1B,KAAK,CAAC;IACrD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAM2B,aAAa,GAAG,MAAAA,CAAOC,IAAI,EAAEC,WAAW,EAAEC,SAAS,GAAG,EAAE,KAAK;EACxE,IAAI;IACF,MAAMjC,QAAQ,GAAG,MAAMN,KAAK,CAACkB,IAAI,CAACf,gBAAgB,EAAE;MAAEkC,IAAI;MAAEC,WAAW;MAAEC;IAAU,CAAC,CAAC;IACrF,OAAOjC,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAM+B,aAAa,GAAG,MAAAA,CAAOL,EAAE,EAAEE,IAAI,EAAEC,WAAW,EAAEC,SAAS,GAAG,EAAE,KAAK;EAC5E,IAAI;IACF,MAAMjC,QAAQ,GAAG,MAAMN,KAAK,CAACyC,GAAG,CAAC,GAAGtC,gBAAgB,IAAIgC,EAAE,EAAE,EAAE;MAAEE,IAAI;MAAEC,WAAW;MAAEC;IAAU,CAAC,CAAC;IAC/F,OAAOjC,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B0B,EAAE,GAAG,EAAE1B,KAAK,CAAC;IACrD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMiC,WAAW,GAAG,MAAAA,CAAOP,EAAE,EAAEQ,OAAO,EAAEC,OAAO,KAAK;EACzD,IAAI;IACF,MAAMtC,QAAQ,GAAG,MAAMN,KAAK,CAACkB,IAAI,CAAC,GAAGf,gBAAgB,IAAIgC,EAAE,OAAO,EAAE;MAAEQ,OAAO;MAAEC;IAAQ,CAAC,CAAC;IACzF,OAAOtC,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB0B,EAAE,GAAG,EAAE1B,KAAK,CAAC;IACpD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMoC,aAAa,GAAG,MAAOV,EAAE,IAAK;EACzC,IAAI;IACF,MAAMnC,KAAK,CAAC8C,MAAM,CAAC,GAAG3C,gBAAgB,IAAIgC,EAAE,EAAE,CAAC;IAC/C,OAAO;MAAEY,OAAO,EAAE;IAA+B,CAAC;EACpD,CAAC,CAAC,OAAOtC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B0B,EAAE,GAAG,EAAE1B,KAAK,CAAC;IACrD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMuC,cAAc,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAM1C,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,OAAO,OAAO,CAAC;IACnD,OAAOK,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMwC,gBAAgB,GAAG,MAAOrC,IAAI,IAAK;EAC9C,IAAI;IACF,MAAMN,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,OAAO,SAASW,IAAI,EAAE,CAAC;IAC3D,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,IAAIA,KAAK,CAACH,QAAQ,IAAIG,KAAK,CAACH,QAAQ,CAACO,MAAM,KAAK,GAAG,EAAE;MACnD,OAAO,IAAI;IACb;IACAH,OAAO,CAACD,KAAK,CAAC,+BAA+BG,IAAI,GAAG,EAAEH,KAAK,CAAC;IAC5D,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMyC,qBAAqB,GAAG,MAAAA,CAAOtC,IAAI,EAAEG,IAAI,EAAEC,KAAK,EAAEC,WAAW,EAAEI,OAAO,GAAG,IAAI,KAAK;EAC7F,IAAI;IACF,MAAMf,QAAQ,GAAG,MAAMN,KAAK,CAACkB,IAAI,CAAC,GAAGjB,OAAO,OAAO,EAAE;MAAEW,IAAI;MAAEG,IAAI;MAAEC,KAAK;MAAEC,WAAW;MAAEI;IAAQ,CAAC,CAAC;IACjG,OAAOf,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC5D,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAM0C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;EAC3C,IAAI;IACF,MAAM7C,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,OAAO,UAAU,CAAC;IACtD,OAAOK,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAM2C,mBAAmB,GAAG,MAAOxC,IAAI,IAAK;EACjD,IAAI;IACF,MAAMN,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGN,OAAO,YAAYW,IAAI,EAAE,CAAC;IAC9D,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,IAAIA,KAAK,CAACH,QAAQ,IAAIG,KAAK,CAACH,QAAQ,CAACO,MAAM,KAAK,GAAG,EAAE;MACnD,OAAO,IAAI;IACb;IACAH,OAAO,CAACD,KAAK,CAAC,kCAAkCG,IAAI,GAAG,EAAEH,KAAK,CAAC;IAC/D,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAM4C,oBAAoB,GAAG,MAAAA,CAAA,KAAY;EAC9C,IAAI;IACF,MAAM/C,QAAQ,GAAG,MAAMN,KAAK,CAAC8C,MAAM,CAAC,GAAG7C,OAAO,UAAU,CAAC;IACzD,OAAOK,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAM6C,gBAAgB,GAAG,MAAAA,CAAO1B,SAAS,EAAEC,OAAO,KAAK;EAC5D,IAAI;IACF,MAAMvB,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGH,gBAAgB,UAAU,EAAE;MAC9DmD,MAAM,EAAE;QAAE3B,SAAS;QAAEC;MAAQ;IAC/B,CAAC,CAAC;IACF,OAAOvB,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAM+C,kBAAkB,GAAG,MAAAA,CAAO5B,SAAS,EAAEC,OAAO,KAAK;EAC9D,IAAI;IACF,MAAMvB,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGH,gBAAgB,aAAa,EAAE;MACjEmD,MAAM,EAAE;QAAE3B,SAAS;QAAEC;MAAQ;IAC/B,CAAC,CAAC;IACF,OAAOvB,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMgD,mBAAmB,GAAG,MAAAA,CAAO7B,SAAS,EAAEC,OAAO,KAAK;EAC/D,IAAI;IACF,MAAMvB,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGH,gBAAgB,WAAW,EAAE;MAC/DmD,MAAM,EAAE;QAAE3B,SAAS;QAAEC;MAAQ;IAC/B,CAAC,CAAC;IACF,OAAOvB,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IACzD,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}