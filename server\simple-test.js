console.log('Starting simple test...');

try {
    console.log('1. Testing dataHandler...');
    const { readData } = require('./src/data/dataHandler');
    const data = readData();
    console.log('Data loaded successfully:', Object.keys(data));
    
    console.log('2. Testing patternService...');
    const patternService = require('./src/services/patternService');
    console.log('PatternService loaded successfully');
    
    console.log('3. Getting all patterns...');
    const patterns = patternService.getAllPatterns();
    console.log('Patterns:', patterns);
    
} catch (error) {
    console.error('Error in simple test:', error.message);
    console.error('Stack:', error.stack);
}
