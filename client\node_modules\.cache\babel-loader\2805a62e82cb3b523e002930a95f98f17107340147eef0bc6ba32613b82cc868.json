{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\components\\\\NavbarComponent.js\";\nimport React from 'react';\nimport { Navbar, Nav, Container } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction NavbarComponent() {\n  return /*#__PURE__*/_jsxDEV(Navbar, {\n    bg: \"dark\",\n    variant: \"dark\",\n    expand: \"lg\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(Navbar.Brand, {\n        as: Link,\n        to: \"/\",\n        children: \"Control Horario\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Navbar.Toggle, {\n        \"aria-controls\": \"basic-navbar-nav\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Navbar.Collapse, {\n        id: \"basic-navbar-nav\",\n        children: /*#__PURE__*/_jsxDEV(Nav, {\n          className: \"me-auto\",\n          children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/\",\n            children: \"Calendario\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/reports\",\n            children: \"Informes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/settings\",\n            children: \"Configuraci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = NavbarComponent;\nexport default NavbarComponent;\nvar _c;\n$RefreshReg$(_c, \"NavbarComponent\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Nav", "Container", "Link", "jsxDEV", "_jsxDEV", "NavbarComponent", "bg", "variant", "expand", "children", "Brand", "as", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Toggle", "Collapse", "id", "className", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/components/NavbarComponent.js"], "sourcesContent": ["import React from 'react';\nimport { Navbar, Nav, Container } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\n\nfunction NavbarComponent() {\n  return (\n    <Navbar bg=\"dark\" variant=\"dark\" expand=\"lg\">\n      <Container>\n        <Navbar.Brand as={Link} to=\"/\">Control Horario</Navbar.Brand>\n        <Navbar.Toggle aria-controls=\"basic-navbar-nav\" />\n        <Navbar.Collapse id=\"basic-navbar-nav\">\n          <Nav className=\"me-auto\">\n            <Nav.Link as={Link} to=\"/\">Calendario</Nav.Link>\n            <Nav.Link as={Link} to=\"/reports\">Informes</Nav.Link>\n            <Nav.Link as={Link} to=\"/settings\">Configuración</Nav.Link>\n          </Nav>\n        </Navbar.Collapse>\n      </Container>\n    </Navbar>\n  );\n}\n\nexport default NavbarComponent;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,GAAG,EAAEC,SAAS,QAAQ,iBAAiB;AACxD,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,eAAeA,CAAA,EAAG;EACzB,oBACED,OAAA,CAACL,MAAM;IAACO,EAAE,EAAC,MAAM;IAACC,OAAO,EAAC,MAAM;IAACC,MAAM,EAAC,IAAI;IAAAC,QAAA,eAC1CL,OAAA,CAACH,SAAS;MAAAQ,QAAA,gBACRL,OAAA,CAACL,MAAM,CAACW,KAAK;QAACC,EAAE,EAAET,IAAK;QAACU,EAAE,EAAC,GAAG;QAAAH,QAAA,EAAC;MAAe;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAC7DZ,OAAA,CAACL,MAAM,CAACkB,MAAM;QAAC,iBAAc;MAAkB;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClDZ,OAAA,CAACL,MAAM,CAACmB,QAAQ;QAACC,EAAE,EAAC,kBAAkB;QAAAV,QAAA,eACpCL,OAAA,CAACJ,GAAG;UAACoB,SAAS,EAAC,SAAS;UAAAX,QAAA,gBACtBL,OAAA,CAACJ,GAAG,CAACE,IAAI;YAACS,EAAE,EAAET,IAAK;YAACU,EAAE,EAAC,GAAG;YAAAH,QAAA,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAChDZ,OAAA,CAACJ,GAAG,CAACE,IAAI;YAACS,EAAE,EAAET,IAAK;YAACU,EAAE,EAAC,UAAU;YAAAH,QAAA,EAAC;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACrDZ,OAAA,CAACJ,GAAG,CAACE,IAAI;YAACS,EAAE,EAAET,IAAK;YAACU,EAAE,EAAC,WAAW;YAAAH,QAAA,EAAC;UAAa;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEb;AAACK,EAAA,GAhBQhB,eAAe;AAkBxB,eAAeA,eAAe;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}