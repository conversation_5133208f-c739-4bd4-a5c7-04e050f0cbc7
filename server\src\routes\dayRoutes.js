const express = require('express');
const dayController = require('../controllers/dayController');
const router = express.Router();

// Rutas para calendario REAL
router.get('/real', dayController.getAllRealDays);
router.post('/real', dayController.createOrUpdateRealDay);
router.get('/real/:date', dayController.getRealDayByDate);

// Rutas para calendario PLANIFICADO
router.get('/planned', dayController.getAllPlannedDays);
router.get('/planned/:date', dayController.getPlannedDayByDate);
router.delete('/planned', dayController.clearPlannedCalendar);

// Aplicación de patrones (genera calendario planificado)
router.post('/apply-pattern', dayController.applyPatternToDates);



module.exports = router;
