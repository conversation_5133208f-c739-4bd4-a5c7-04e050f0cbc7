import React, { useState, useEffect } from 'react';
import { Row, Col, Button, Alert } from 'react-bootstrap';
import { getAllRealDays } from '../services/api';

function RealCalendarView({ onDateSelect, selectedDate, onCalendarUpdate }) {
  const [realDays, setRealDays] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    loadRealDays();
  }, []);

  const loadRealDays = async () => {
    try {
      setLoading(true);
      const days = await getAllRealDays();
      setRealDays(days);
      setError('');
    } catch (err) {
      console.error('Error loading real days:', err);
      setError('Error cargando días reales: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const getTotalHours = () => {
    return realDays.reduce((total, day) => total + (day.hours || 0), 0);
  };

  return (
    <div>
      <Row className="mb-3">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <h4>Calendario de Trabajo Real</h4>
            <div>
              <Button
                variant="primary"
                onClick={() => alert('Funcionalidad en desarrollo')}
              >
                ➕ Registrar Día
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {error && (
        <Row className="mb-3">
          <Col>
            <Alert variant="danger" onClose={() => setError('')} dismissible>
              {error}
            </Alert>
          </Col>
        </Row>
      )}

      <Row className="mb-3">
        <Col md={8}>
          <div className="calendar-container">
            <p>Calendario visual estará disponible pronto</p>
            {loading && <p>Cargando días reales...</p>}
          </div>
        </Col>
        <Col md={4}>
          <div className="calendar-stats">
            <h5>Estadísticas</h5>
            <div className="stat-item">
              <strong>Total de días registrados:</strong> {realDays.length}
            </div>
            <div className="stat-item">
              <strong>Total de horas:</strong> {getTotalHours().toFixed(1)}h
            </div>
          </div>

          {selectedDate && (
            <div className="selected-date-info mt-3">
              <h6>Fecha seleccionada:</h6>
              <p>{selectedDate.toLocaleDateString('es-ES', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}</p>
            </div>
          )}
        </Col>
      </Row>
    </div>
  );
}

export default RealCalendarView;
