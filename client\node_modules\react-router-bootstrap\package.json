{"name": "react-router-bootstrap", "version": "0.26.3", "description": "Integration between React Router and React-Bootstrap", "main": "./index.js", "repository": {"type": "git", "url": "**************:react-bootstrap/react-router-bootstrap.git"}, "keywords": ["react", "react-router", "react-bootstrap"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/react-bootstrap/react-router-bootstrap/issues"}, "homepage": "https://github.com/react-bootstrap/react-router-bootstrap", "peerDependencies": {"react": ">=16.13.1", "react-router-dom": ">=6.0.0"}, "prettier": {"singleQuote": true, "trailingComma": "all"}, "dependencies": {"prop-types": "^15.7.2"}, "jest": {"setupFilesAfterEnv": ["<rootDir>test/setupTests.js"], "transform": {"^.+\\.js$": "babel-jest"}}, "publishConfig": {"directory": "lib"}, "release": {"conventionalCommits": true}}