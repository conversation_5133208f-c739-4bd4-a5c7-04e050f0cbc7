{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\SettingsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Form, Alert, ListGroup, Modal } from 'react-bootstrap';\nimport { getAllPatterns, createPattern, updatePattern, deletePattern, getAllShifts, copyPattern } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SettingsPage() {\n  _s();\n  const [patterns, setPatterns] = useState([]);\n  const [currentPattern, setCurrentPattern] = useState(null);\n  const [showPatternForm, setShowPatternForm] = useState(false);\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showCopyModal, setShowCopyModal] = useState(false);\n  const [patternToCopy, setPatternToCopy] = useState(null);\n  const [newPatternName, setNewPatternName] = useState('');\n  const [newPatternYear, setNewPatternYear] = useState(new Date().getFullYear() + 1);\n  useEffect(() => {\n    loadPatterns();\n    loadAvailableShifts();\n  }, []);\n  const loadPatterns = async () => {\n    setLoading(true);\n    try {\n      const data = await getAllPatterns();\n      setPatterns(data);\n    } catch (err) {\n      setError('Error cargando patrones.');\n      console.error('Error loading patterns:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (err) {\n      console.error('Error loading shifts:', err);\n    }\n  };\n  const handleAddPattern = () => {\n    setCurrentPattern({\n      name: '',\n      basePattern: Array.from({\n        length: 7\n      }, () => ({\n        type: '',\n        hours: 0,\n        description: '',\n        shiftId: null\n      })),\n      overrides: []\n    });\n    setShowPatternForm(true);\n  };\n  const handleEditPattern = pattern => {\n    setCurrentPattern({\n      ...pattern\n    });\n    setShowPatternForm(true);\n  };\n  const handleDeletePattern = async id => {\n    if (window.confirm('¿Estás seguro de que quieres eliminar este patrón?')) {\n      setLoading(true);\n      try {\n        await deletePattern(id);\n        await loadPatterns();\n      } catch (err) {\n        setError('Error eliminando patrón.');\n        console.error('Error deleting pattern:', err);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  const handleSavePattern = async () => {\n    if (!currentPattern || !currentPattern.name || !currentPattern.basePattern) {\n      setError('El nombre del patrón y el patrón base son requeridos.');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      if (currentPattern.id) {\n        await updatePattern(currentPattern.id, currentPattern.name, currentPattern.basePattern, currentPattern.overrides);\n      } else {\n        await createPattern(currentPattern.name, currentPattern.basePattern, currentPattern.overrides);\n      }\n      setShowPatternForm(false);\n      setCurrentPattern(null);\n      await loadPatterns();\n    } catch (err) {\n      setError('Error guardando patrón.');\n      console.error('Error saving pattern:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCancelEdit = () => {\n    setShowPatternForm(false);\n    setCurrentPattern(null);\n    setError('');\n  };\n  const handleBasePatternDayChange = (index, field, value) => {\n    const newBasePattern = [...currentPattern.basePattern];\n    newBasePattern[index][field] = value;\n    if (field === 'shiftId' && value) {\n      const shift = availableShifts.find(s => s.id === value);\n      if (shift) {\n        newBasePattern[index].type = 'worked';\n        newBasePattern[index].hours = shift.totalHours;\n        newBasePattern[index].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;\n      }\n    } else if (field === 'shiftId' && !value) {\n      newBasePattern[index].description = '';\n    }\n    setCurrentPattern({\n      ...currentPattern,\n      basePattern: newBasePattern\n    });\n  };\n  const handleOverrideChange = (overrideIndex, field, value) => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex][field] = value;\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: newOverrides\n    });\n  };\n  const handleOverridePatternDayChange = (overrideIndex, dayIndex, field, value) => {\n    const newOverrides = [...currentPattern.overrides];\n    const newOverridePattern = [...newOverrides[overrideIndex].overridePattern];\n    newOverridePattern[dayIndex][field] = value;\n    if (field === 'shiftId' && value) {\n      const shift = availableShifts.find(s => s.id === value);\n      if (shift) {\n        newOverridePattern[dayIndex].type = 'worked';\n        newOverridePattern[dayIndex].hours = shift.totalHours;\n        newOverridePattern[dayIndex].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;\n      }\n    } else if (field === 'shiftId' && !value) {\n      newOverridePattern[dayIndex].description = '';\n    }\n    newOverrides[overrideIndex].overridePattern = newOverridePattern;\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: newOverrides\n    });\n  };\n  const handleAddOverride = () => {\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: [...currentPattern.overrides, {\n        startDate: '',\n        endDate: '',\n        overridePattern: [{\n          type: '',\n          hours: 0,\n          description: '',\n          shiftId: null\n        }]\n      }]\n    });\n  };\n  const handleRemoveOverride = overrideIndex => {\n    const newOverrides = currentPattern.overrides.filter((_, i) => i !== overrideIndex);\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: newOverrides\n    });\n  };\n  const handleAddOverridePatternDay = overrideIndex => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex].overridePattern.push({\n      type: '',\n      hours: 0,\n      description: '',\n      shiftId: null\n    });\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: newOverrides\n    });\n  };\n  const handleRemoveOverridePatternDay = (overrideIndex, dayIndex) => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex].overridePattern = newOverrides[overrideIndex].overridePattern.filter((_, i) => i !== dayIndex);\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: newOverrides\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"my-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Configuraci\\xF3n de Patrones de Calendario\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleAddPattern,\n          className: \"mb-3\",\n          children: \"Crear Nuevo Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Cargando patrones...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this) : patterns.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No hay patrones definidos.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ListGroup, {\n          children: patterns.map(pattern => /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n            className: \"d-flex justify-content-between align-items-center\",\n            children: [pattern.name, /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"info\",\n                size: \"sm\",\n                className: \"me-2\",\n                onClick: () => handleEditPattern(pattern),\n                children: \"Editar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"secondary\",\n                size: \"sm\",\n                className: \"me-2\",\n                onClick: () => handleShowCopyModal(pattern),\n                children: \"Copiar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"danger\",\n                size: \"sm\",\n                onClick: () => handleDeletePattern(pattern.id),\n                children: \"Eliminar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 19\n            }, this)]\n          }, pattern.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPatternForm,\n      onHide: handleCancelEdit,\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: currentPattern && currentPattern.id ? 'Editar Patrón' : 'Crear Nuevo Patrón'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: currentPattern && /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Nombre del Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              value: currentPattern.name,\n              onChange: e => setCurrentPattern({\n                ...currentPattern,\n                name: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Patr\\xF3n Base (Semanal):\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this), currentPattern.basePattern.map((day, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border p-3 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: [\"D\\xEDa \", ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'][index]]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Tipo de D\\xEDa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: day.type,\n                onChange: e => handleBasePatternDayChange(index, 'type', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Seleccionar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"worked\",\n                  children: \"Trabajado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"holiday\",\n                  children: \"Vacaciones\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"permit\",\n                  children: \"Permiso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"negative\",\n                  children: \"C\\xF3mputo Negativo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Horas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"number\",\n                value: day.hours,\n                onChange: e => handleBasePatternDayChange(index, 'hours', parseFloat(e.target.value))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Turno Predefinido\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: day.shiftId || '',\n                onChange: e => handleBasePatternDayChange(index, 'shiftId', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ninguno\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 23\n                }, this), availableShifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: shift.id,\n                  children: [shift.name, \" - \", shift.startTime, \" a \", shift.endTime, \" (\", shift.totalHours, \"h)\"]\n                }, shift.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Descripci\\xF3n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                as: \"textarea\",\n                rows: 1,\n                value: day.description,\n                onChange: e => handleBasePatternDayChange(index, 'description', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 17\n          }, this)), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mt-4\",\n            children: \"Anulaciones (Overrides):\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this), currentPattern.overrides.map((override, overrideIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border p-3 mb-2 bg-light\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: [\"Anulaci\\xF3n \", overrideIndex + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  controlId: `overrideStartDate-${overrideIndex}`,\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Fecha de Inicio\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: override.startDate,\n                    onChange: e => handleOverrideChange(overrideIndex, 'startDate', e.target.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  controlId: `overrideEndDate-${overrideIndex}`,\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Fecha de Fin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: override.endDate,\n                    onChange: e => handleOverrideChange(overrideIndex, 'endDate', e.target.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"D\\xEDas de la Anulaci\\xF3n:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 19\n            }, this), override.overridePattern.map((day, dayIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border p-3 mb-2 ms-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: [\"D\\xEDa \", dayIndex + 1]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Tipo de D\\xEDa\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: day.type,\n                  onChange: e => handleOverridePatternDayChange(overrideIndex, dayIndex, 'type', e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Seleccionar\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"worked\",\n                    children: \"Trabajado\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"holiday\",\n                    children: \"Vacaciones\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"permit\",\n                    children: \"Permiso\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"negative\",\n                    children: \"C\\xF3mputo Negativo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Horas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  value: day.hours,\n                  onChange: e => handleOverridePatternDayChange(overrideIndex, dayIndex, 'hours', parseFloat(e.target.value))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Turno Predefinido\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: day.shiftId || '',\n                  onChange: e => handleOverridePatternDayChange(overrideIndex, dayIndex, 'shiftId', e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Ninguno\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 27\n                  }, this), availableShifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: shift.id,\n                    children: [shift.name, \" - \", shift.startTime, \" a \", shift.endTime, \" (\", shift.totalHours, \"h)\"]\n                  }, shift.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 29\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Descripci\\xF3n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  as: \"textarea\",\n                  rows: 1,\n                  value: day.description,\n                  onChange: e => handleOverridePatternDayChange(overrideIndex, dayIndex, 'description', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"danger\",\n                size: \"sm\",\n                onClick: () => handleRemoveOverridePatternDay(overrideIndex, dayIndex),\n                children: \"Eliminar D\\xEDa de Anulaci\\xF3n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 23\n              }, this)]\n            }, dayIndex, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 21\n            }, this)), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"secondary\",\n              onClick: () => handleAddOverridePatternDay(overrideIndex),\n              className: \"mt-3\",\n              children: \"A\\xF1adir D\\xEDa a Anulaci\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              onClick: () => handleRemoveOverride(overrideIndex),\n              className: \"mt-3 ms-2\",\n              children: \"Eliminar Anulaci\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 19\n            }, this)]\n          }, overrideIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 17\n          }, this)), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"info\",\n            onClick: handleAddOverride,\n            className: \"mt-3\",\n            children: \"A\\xF1adir Anulaci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: handleCancelEdit,\n          children: \"Cancelar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSavePattern,\n          disabled: loading,\n          children: \"Guardar Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 172,\n    columnNumber: 5\n  }, this);\n}\n_s(SettingsPage, \"lej1LqdI/m2pH0PDXuiFxsRVlFM=\");\n_c = SettingsPage;\nexport default SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "<PERSON><PERSON>", "Form", "<PERSON><PERSON>", "ListGroup", "Modal", "getAllPatterns", "createPattern", "updatePattern", "deletePattern", "getAllShifts", "copyPattern", "jsxDEV", "_jsxDEV", "SettingsPage", "_s", "patterns", "setPatterns", "currentPattern", "setCurrentPattern", "showPatternForm", "setShowPatternForm", "availableShifts", "setAvailableShifts", "loading", "setLoading", "error", "setError", "showCopyModal", "setShowCopyModal", "patternToCopy", "setPatternToCopy", "newPatternName", "setNewPatternName", "newPatternYear", "setNewPatternYear", "Date", "getFullYear", "loadPatterns", "loadAvailableShifts", "data", "err", "console", "shifts", "handleAddPattern", "name", "basePattern", "Array", "from", "length", "type", "hours", "description", "shiftId", "overrides", "handleEditPattern", "pattern", "handleDeletePattern", "id", "window", "confirm", "handleSavePattern", "handleCancelEdit", "handleBasePatternDayChange", "index", "field", "value", "newBasePattern", "shift", "find", "s", "totalHours", "startTime", "endTime", "breakMinutes", "handleOverrideChange", "overrideIndex", "newOverrides", "handleOverridePatternDayChange", "dayIndex", "newOverridePattern", "overridePattern", "handleAddOverride", "startDate", "endDate", "handleRemoveOverride", "filter", "_", "i", "handleAddOverridePatternDay", "push", "handleRemoveOverridePatternDay", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "map", "<PERSON><PERSON>", "size", "handleShowCopyModal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Group", "Label", "Control", "onChange", "e", "target", "day", "Select", "parseFloat", "as", "rows", "override", "controlId", "Footer", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/SettingsPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Form, Alert, ListGroup, Modal } from 'react-bootstrap';\nimport { getAllPatterns, createPattern, updatePattern, deletePattern, getAllShifts, copyPattern } from '../services/api';\n\nfunction SettingsPage() {\n  const [patterns, setPatterns] = useState([]);\n  const [currentPattern, setCurrentPattern] = useState(null);\n  const [showPatternForm, setShowPatternForm] = useState(false);\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showCopyModal, setShowCopyModal] = useState(false);\n  const [patternToCopy, setPatternToCopy] = useState(null);\n  const [newPatternName, setNewPatternName] = useState('');\n  const [newPatternYear, setNewPatternYear] = useState(new Date().getFullYear() + 1);\n\n  useEffect(() => {\n    loadPatterns();\n    loadAvailableShifts();\n  }, []);\n\n  const loadPatterns = async () => {\n    setLoading(true);\n    try {\n      const data = await getAllPatterns();\n      setPatterns(data);\n    } catch (err) {\n      setError('Error cargando patrones.');\n      console.error('Error loading patterns:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (err) {\n      console.error('Error loading shifts:', err);\n    }\n  };\n\n  const handleAddPattern = () => {\n    setCurrentPattern({\n      name: '',\n      basePattern: Array.from({ length: 7 }, () => ({ type: '', hours: 0, description: '', shiftId: null })),\n      overrides: [],\n    });\n    setShowPatternForm(true);\n  };\n\n  const handleEditPattern = (pattern) => {\n    setCurrentPattern({ ...pattern });\n    setShowPatternForm(true);\n  };\n\n  const handleDeletePattern = async (id) => {\n    if (window.confirm('¿Estás seguro de que quieres eliminar este patrón?')) {\n      setLoading(true);\n      try {\n        await deletePattern(id);\n        await loadPatterns();\n      } catch (err) {\n        setError('Error eliminando patrón.');\n        console.error('Error deleting pattern:', err);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  const handleSavePattern = async () => {\n    if (!currentPattern || !currentPattern.name || !currentPattern.basePattern) {\n      setError('El nombre del patrón y el patrón base son requeridos.');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    try {\n      if (currentPattern.id) {\n        await updatePattern(currentPattern.id, currentPattern.name, currentPattern.basePattern, currentPattern.overrides);\n      } else {\n        await createPattern(currentPattern.name, currentPattern.basePattern, currentPattern.overrides);\n      }\n      setShowPatternForm(false);\n      setCurrentPattern(null);\n      await loadPatterns();\n    } catch (err) {\n      setError('Error guardando patrón.');\n      console.error('Error saving pattern:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancelEdit = () => {\n    setShowPatternForm(false);\n    setCurrentPattern(null);\n    setError('');\n  };\n\n  const handleBasePatternDayChange = (index, field, value) => {\n    const newBasePattern = [...currentPattern.basePattern];\n    newBasePattern[index][field] = value;\n\n    if (field === 'shiftId' && value) {\n      const shift = availableShifts.find(s => s.id === value);\n      if (shift) {\n        newBasePattern[index].type = 'worked';\n        newBasePattern[index].hours = shift.totalHours;\n        newBasePattern[index].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;\n      }\n    } else if (field === 'shiftId' && !value) {\n      newBasePattern[index].description = '';\n    }\n    setCurrentPattern({ ...currentPattern, basePattern: newBasePattern });\n  };\n\n  const handleOverrideChange = (overrideIndex, field, value) => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex][field] = value;\n    setCurrentPattern({ ...currentPattern, overrides: newOverrides });\n  };\n\n  const handleOverridePatternDayChange = (overrideIndex, dayIndex, field, value) => {\n    const newOverrides = [...currentPattern.overrides];\n    const newOverridePattern = [...newOverrides[overrideIndex].overridePattern];\n    newOverridePattern[dayIndex][field] = value;\n\n    if (field === 'shiftId' && value) {\n      const shift = availableShifts.find(s => s.id === value);\n      if (shift) {\n        newOverridePattern[dayIndex].type = 'worked';\n        newOverridePattern[dayIndex].hours = shift.totalHours;\n        newOverridePattern[dayIndex].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;\n      }\n    } else if (field === 'shiftId' && !value) {\n      newOverridePattern[dayIndex].description = '';\n    }\n\n    newOverrides[overrideIndex].overridePattern = newOverridePattern;\n    setCurrentPattern({ ...currentPattern, overrides: newOverrides });\n  };\n\n  const handleAddOverride = () => {\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: [...currentPattern.overrides, { startDate: '', endDate: '', overridePattern: [{ type: '', hours: 0, description: '', shiftId: null }] }]\n    });\n  };\n\n  const handleRemoveOverride = (overrideIndex) => {\n    const newOverrides = currentPattern.overrides.filter((_, i) => i !== overrideIndex);\n    setCurrentPattern({ ...currentPattern, overrides: newOverrides });\n  };\n\n  const handleAddOverridePatternDay = (overrideIndex) => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex].overridePattern.push({ type: '', hours: 0, description: '', shiftId: null });\n    setCurrentPattern({ ...currentPattern, overrides: newOverrides });\n  };\n\n  const handleRemoveOverridePatternDay = (overrideIndex, dayIndex) => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex].overridePattern = newOverrides[overrideIndex].overridePattern.filter((_, i) => i !== dayIndex);\n    setCurrentPattern({ ...currentPattern, overrides: newOverrides });\n  };\n\n  return (\n    <Container>\n      <Row className=\"my-4\">\n        <Col>\n          <h2>Configuración de Patrones de Calendario</h2>\n          {error && <Alert variant=\"danger\">{error}</Alert>}\n          <Button onClick={handleAddPattern} className=\"mb-3\">Crear Nuevo Patrón</Button>\n\n          {loading ? (\n            <p>Cargando patrones...</p>\n          ) : patterns.length === 0 ? (\n            <p>No hay patrones definidos.</p>\n          ) : (\n            <ListGroup>\n              {patterns.map(pattern => (\n                <ListGroup.Item key={pattern.id} className=\"d-flex justify-content-between align-items-center\">\n                  {pattern.name}\n                  <div>\n                    <Button variant=\"info\" size=\"sm\" className=\"me-2\" onClick={() => handleEditPattern(pattern)}>Editar</Button>\n                    <Button variant=\"secondary\" size=\"sm\" className=\"me-2\" onClick={() => handleShowCopyModal(pattern)}>Copiar</Button>\n                    <Button variant=\"danger\" size=\"sm\" onClick={() => handleDeletePattern(pattern.id)}>Eliminar</Button>\n                  </div>\n                </ListGroup.Item>\n              ))}\n            </ListGroup>\n          )}\n        </Col>\n      </Row>\n\n      <Modal show={showPatternForm} onHide={handleCancelEdit} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>{currentPattern && currentPattern.id ? 'Editar Patrón' : 'Crear Nuevo Patrón'}</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {currentPattern && (\n            <Form>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Nombre del Patrón</Form.Label>\n                <Form.Control\n                  type=\"text\"\n                  value={currentPattern.name}\n                  onChange={(e) => setCurrentPattern({ ...currentPattern, name: e.target.value })}\n                />\n              </Form.Group>\n\n              <h5>Patrón Base (Semanal):</h5>\n              {currentPattern.basePattern.map((day, index) => (\n                <div key={index} className=\"border p-3 mb-2\">\n                  <h6>Día {['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'][index]}</h6>\n                  <Form.Group className=\"mb-2\">\n                    <Form.Label>Tipo de Día</Form.Label>\n                    <Form.Select\n                      value={day.type}\n                      onChange={(e) => handleBasePatternDayChange(index, 'type', e.target.value)}\n                    >\n                      <option value=\"\">Seleccionar</option>\n                      <option value=\"worked\">Trabajado</option>\n                      <option value=\"holiday\">Vacaciones</option>\n                      <option value=\"permit\">Permiso</option>\n                      <option value=\"negative\">Cómputo Negativo</option>\n                    </Form.Select>\n                  </Form.Group>\n                  <Form.Group className=\"mb-2\">\n                    <Form.Label>Horas</Form.Label>\n                    <Form.Control\n                      type=\"number\"\n                      value={day.hours}\n                      onChange={(e) => handleBasePatternDayChange(index, 'hours', parseFloat(e.target.value))}\n                    />\n                  </Form.Group>\n                  <Form.Group className=\"mb-2\">\n                    <Form.Label>Turno Predefinido</Form.Label>\n                    <Form.Select\n                      value={day.shiftId || ''}\n                      onChange={(e) => handleBasePatternDayChange(index, 'shiftId', e.target.value)}\n                    >\n                      <option value=\"\">Ninguno</option>\n                      {availableShifts.map((shift) => (\n                        <option key={shift.id} value={shift.id}>\n                          {shift.name} - {shift.startTime} a {shift.endTime} ({shift.totalHours}h)\n                        </option>\n                      ))}\n                    </Form.Select>\n                  </Form.Group>\n                  <Form.Group className=\"mb-2\">\n                    <Form.Label>Descripción</Form.Label>\n                    <Form.Control\n                      as=\"textarea\"\n                      rows={1}\n                      value={day.description}\n                      onChange={(e) => handleBasePatternDayChange(index, 'description', e.target.value)}\n                    />\n                  </Form.Group>\n                </div>\n              ))}\n\n              <h5 className=\"mt-4\">Anulaciones (Overrides):</h5>\n              {currentPattern.overrides.map((override, overrideIndex) => (\n                <div key={overrideIndex} className=\"border p-3 mb-2 bg-light\">\n                  <h6>Anulación {overrideIndex + 1}</h6>\n                  <Row className=\"mb-2\">\n                    <Col>\n                      <Form.Group controlId={`overrideStartDate-${overrideIndex}`}>\n                        <Form.Label>Fecha de Inicio</Form.Label>\n                        <Form.Control\n                          type=\"date\"\n                          value={override.startDate}\n                          onChange={(e) => handleOverrideChange(overrideIndex, 'startDate', e.target.value)}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col>\n                      <Form.Group controlId={`overrideEndDate-${overrideIndex}`}>\n                        <Form.Label>Fecha de Fin</Form.Label>\n                        <Form.Control\n                          type=\"date\"\n                          value={override.endDate}\n                          onChange={(e) => handleOverrideChange(overrideIndex, 'endDate', e.target.value)}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n                  <h6>Días de la Anulación:</h6>\n                  {override.overridePattern.map((day, dayIndex) => (\n                    <div key={dayIndex} className=\"border p-3 mb-2 ms-3\">\n                      <h6>Día {dayIndex + 1}</h6>\n                      <Form.Group className=\"mb-2\">\n                        <Form.Label>Tipo de Día</Form.Label>\n                        <Form.Select\n                          value={day.type}\n                          onChange={(e) => handleOverridePatternDayChange(overrideIndex, dayIndex, 'type', e.target.value)}\n                        >\n                          <option value=\"\">Seleccionar</option>\n                          <option value=\"worked\">Trabajado</option>\n                          <option value=\"holiday\">Vacaciones</option>\n                          <option value=\"permit\">Permiso</option>\n                          <option value=\"negative\">Cómputo Negativo</option>\n                        </Form.Select>\n                      </Form.Group>\n                      <Form.Group className=\"mb-2\">\n                        <Form.Label>Horas</Form.Label>\n                        <Form.Control\n                          type=\"number\"\n                          value={day.hours}\n                          onChange={(e) => handleOverridePatternDayChange(overrideIndex, dayIndex, 'hours', parseFloat(e.target.value))}\n                        />\n                      </Form.Group>\n                      <Form.Group className=\"mb-2\">\n                        <Form.Label>Turno Predefinido</Form.Label>\n                        <Form.Select\n                          value={day.shiftId || ''}\n                          onChange={(e) => handleOverridePatternDayChange(overrideIndex, dayIndex, 'shiftId', e.target.value)}\n                        >\n                          <option value=\"\">Ninguno</option>\n                          {availableShifts.map((shift) => (\n                            <option key={shift.id} value={shift.id}>\n                              {shift.name} - {shift.startTime} a {shift.endTime} ({shift.totalHours}h)\n                            </option>\n                          ))}\n                        </Form.Select>\n                      </Form.Group>\n                      <Form.Group className=\"mb-2\">\n                        <Form.Label>Descripción</Form.Label>\n                        <Form.Control\n                          as=\"textarea\"\n                          rows={1}\n                          value={day.description}\n                          onChange={(e) => handleOverridePatternDayChange(overrideIndex, dayIndex, 'description', e.target.value)}\n                        />\n                      </Form.Group>\n                      <Button variant=\"danger\" size=\"sm\" onClick={() => handleRemoveOverridePatternDay(overrideIndex, dayIndex)}>\n                        Eliminar Día de Anulación\n                      </Button>\n                    </div>\n                  ))}\n                  <Button variant=\"secondary\" onClick={() => handleAddOverridePatternDay(overrideIndex)} className=\"mt-3\">Añadir Día a Anulación</Button>\n                  <Button variant=\"danger\" onClick={() => handleRemoveOverride(overrideIndex)} className=\"mt-3 ms-2\">Eliminar Anulación</Button>\n                </div>\n              ))}\n              <Button variant=\"info\" onClick={handleAddOverride} className=\"mt-3\">Añadir Anulación</Button>\n            </Form>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={handleCancelEdit}>Cancelar</Button>\n          <Button variant=\"primary\" onClick={handleSavePattern} disabled={loading}>Guardar Patrón</Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n}\n\nexport default SettingsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAQ,iBAAiB;AAC5F,SAASC,cAAc,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,EAAEC,WAAW,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzH,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsB,cAAc,EAAEC,iBAAiB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACsC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAC,IAAIwC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;EAElFxC,SAAS,CAAC,MAAM;IACdyC,YAAY,CAAC,CAAC;IACdC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/Bb,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMe,IAAI,GAAG,MAAMlC,cAAc,CAAC,CAAC;MACnCW,WAAW,CAACuB,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZd,QAAQ,CAAC,0BAA0B,CAAC;MACpCe,OAAO,CAAChB,KAAK,CAAC,yBAAyB,EAAEe,GAAG,CAAC;IAC/C,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMI,MAAM,GAAG,MAAMjC,YAAY,CAAC,CAAC;MACnCa,kBAAkB,CAACoB,MAAM,CAAC;IAC5B,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAAChB,KAAK,CAAC,uBAAuB,EAAEe,GAAG,CAAC;IAC7C;EACF,CAAC;EAED,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzB,iBAAiB,CAAC;MAChB0B,IAAI,EAAE,EAAE;MACRC,WAAW,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,EAAE,OAAO;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,WAAW,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC,CAAC;MACtGC,SAAS,EAAE;IACb,CAAC,CAAC;IACFjC,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMkC,iBAAiB,GAAIC,OAAO,IAAK;IACrCrC,iBAAiB,CAAC;MAAE,GAAGqC;IAAQ,CAAC,CAAC;IACjCnC,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMoC,mBAAmB,GAAG,MAAOC,EAAE,IAAK;IACxC,IAAIC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACxEnC,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMhB,aAAa,CAACiD,EAAE,CAAC;QACvB,MAAMpB,YAAY,CAAC,CAAC;MACtB,CAAC,CAAC,OAAOG,GAAG,EAAE;QACZd,QAAQ,CAAC,0BAA0B,CAAC;QACpCe,OAAO,CAAChB,KAAK,CAAC,yBAAyB,EAAEe,GAAG,CAAC;MAC/C,CAAC,SAAS;QACRhB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;EAED,MAAMoC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC3C,cAAc,IAAI,CAACA,cAAc,CAAC2B,IAAI,IAAI,CAAC3B,cAAc,CAAC4B,WAAW,EAAE;MAC1EnB,QAAQ,CAAC,uDAAuD,CAAC;MACjE;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,IAAIT,cAAc,CAACwC,EAAE,EAAE;QACrB,MAAMlD,aAAa,CAACU,cAAc,CAACwC,EAAE,EAAExC,cAAc,CAAC2B,IAAI,EAAE3B,cAAc,CAAC4B,WAAW,EAAE5B,cAAc,CAACoC,SAAS,CAAC;MACnH,CAAC,MAAM;QACL,MAAM/C,aAAa,CAACW,cAAc,CAAC2B,IAAI,EAAE3B,cAAc,CAAC4B,WAAW,EAAE5B,cAAc,CAACoC,SAAS,CAAC;MAChG;MACAjC,kBAAkB,CAAC,KAAK,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;MACvB,MAAMmB,YAAY,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZd,QAAQ,CAAC,yBAAyB,CAAC;MACnCe,OAAO,CAAChB,KAAK,CAAC,uBAAuB,EAAEe,GAAG,CAAC;IAC7C,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzC,kBAAkB,CAAC,KAAK,CAAC;IACzBF,iBAAiB,CAAC,IAAI,CAAC;IACvBQ,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMoC,0BAA0B,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,KAAK;IAC1D,MAAMC,cAAc,GAAG,CAAC,GAAGjD,cAAc,CAAC4B,WAAW,CAAC;IACtDqB,cAAc,CAACH,KAAK,CAAC,CAACC,KAAK,CAAC,GAAGC,KAAK;IAEpC,IAAID,KAAK,KAAK,SAAS,IAAIC,KAAK,EAAE;MAChC,MAAME,KAAK,GAAG9C,eAAe,CAAC+C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,EAAE,KAAKQ,KAAK,CAAC;MACvD,IAAIE,KAAK,EAAE;QACTD,cAAc,CAACH,KAAK,CAAC,CAACd,IAAI,GAAG,QAAQ;QACrCiB,cAAc,CAACH,KAAK,CAAC,CAACb,KAAK,GAAGiB,KAAK,CAACG,UAAU;QAC9CJ,cAAc,CAACH,KAAK,CAAC,CAACZ,WAAW,GAAG,GAAGgB,KAAK,CAACvB,IAAI,KAAKuB,KAAK,CAACI,SAAS,MAAMJ,KAAK,CAACK,OAAO,KAAKL,KAAK,CAACM,YAAY,eAAe;MAChI;IACF,CAAC,MAAM,IAAIT,KAAK,KAAK,SAAS,IAAI,CAACC,KAAK,EAAE;MACxCC,cAAc,CAACH,KAAK,CAAC,CAACZ,WAAW,GAAG,EAAE;IACxC;IACAjC,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAE4B,WAAW,EAAEqB;IAAe,CAAC,CAAC;EACvE,CAAC;EAED,MAAMQ,oBAAoB,GAAGA,CAACC,aAAa,EAAEX,KAAK,EAAEC,KAAK,KAAK;IAC5D,MAAMW,YAAY,GAAG,CAAC,GAAG3D,cAAc,CAACoC,SAAS,CAAC;IAClDuB,YAAY,CAACD,aAAa,CAAC,CAACX,KAAK,CAAC,GAAGC,KAAK;IAC1C/C,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAEoC,SAAS,EAAEuB;IAAa,CAAC,CAAC;EACnE,CAAC;EAED,MAAMC,8BAA8B,GAAGA,CAACF,aAAa,EAAEG,QAAQ,EAAEd,KAAK,EAAEC,KAAK,KAAK;IAChF,MAAMW,YAAY,GAAG,CAAC,GAAG3D,cAAc,CAACoC,SAAS,CAAC;IAClD,MAAM0B,kBAAkB,GAAG,CAAC,GAAGH,YAAY,CAACD,aAAa,CAAC,CAACK,eAAe,CAAC;IAC3ED,kBAAkB,CAACD,QAAQ,CAAC,CAACd,KAAK,CAAC,GAAGC,KAAK;IAE3C,IAAID,KAAK,KAAK,SAAS,IAAIC,KAAK,EAAE;MAChC,MAAME,KAAK,GAAG9C,eAAe,CAAC+C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,EAAE,KAAKQ,KAAK,CAAC;MACvD,IAAIE,KAAK,EAAE;QACTY,kBAAkB,CAACD,QAAQ,CAAC,CAAC7B,IAAI,GAAG,QAAQ;QAC5C8B,kBAAkB,CAACD,QAAQ,CAAC,CAAC5B,KAAK,GAAGiB,KAAK,CAACG,UAAU;QACrDS,kBAAkB,CAACD,QAAQ,CAAC,CAAC3B,WAAW,GAAG,GAAGgB,KAAK,CAACvB,IAAI,KAAKuB,KAAK,CAACI,SAAS,MAAMJ,KAAK,CAACK,OAAO,KAAKL,KAAK,CAACM,YAAY,eAAe;MACvI;IACF,CAAC,MAAM,IAAIT,KAAK,KAAK,SAAS,IAAI,CAACC,KAAK,EAAE;MACxCc,kBAAkB,CAACD,QAAQ,CAAC,CAAC3B,WAAW,GAAG,EAAE;IAC/C;IAEAyB,YAAY,CAACD,aAAa,CAAC,CAACK,eAAe,GAAGD,kBAAkB;IAChE7D,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAEoC,SAAS,EAAEuB;IAAa,CAAC,CAAC;EACnE,CAAC;EAED,MAAMK,iBAAiB,GAAGA,CAAA,KAAM;IAC9B/D,iBAAiB,CAAC;MAChB,GAAGD,cAAc;MACjBoC,SAAS,EAAE,CAAC,GAAGpC,cAAc,CAACoC,SAAS,EAAE;QAAE6B,SAAS,EAAE,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEH,eAAe,EAAE,CAAC;UAAE/B,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,CAAC;UAAEC,WAAW,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAK,CAAC;MAAE,CAAC;IACpJ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgC,oBAAoB,GAAIT,aAAa,IAAK;IAC9C,MAAMC,YAAY,GAAG3D,cAAc,CAACoC,SAAS,CAACgC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKZ,aAAa,CAAC;IACnFzD,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAEoC,SAAS,EAAEuB;IAAa,CAAC,CAAC;EACnE,CAAC;EAED,MAAMY,2BAA2B,GAAIb,aAAa,IAAK;IACrD,MAAMC,YAAY,GAAG,CAAC,GAAG3D,cAAc,CAACoC,SAAS,CAAC;IAClDuB,YAAY,CAACD,aAAa,CAAC,CAACK,eAAe,CAACS,IAAI,CAAC;MAAExC,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,WAAW,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IACxGlC,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAEoC,SAAS,EAAEuB;IAAa,CAAC,CAAC;EACnE,CAAC;EAED,MAAMc,8BAA8B,GAAGA,CAACf,aAAa,EAAEG,QAAQ,KAAK;IAClE,MAAMF,YAAY,GAAG,CAAC,GAAG3D,cAAc,CAACoC,SAAS,CAAC;IAClDuB,YAAY,CAACD,aAAa,CAAC,CAACK,eAAe,GAAGJ,YAAY,CAACD,aAAa,CAAC,CAACK,eAAe,CAACK,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKT,QAAQ,CAAC;IAC1H5D,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAEoC,SAAS,EAAEuB;IAAa,CAAC,CAAC;EACnE,CAAC;EAED,oBACEhE,OAAA,CAACf,SAAS;IAAA8F,QAAA,gBACR/E,OAAA,CAACd,GAAG;MAAC8F,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnB/E,OAAA,CAACb,GAAG;QAAA4F,QAAA,gBACF/E,OAAA;UAAA+E,QAAA,EAAI;QAAuC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC/CvE,KAAK,iBAAIb,OAAA,CAACV,KAAK;UAAC+F,OAAO,EAAC,QAAQ;UAAAN,QAAA,EAAElE;QAAK;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACjDpF,OAAA,CAACZ,MAAM;UAACkG,OAAO,EAAEvD,gBAAiB;UAACiD,SAAS,EAAC,MAAM;UAAAD,QAAA,EAAC;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAE9EzE,OAAO,gBACNX,OAAA;UAAA+E,QAAA,EAAG;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,GACzBjF,QAAQ,CAACiC,MAAM,KAAK,CAAC,gBACvBpC,OAAA;UAAA+E,QAAA,EAAG;QAA0B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAEjCpF,OAAA,CAACT,SAAS;UAAAwF,QAAA,EACP5E,QAAQ,CAACoF,GAAG,CAAC5C,OAAO,iBACnB3C,OAAA,CAACT,SAAS,CAACiG,IAAI;YAAkBR,SAAS,EAAC,mDAAmD;YAAAD,QAAA,GAC3FpC,OAAO,CAACX,IAAI,eACbhC,OAAA;cAAA+E,QAAA,gBACE/E,OAAA,CAACZ,MAAM;gBAACiG,OAAO,EAAC,MAAM;gBAACI,IAAI,EAAC,IAAI;gBAACT,SAAS,EAAC,MAAM;gBAACM,OAAO,EAAEA,CAAA,KAAM5C,iBAAiB,CAACC,OAAO,CAAE;gBAAAoC,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5GpF,OAAA,CAACZ,MAAM;gBAACiG,OAAO,EAAC,WAAW;gBAACI,IAAI,EAAC,IAAI;gBAACT,SAAS,EAAC,MAAM;gBAACM,OAAO,EAAEA,CAAA,KAAMI,mBAAmB,CAAC/C,OAAO,CAAE;gBAAAoC,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnHpF,OAAA,CAACZ,MAAM;gBAACiG,OAAO,EAAC,QAAQ;gBAACI,IAAI,EAAC,IAAI;gBAACH,OAAO,EAAEA,CAAA,KAAM1C,mBAAmB,CAACD,OAAO,CAACE,EAAE,CAAE;gBAAAkC,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjG,CAAC;UAAA,GANazC,OAAO,CAACE,EAAE;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOf,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CACZ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpF,OAAA,CAACR,KAAK;MAACmG,IAAI,EAAEpF,eAAgB;MAACqF,MAAM,EAAE3C,gBAAiB;MAACwC,IAAI,EAAC,IAAI;MAAAV,QAAA,gBAC/D/E,OAAA,CAACR,KAAK,CAACqG,MAAM;QAACC,WAAW;QAAAf,QAAA,eACvB/E,OAAA,CAACR,KAAK,CAACuG,KAAK;UAAAhB,QAAA,EAAE1E,cAAc,IAAIA,cAAc,CAACwC,EAAE,GAAG,eAAe,GAAG;QAAoB;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F,CAAC,eACfpF,OAAA,CAACR,KAAK,CAACwG,IAAI;QAAAjB,QAAA,EACR1E,cAAc,iBACbL,OAAA,CAACX,IAAI;UAAA0F,QAAA,gBACH/E,OAAA,CAACX,IAAI,CAAC4G,KAAK;YAACjB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1B/E,OAAA,CAACX,IAAI,CAAC6G,KAAK;cAAAnB,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1CpF,OAAA,CAACX,IAAI,CAAC8G,OAAO;cACX9D,IAAI,EAAC,MAAM;cACXgB,KAAK,EAAEhD,cAAc,CAAC2B,IAAK;cAC3BoE,QAAQ,EAAGC,CAAC,IAAK/F,iBAAiB,CAAC;gBAAE,GAAGD,cAAc;gBAAE2B,IAAI,EAAEqE,CAAC,CAACC,MAAM,CAACjD;cAAM,CAAC;YAAE;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEbpF,OAAA;YAAA+E,QAAA,EAAI;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC9B/E,cAAc,CAAC4B,WAAW,CAACsD,GAAG,CAAC,CAACgB,GAAG,EAAEpD,KAAK,kBACzCnD,OAAA;YAAiBgF,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC1C/E,OAAA;cAAA+E,QAAA,GAAI,SAAI,EAAC,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC5B,KAAK,CAAC;YAAA;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChGpF,OAAA,CAACX,IAAI,CAAC4G,KAAK;cAACjB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1B/E,OAAA,CAACX,IAAI,CAAC6G,KAAK;gBAAAnB,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpCpF,OAAA,CAACX,IAAI,CAACmH,MAAM;gBACVnD,KAAK,EAAEkD,GAAG,CAAClE,IAAK;gBAChB+D,QAAQ,EAAGC,CAAC,IAAKnD,0BAA0B,CAACC,KAAK,EAAE,MAAM,EAAEkD,CAAC,CAACC,MAAM,CAACjD,KAAK,CAAE;gBAAA0B,QAAA,gBAE3E/E,OAAA;kBAAQqD,KAAK,EAAC,EAAE;kBAAA0B,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCpF,OAAA;kBAAQqD,KAAK,EAAC,QAAQ;kBAAA0B,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCpF,OAAA;kBAAQqD,KAAK,EAAC,SAAS;kBAAA0B,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3CpF,OAAA;kBAAQqD,KAAK,EAAC,QAAQ;kBAAA0B,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCpF,OAAA;kBAAQqD,KAAK,EAAC,UAAU;kBAAA0B,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACbpF,OAAA,CAACX,IAAI,CAAC4G,KAAK;cAACjB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1B/E,OAAA,CAACX,IAAI,CAAC6G,KAAK;gBAAAnB,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9BpF,OAAA,CAACX,IAAI,CAAC8G,OAAO;gBACX9D,IAAI,EAAC,QAAQ;gBACbgB,KAAK,EAAEkD,GAAG,CAACjE,KAAM;gBACjB8D,QAAQ,EAAGC,CAAC,IAAKnD,0BAA0B,CAACC,KAAK,EAAE,OAAO,EAAEsD,UAAU,CAACJ,CAAC,CAACC,MAAM,CAACjD,KAAK,CAAC;cAAE;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eACbpF,OAAA,CAACX,IAAI,CAAC4G,KAAK;cAACjB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1B/E,OAAA,CAACX,IAAI,CAAC6G,KAAK;gBAAAnB,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1CpF,OAAA,CAACX,IAAI,CAACmH,MAAM;gBACVnD,KAAK,EAAEkD,GAAG,CAAC/D,OAAO,IAAI,EAAG;gBACzB4D,QAAQ,EAAGC,CAAC,IAAKnD,0BAA0B,CAACC,KAAK,EAAE,SAAS,EAAEkD,CAAC,CAACC,MAAM,CAACjD,KAAK,CAAE;gBAAA0B,QAAA,gBAE9E/E,OAAA;kBAAQqD,KAAK,EAAC,EAAE;kBAAA0B,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAChC3E,eAAe,CAAC8E,GAAG,CAAEhC,KAAK,iBACzBvD,OAAA;kBAAuBqD,KAAK,EAAEE,KAAK,CAACV,EAAG;kBAAAkC,QAAA,GACpCxB,KAAK,CAACvB,IAAI,EAAC,KAAG,EAACuB,KAAK,CAACI,SAAS,EAAC,KAAG,EAACJ,KAAK,CAACK,OAAO,EAAC,IAAE,EAACL,KAAK,CAACG,UAAU,EAAC,IACxE;gBAAA,GAFaH,KAAK,CAACV,EAAE;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACbpF,OAAA,CAACX,IAAI,CAAC4G,KAAK;cAACjB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1B/E,OAAA,CAACX,IAAI,CAAC6G,KAAK;gBAAAnB,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpCpF,OAAA,CAACX,IAAI,CAAC8G,OAAO;gBACXO,EAAE,EAAC,UAAU;gBACbC,IAAI,EAAE,CAAE;gBACRtD,KAAK,EAAEkD,GAAG,CAAChE,WAAY;gBACvB6D,QAAQ,EAAGC,CAAC,IAAKnD,0BAA0B,CAACC,KAAK,EAAE,aAAa,EAAEkD,CAAC,CAACC,MAAM,CAACjD,KAAK;cAAE;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA,GA7CLjC,KAAK;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8CV,CACN,CAAC,eAEFpF,OAAA;YAAIgF,SAAS,EAAC,MAAM;YAAAD,QAAA,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACjD/E,cAAc,CAACoC,SAAS,CAAC8C,GAAG,CAAC,CAACqB,QAAQ,EAAE7C,aAAa,kBACpD/D,OAAA;YAAyBgF,SAAS,EAAC,0BAA0B;YAAAD,QAAA,gBAC3D/E,OAAA;cAAA+E,QAAA,GAAI,eAAU,EAAChB,aAAa,GAAG,CAAC;YAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCpF,OAAA,CAACd,GAAG;cAAC8F,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB/E,OAAA,CAACb,GAAG;gBAAA4F,QAAA,eACF/E,OAAA,CAACX,IAAI,CAAC4G,KAAK;kBAACY,SAAS,EAAE,qBAAqB9C,aAAa,EAAG;kBAAAgB,QAAA,gBAC1D/E,OAAA,CAACX,IAAI,CAAC6G,KAAK;oBAAAnB,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACxCpF,OAAA,CAACX,IAAI,CAAC8G,OAAO;oBACX9D,IAAI,EAAC,MAAM;oBACXgB,KAAK,EAAEuD,QAAQ,CAACtC,SAAU;oBAC1B8B,QAAQ,EAAGC,CAAC,IAAKvC,oBAAoB,CAACC,aAAa,EAAE,WAAW,EAAEsC,CAAC,CAACC,MAAM,CAACjD,KAAK;kBAAE;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNpF,OAAA,CAACb,GAAG;gBAAA4F,QAAA,eACF/E,OAAA,CAACX,IAAI,CAAC4G,KAAK;kBAACY,SAAS,EAAE,mBAAmB9C,aAAa,EAAG;kBAAAgB,QAAA,gBACxD/E,OAAA,CAACX,IAAI,CAAC6G,KAAK;oBAAAnB,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrCpF,OAAA,CAACX,IAAI,CAAC8G,OAAO;oBACX9D,IAAI,EAAC,MAAM;oBACXgB,KAAK,EAAEuD,QAAQ,CAACrC,OAAQ;oBACxB6B,QAAQ,EAAGC,CAAC,IAAKvC,oBAAoB,CAACC,aAAa,EAAE,SAAS,EAAEsC,CAAC,CAACC,MAAM,CAACjD,KAAK;kBAAE;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpF,OAAA;cAAA+E,QAAA,EAAI;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC7BwB,QAAQ,CAACxC,eAAe,CAACmB,GAAG,CAAC,CAACgB,GAAG,EAAErC,QAAQ,kBAC1ClE,OAAA;cAAoBgF,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBAClD/E,OAAA;gBAAA+E,QAAA,GAAI,SAAI,EAACb,QAAQ,GAAG,CAAC;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3BpF,OAAA,CAACX,IAAI,CAAC4G,KAAK;gBAACjB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1B/E,OAAA,CAACX,IAAI,CAAC6G,KAAK;kBAAAnB,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCpF,OAAA,CAACX,IAAI,CAACmH,MAAM;kBACVnD,KAAK,EAAEkD,GAAG,CAAClE,IAAK;kBAChB+D,QAAQ,EAAGC,CAAC,IAAKpC,8BAA8B,CAACF,aAAa,EAAEG,QAAQ,EAAE,MAAM,EAAEmC,CAAC,CAACC,MAAM,CAACjD,KAAK,CAAE;kBAAA0B,QAAA,gBAEjG/E,OAAA;oBAAQqD,KAAK,EAAC,EAAE;oBAAA0B,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrCpF,OAAA;oBAAQqD,KAAK,EAAC,QAAQ;oBAAA0B,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzCpF,OAAA;oBAAQqD,KAAK,EAAC,SAAS;oBAAA0B,QAAA,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC3CpF,OAAA;oBAAQqD,KAAK,EAAC,QAAQ;oBAAA0B,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvCpF,OAAA;oBAAQqD,KAAK,EAAC,UAAU;oBAAA0B,QAAA,EAAC;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACbpF,OAAA,CAACX,IAAI,CAAC4G,KAAK;gBAACjB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1B/E,OAAA,CAACX,IAAI,CAAC6G,KAAK;kBAAAnB,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9BpF,OAAA,CAACX,IAAI,CAAC8G,OAAO;kBACX9D,IAAI,EAAC,QAAQ;kBACbgB,KAAK,EAAEkD,GAAG,CAACjE,KAAM;kBACjB8D,QAAQ,EAAGC,CAAC,IAAKpC,8BAA8B,CAACF,aAAa,EAAEG,QAAQ,EAAE,OAAO,EAAEuC,UAAU,CAACJ,CAAC,CAACC,MAAM,CAACjD,KAAK,CAAC;gBAAE;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eACbpF,OAAA,CAACX,IAAI,CAAC4G,KAAK;gBAACjB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1B/E,OAAA,CAACX,IAAI,CAAC6G,KAAK;kBAAAnB,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1CpF,OAAA,CAACX,IAAI,CAACmH,MAAM;kBACVnD,KAAK,EAAEkD,GAAG,CAAC/D,OAAO,IAAI,EAAG;kBACzB4D,QAAQ,EAAGC,CAAC,IAAKpC,8BAA8B,CAACF,aAAa,EAAEG,QAAQ,EAAE,SAAS,EAAEmC,CAAC,CAACC,MAAM,CAACjD,KAAK,CAAE;kBAAA0B,QAAA,gBAEpG/E,OAAA;oBAAQqD,KAAK,EAAC,EAAE;oBAAA0B,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAChC3E,eAAe,CAAC8E,GAAG,CAAEhC,KAAK,iBACzBvD,OAAA;oBAAuBqD,KAAK,EAAEE,KAAK,CAACV,EAAG;oBAAAkC,QAAA,GACpCxB,KAAK,CAACvB,IAAI,EAAC,KAAG,EAACuB,KAAK,CAACI,SAAS,EAAC,KAAG,EAACJ,KAAK,CAACK,OAAO,EAAC,IAAE,EAACL,KAAK,CAACG,UAAU,EAAC,IACxE;kBAAA,GAFaH,KAAK,CAACV,EAAE;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEb,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACbpF,OAAA,CAACX,IAAI,CAAC4G,KAAK;gBAACjB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1B/E,OAAA,CAACX,IAAI,CAAC6G,KAAK;kBAAAnB,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCpF,OAAA,CAACX,IAAI,CAAC8G,OAAO;kBACXO,EAAE,EAAC,UAAU;kBACbC,IAAI,EAAE,CAAE;kBACRtD,KAAK,EAAEkD,GAAG,CAAChE,WAAY;kBACvB6D,QAAQ,EAAGC,CAAC,IAAKpC,8BAA8B,CAACF,aAAa,EAAEG,QAAQ,EAAE,aAAa,EAAEmC,CAAC,CAACC,MAAM,CAACjD,KAAK;gBAAE;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eACbpF,OAAA,CAACZ,MAAM;gBAACiG,OAAO,EAAC,QAAQ;gBAACI,IAAI,EAAC,IAAI;gBAACH,OAAO,EAAEA,CAAA,KAAMR,8BAA8B,CAACf,aAAa,EAAEG,QAAQ,CAAE;gBAAAa,QAAA,EAAC;cAE3G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GAhDDlB,QAAQ;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiDb,CACN,CAAC,eACFpF,OAAA,CAACZ,MAAM;cAACiG,OAAO,EAAC,WAAW;cAACC,OAAO,EAAEA,CAAA,KAAMV,2BAA2B,CAACb,aAAa,CAAE;cAACiB,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvIpF,OAAA,CAACZ,MAAM;cAACiG,OAAO,EAAC,QAAQ;cAACC,OAAO,EAAEA,CAAA,KAAMd,oBAAoB,CAACT,aAAa,CAAE;cAACiB,SAAS,EAAC,WAAW;cAAAD,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,GA9EtHrB,aAAa;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+ElB,CACN,CAAC,eACFpF,OAAA,CAACZ,MAAM;YAACiG,OAAO,EAAC,MAAM;YAACC,OAAO,EAAEjB,iBAAkB;YAACW,SAAS,EAAC,MAAM;YAAAD,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACbpF,OAAA,CAACR,KAAK,CAACsH,MAAM;QAAA/B,QAAA,gBACX/E,OAAA,CAACZ,MAAM;UAACiG,OAAO,EAAC,WAAW;UAACC,OAAO,EAAErC,gBAAiB;UAAA8B,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxEpF,OAAA,CAACZ,MAAM;UAACiG,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEtC,iBAAkB;UAAC+D,QAAQ,EAAEpG,OAAQ;UAAAoE,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB;AAAClF,EAAA,CApWQD,YAAY;AAAA+G,EAAA,GAAZ/G,YAAY;AAsWrB,eAAeA,YAAY;AAAC,IAAA+G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}