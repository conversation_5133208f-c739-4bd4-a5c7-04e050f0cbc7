import React, { useState, useEffect } from 'react';
import { Modal, <PERSON>, Button, Row, Col, Alert, ButtonGroup } from 'react-bootstrap';
import { 
  createOrUpdateRealDay, 
  deleteRealDay, 
  getAllShifts, 
  suggestShiftsForDate 
} from '../services/api';

function RealDayModal({ show, onHide, selectedDate, existingDay, onDayUpdated }) {
  const [formData, setFormData] = useState({
    type: 'trabajo',
    hours: '',
    entryTime: '',
    exitTime: '',
    description: '',
    shiftId: ''
  });
  
  const [useShift, setUseShift] = useState(false);
  const [availableShifts, setAvailableShifts] = useState([]);
  const [suggestedShifts, setSuggestedShifts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [mode, setMode] = useState('create'); // 'create', 'edit', 'delete'

  useEffect(() => {
    if (show) {
      loadShifts();
      loadSuggestedShifts();
      
      if (existingDay) {
        setMode('edit');
        setFormData({
          type: existingDay.type || 'trabajo',
          hours: existingDay.hours || '',
          entryTime: existingDay.entryTime || '',
          exitTime: existingDay.exitTime || '',
          description: existingDay.description || '',
          shiftId: existingDay.shiftId || ''
        });
        setUseShift(!!existingDay.shiftId);
      } else {
        setMode('create');
        resetForm();
      }
    }
  }, [show, existingDay]);

  const loadShifts = async () => {
    try {
      const shifts = await getAllShifts();
      setAvailableShifts(shifts);
    } catch (err) {
      console.error('Error loading shifts:', err);
    }
  };

  const loadSuggestedShifts = async () => {
    if (selectedDate) {
      try {
        const suggestions = await suggestShiftsForDate(selectedDate.toISOString().split('T')[0]);
        setSuggestedShifts(suggestions);
      } catch (err) {
        console.error('Error loading suggested shifts:', err);
      }
    }
  };

  const resetForm = () => {
    setFormData({
      type: 'trabajo',
      hours: '',
      entryTime: '',
      exitTime: '',
      description: '',
      shiftId: ''
    });
    setUseShift(false);
    setError('');
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Auto-calcular horas si se especifican entrada y salida
    if (field === 'entryTime' || field === 'exitTime') {
      const entry = field === 'entryTime' ? value : formData.entryTime;
      const exit = field === 'exitTime' ? value : formData.exitTime;
      
      if (entry && exit) {
        const hours = calculateHours(entry, exit);
        setFormData(prev => ({ ...prev, hours: hours.toString() }));
      }
    }
  };

  const calculateHours = (entryTime, exitTime) => {
    if (!entryTime || !exitTime) return 0;
    
    const [entryHour, entryMin] = entryTime.split(':').map(Number);
    const [exitHour, exitMin] = exitTime.split(':').map(Number);
    
    let entryMinutes = entryHour * 60 + entryMin;
    let exitMinutes = exitHour * 60 + exitMin;
    
    // Si la salida es menor que la entrada, asumimos que es del día siguiente
    if (exitMinutes < entryMinutes) {
      exitMinutes += 24 * 60;
    }
    
    const diffMinutes = exitMinutes - entryMinutes;
    return Math.round((diffMinutes / 60) * 100) / 100; // Redondear a 2 decimales
  };

  const handleShiftSelect = (shift) => {
    setFormData(prev => ({
      ...prev,
      shiftId: shift.id,
      entryTime: shift.entryTime || '',
      exitTime: shift.exitTime || '',
      hours: shift.hours.toString(),
      description: shift.description || prev.description
    }));
    setUseShift(true);
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      setError('');

      // Validaciones
      if (!formData.hours || parseFloat(formData.hours) <= 0) {
        setError('Las horas deben ser un número mayor que 0');
        return;
      }

      if (formData.entryTime && formData.exitTime) {
        const calculatedHours = calculateHours(formData.entryTime, formData.exitTime);
        if (Math.abs(calculatedHours - parseFloat(formData.hours)) > 0.1) {
          if (!window.confirm(`Las horas calculadas (${calculatedHours}h) no coinciden con las especificadas (${formData.hours}h). ¿Continuar?`)) {
            return;
          }
        }
      }

      const dayData = {
        date: selectedDate.toISOString().split('T')[0],
        type: formData.type,
        hours: parseFloat(formData.hours),
        entryTime: formData.entryTime || null,
        exitTime: formData.exitTime || null,
        description: formData.description || null,
        shiftId: useShift ? formData.shiftId : null
      };

      await createOrUpdateRealDay(dayData);
      onDayUpdated();
      onHide();
    } catch (err) {
      setError('Error guardando el día: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!existingDay) return;
    
    if (window.confirm('¿Estás seguro de que quieres eliminar este registro?')) {
      try {
        setLoading(true);
        await deleteRealDay(existingDay.id);
        onDayUpdated();
        onHide();
      } catch (err) {
        setError('Error eliminando el día: ' + err.message);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <Modal show={show} onHide={onHide} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>
          {mode === 'edit' ? 'Editar' : 'Registrar'} Día Real
          {selectedDate && ` - ${selectedDate.toLocaleDateString('es-ES')}`}
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body>
        {error && (
          <Alert variant="danger" className="mb-3">
            {error}
          </Alert>
        )}

        {/* Turnos sugeridos */}
        {suggestedShifts.length > 0 && (
          <div className="mb-3">
            <Form.Label>Turnos sugeridos para este día:</Form.Label>
            <div className="d-flex flex-wrap gap-2">
              {suggestedShifts.map(shift => (
                <Button
                  key={shift.id}
                  variant="outline-info"
                  size="sm"
                  onClick={() => handleShiftSelect(shift)}
                >
                  {shift.name} ({shift.hours}h)
                </Button>
              ))}
            </div>
          </div>
        )}

        <Form>
          {/* Tipo de día */}
          <Form.Group className="mb-3">
            <Form.Label>Tipo de día</Form.Label>
            <Form.Select
              value={formData.type}
              onChange={(e) => handleInputChange('type', e.target.value)}
            >
              <option value="trabajo">Trabajo</option>
              <option value="vacaciones">Vacaciones</option>
              <option value="baja">Baja médica</option>
              <option value="festivo">Festivo</option>
              <option value="libre">Día libre</option>
            </Form.Select>
          </Form.Group>

          {/* Usar turno predefinido */}
          <Form.Group className="mb-3">
            <Form.Check
              type="checkbox"
              label="Usar turno predefinido"
              checked={useShift}
              onChange={(e) => setUseShift(e.target.checked)}
            />
          </Form.Group>

          {useShift && (
            <Form.Group className="mb-3">
              <Form.Label>Seleccionar turno</Form.Label>
              <Form.Select
                value={formData.shiftId}
                onChange={(e) => {
                  const shift = availableShifts.find(s => s.id === e.target.value);
                  if (shift) {
                    handleShiftSelect(shift);
                  }
                }}
              >
                <option value="">Seleccionar turno...</option>
                {availableShifts.map(shift => (
                  <option key={shift.id} value={shift.id}>
                    {shift.name} - {shift.hours}h ({shift.entryTime}-{shift.exitTime})
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
          )}

          {/* Horas trabajadas */}
          <Form.Group className="mb-3">
            <Form.Label>Horas trabajadas</Form.Label>
            <Form.Control
              type="number"
              step="0.25"
              min="0"
              max="24"
              value={formData.hours}
              onChange={(e) => handleInputChange('hours', e.target.value)}
              placeholder="8.0"
            />
          </Form.Group>

          {/* Horarios específicos */}
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Hora de entrada (opcional)</Form.Label>
                <Form.Control
                  type="time"
                  value={formData.entryTime}
                  onChange={(e) => handleInputChange('entryTime', e.target.value)}
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Hora de salida (opcional)</Form.Label>
                <Form.Control
                  type="time"
                  value={formData.exitTime}
                  onChange={(e) => handleInputChange('exitTime', e.target.value)}
                />
              </Form.Group>
            </Col>
          </Row>

          {/* Descripción */}
          <Form.Group className="mb-3">
            <Form.Label>Descripción (opcional)</Form.Label>
            <Form.Control
              as="textarea"
              rows={2}
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Detalles adicionales del día..."
            />
          </Form.Group>
        </Form>
      </Modal.Body>
      
      <Modal.Footer>
        <div className="d-flex justify-content-between w-100">
          <div>
            {mode === 'edit' && (
              <Button
                variant="danger"
                onClick={handleDelete}
                disabled={loading}
              >
                🗑️ Eliminar
              </Button>
            )}
          </div>
          <div>
            <Button variant="secondary" onClick={onHide} disabled={loading}>
              Cancelar
            </Button>
            <Button
              variant="primary"
              onClick={handleSave}
              disabled={loading}
              className="ms-2"
            >
              {loading ? 'Guardando...' : (mode === 'edit' ? 'Actualizar' : 'Guardar')}
            </Button>
          </div>
        </div>
      </Modal.Footer>
    </Modal>
  );
}

export default RealDayModal;
