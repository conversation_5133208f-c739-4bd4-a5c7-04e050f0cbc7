const express = require('express');
const router = express.Router();
const shiftController = require('../controllers/shiftController');

// GET /api/shifts - Obtener todos los turnos predefinidos
router.get('/', shiftController.getAllShifts);

// GET /api/shifts/statistics - Obtener estadísticas de turnos
router.get('/statistics', shiftController.getShiftStatistics);

// GET /api/shifts/suggest/:date - Sugerir turnos para una fecha específica
router.get('/suggest/:date', shiftController.suggestShiftsForDate);

// GET /api/shifts/:shiftId - Obtener un turno específico por ID
router.get('/:shiftId', shiftController.getShiftById);

// POST /api/shifts/apply - Aplicar un turno a una fecha específica
router.post('/apply', shiftController.applyShiftToDate);

// POST /api/shifts/apply-multiple - Aplicar un turno a múltiples fechas
router.post('/apply-multiple', shiftController.applyShiftToMultipleDates);

module.exports = router;
