{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:5000/api/days';\nconst SHIFTS_API_URL = 'http://localhost:5000/api/shifts';\nexport const getAllDays = async () => {\n  try {\n    const response = await axios.get(API_URL);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all days:', error);\n    throw error;\n  }\n};\nexport const getDayByDate = async date => {\n  try {\n    const response = await axios.get(`${API_URL}/${date}`);\n    return response.data;\n  } catch (error) {\n    if (error.response && error.response.status === 404) {\n      return null; // Day not found, return null instead of throwing error\n    }\n    console.error(`Error fetching day for ${date}:`, error);\n    throw error;\n  }\n};\nexport const createOrUpdateDay = async (date, type, hours, description) => {\n  try {\n    const response = await axios.post(API_URL, {\n      date,\n      type,\n      hours,\n      description\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error creating or updating day:', error);\n    throw error;\n  }\n};\n\n// ===== FUNCIONES PARA TURNOS PREDEFINIDOS =====\n\n/**\n * Obtiene todos los turnos predefinidos\n */\nexport const getAllShifts = async () => {\n  try {\n    const response = await axios.get(SHIFTS_API_URL);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all shifts:', error);\n    throw error;\n  }\n};\n\n/**\n * Obtiene un turno específico por ID\n */\nexport const getShiftById = async shiftId => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/${shiftId}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error fetching shift ${shiftId}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un turno predefinido a una fecha específica\n */\nexport const applyShiftToDate = async (date, shiftId, description = '') => {\n  try {\n    const response = await axios.post(`${SHIFTS_API_URL}/apply`, {\n      date,\n      shiftId,\n      description\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying shift to date:', error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un turno a múltiples fechas\n */\nexport const applyShiftToMultipleDates = async (dates, shiftId, description = '') => {\n  try {\n    const response = await axios.post(`${SHIFTS_API_URL}/apply-multiple`, {\n      dates,\n      shiftId,\n      description\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying shift to multiple dates:', error);\n    throw error;\n  }\n};\n\n/**\n * Obtiene estadísticas de turnos aplicados\n */\nexport const getShiftStatistics = async () => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/statistics`);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching shift statistics:', error);\n    throw error;\n  }\n};\n\n/**\n * Sugiere turnos apropiados para una fecha\n */\nexport const suggestShiftsForDate = async date => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/suggest/${date}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error suggesting shifts for date ${date}:`, error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["axios", "API_URL", "SHIFTS_API_URL", "getAllDays", "response", "get", "data", "error", "console", "getDayByDate", "date", "status", "createOrUpdateDay", "type", "hours", "description", "post", "getAllShifts", "getShiftById", "shiftId", "applyShiftToDate", "applyShiftToMultipleDates", "dates", "getShiftStatistics", "suggestShiftsForDate"], "sources": ["D:/Proyectos Python/Horario/client/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:5000/api/days';\nconst SHIFTS_API_URL = 'http://localhost:5000/api/shifts';\n\nexport const getAllDays = async () => {\n  try {\n    const response = await axios.get(API_URL);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all days:', error);\n    throw error;\n  }\n};\n\nexport const getDayByDate = async (date) => {\n  try {\n    const response = await axios.get(`${API_URL}/${date}`);\n    return response.data;\n  } catch (error) {\n    if (error.response && error.response.status === 404) {\n      return null; // Day not found, return null instead of throwing error\n    }\n    console.error(`Error fetching day for ${date}:`, error);\n    throw error;\n  }\n};\n\nexport const createOrUpdateDay = async (date, type, hours, description) => {\n  try {\n    const response = await axios.post(API_URL, { date, type, hours, description });\n    return response.data;\n  } catch (error) {\n    console.error('Error creating or updating day:', error);\n    throw error;\n  }\n};\n\n// ===== FUNCIONES PARA TURNOS PREDEFINIDOS =====\n\n/**\n * Obtiene todos los turnos predefinidos\n */\nexport const getAllShifts = async () => {\n  try {\n    const response = await axios.get(SHIFTS_API_URL);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching all shifts:', error);\n    throw error;\n  }\n};\n\n/**\n * Obtiene un turno específico por ID\n */\nexport const getShiftById = async (shiftId) => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/${shiftId}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error fetching shift ${shiftId}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un turno predefinido a una fecha específica\n */\nexport const applyShiftToDate = async (date, shiftId, description = '') => {\n  try {\n    const response = await axios.post(`${SHIFTS_API_URL}/apply`, {\n      date,\n      shiftId,\n      description\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying shift to date:', error);\n    throw error;\n  }\n};\n\n/**\n * Aplica un turno a múltiples fechas\n */\nexport const applyShiftToMultipleDates = async (dates, shiftId, description = '') => {\n  try {\n    const response = await axios.post(`${SHIFTS_API_URL}/apply-multiple`, {\n      dates,\n      shiftId,\n      description\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Error applying shift to multiple dates:', error);\n    throw error;\n  }\n};\n\n/**\n * Obtiene estadísticas de turnos aplicados\n */\nexport const getShiftStatistics = async () => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/statistics`);\n    return response.data;\n  } catch (error) {\n    console.error('Error fetching shift statistics:', error);\n    throw error;\n  }\n};\n\n/**\n * Sugiere turnos apropiados para una fecha\n */\nexport const suggestShiftsForDate = async (date) => {\n  try {\n    const response = await axios.get(`${SHIFTS_API_URL}/suggest/${date}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error suggesting shifts for date ${date}:`, error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,gCAAgC;AAChD,MAAMC,cAAc,GAAG,kCAAkC;AAEzD,OAAO,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;EACpC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMJ,KAAK,CAACK,GAAG,CAACJ,OAAO,CAAC;IACzC,OAAOG,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAME,YAAY,GAAG,MAAOC,IAAI,IAAK;EAC1C,IAAI;IACF,MAAMN,QAAQ,GAAG,MAAMJ,KAAK,CAACK,GAAG,CAAC,GAAGJ,OAAO,IAAIS,IAAI,EAAE,CAAC;IACtD,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,IAAIA,KAAK,CAACH,QAAQ,IAAIG,KAAK,CAACH,QAAQ,CAACO,MAAM,KAAK,GAAG,EAAE;MACnD,OAAO,IAAI,CAAC,CAAC;IACf;IACAH,OAAO,CAACD,KAAK,CAAC,0BAA0BG,IAAI,GAAG,EAAEH,KAAK,CAAC;IACvD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMK,iBAAiB,GAAG,MAAAA,CAAOF,IAAI,EAAEG,IAAI,EAAEC,KAAK,EAAEC,WAAW,KAAK;EACzE,IAAI;IACF,MAAMX,QAAQ,GAAG,MAAMJ,KAAK,CAACgB,IAAI,CAACf,OAAO,EAAE;MAAES,IAAI;MAAEG,IAAI;MAAEC,KAAK;MAAEC;IAAY,CAAC,CAAC;IAC9E,OAAOX,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACvD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;;AAEA;AACA;AACA;AACA,OAAO,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;EACtC,IAAI;IACF,MAAMb,QAAQ,GAAG,MAAMJ,KAAK,CAACK,GAAG,CAACH,cAAc,CAAC;IAChD,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMW,YAAY,GAAG,MAAOC,OAAO,IAAK;EAC7C,IAAI;IACF,MAAMf,QAAQ,GAAG,MAAMJ,KAAK,CAACK,GAAG,CAAC,GAAGH,cAAc,IAAIiB,OAAO,EAAE,CAAC;IAChE,OAAOf,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,wBAAwBY,OAAO,GAAG,EAAEZ,KAAK,CAAC;IACxD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMa,gBAAgB,GAAG,MAAAA,CAAOV,IAAI,EAAES,OAAO,EAAEJ,WAAW,GAAG,EAAE,KAAK;EACzE,IAAI;IACF,MAAMX,QAAQ,GAAG,MAAMJ,KAAK,CAACgB,IAAI,CAAC,GAAGd,cAAc,QAAQ,EAAE;MAC3DQ,IAAI;MACJS,OAAO;MACPJ;IACF,CAAC,CAAC;IACF,OAAOX,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMc,yBAAyB,GAAG,MAAAA,CAAOC,KAAK,EAAEH,OAAO,EAAEJ,WAAW,GAAG,EAAE,KAAK;EACnF,IAAI;IACF,MAAMX,QAAQ,GAAG,MAAMJ,KAAK,CAACgB,IAAI,CAAC,GAAGd,cAAc,iBAAiB,EAAE;MACpEoB,KAAK;MACLH,OAAO;MACPJ;IACF,CAAC,CAAC;IACF,OAAOX,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IAC/D,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMgB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;EAC5C,IAAI;IACF,MAAMnB,QAAQ,GAAG,MAAMJ,KAAK,CAACK,GAAG,CAAC,GAAGH,cAAc,aAAa,CAAC;IAChE,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMiB,oBAAoB,GAAG,MAAOd,IAAI,IAAK;EAClD,IAAI;IACF,MAAMN,QAAQ,GAAG,MAAMJ,KAAK,CAACK,GAAG,CAAC,GAAGH,cAAc,YAAYQ,IAAI,EAAE,CAAC;IACrE,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,oCAAoCG,IAAI,GAAG,EAAEH,KAAK,CAAC;IACjE,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}