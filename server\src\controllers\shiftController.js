const shiftService = require('../services/shiftService');

/**
 * Obtiene todos los turnos predefinidos
 */
const getAllShifts = (req, res) => {
    try {
        const shifts = shiftService.getAllShifts();
        res.status(200).json(shifts);
    } catch (error) {
        console.error('Error getting all shifts:', error);
        res.status(500).json({ message: 'Error interno del servidor' });
    }
};

/**
 * Obtiene un turno específico por ID
 */
const getShiftById = (req, res) => {
    try {
        const { shiftId } = req.params;
        const shift = shiftService.getShiftById(shiftId);
        
        if (!shift) {
            return res.status(404).json({ message: 'Turno no encontrado' });
        }
        
        res.status(200).json(shift);
    } catch (error) {
        console.error('Error getting shift by ID:', error);
        res.status(500).json({ message: 'Error interno del servidor' });
    }
};

/**
 * Aplica un turno predefinido a una fecha específica
 */
const applyShiftToDate = (req, res) => {
    try {
        const { date, shiftId, description } = req.body;
        
        if (!date || !shiftId) {
            return res.status(400).json({ 
                message: 'La fecha y el ID del turno son requeridos' 
            });
        }

        const result = shiftService.applyShiftToDate(date, shiftId, description);
        res.status(200).json(result);
    } catch (error) {
        console.error('Error applying shift to date:', error);
        if (error.message.includes('Turno no encontrado')) {
            return res.status(404).json({ message: error.message });
        }
        res.status(500).json({ message: 'Error interno del servidor' });
    }
};

/**
 * Aplica un turno a múltiples fechas
 */
const applyShiftToMultipleDates = (req, res) => {
    try {
        const { dates, shiftId, description } = req.body;
        
        if (!dates || !Array.isArray(dates) || dates.length === 0 || !shiftId) {
            return res.status(400).json({ 
                message: 'Las fechas (array) y el ID del turno son requeridos' 
            });
        }

        const results = shiftService.applyShiftToMultipleDates(dates, shiftId, description);
        res.status(200).json({
            message: `Turno aplicado a ${results.length} fechas`,
            results
        });
    } catch (error) {
        console.error('Error applying shift to multiple dates:', error);
        if (error.message.includes('Turno no encontrado')) {
            return res.status(404).json({ message: error.message });
        }
        res.status(500).json({ message: 'Error interno del servidor' });
    }
};

/**
 * Obtiene estadísticas de turnos aplicados
 */
const getShiftStatistics = (req, res) => {
    try {
        const stats = shiftService.getShiftStatistics();
        res.status(200).json(stats);
    } catch (error) {
        console.error('Error getting shift statistics:', error);
        res.status(500).json({ message: 'Error interno del servidor' });
    }
};

/**
 * Sugiere turnos apropiados para una fecha
 */
const suggestShiftsForDate = (req, res) => {
    try {
        const { date } = req.params;
        
        if (!date) {
            return res.status(400).json({ 
                message: 'La fecha es requerida' 
            });
        }

        const suggestions = shiftService.suggestShiftForDate(date);
        const isWeekend = shiftService.isWeekend(date);
        
        res.status(200).json({
            date,
            isWeekend,
            suggestions
        });
    } catch (error) {
        console.error('Error suggesting shifts for date:', error);
        res.status(500).json({ message: 'Error interno del servidor' });
    }
};

module.exports = {
    getAllShifts,
    getShiftById,
    applyShiftToDate,
    applyShiftToMultipleDates,
    getShiftStatistics,
    suggestShiftsForDate
};
