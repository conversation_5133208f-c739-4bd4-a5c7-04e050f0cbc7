import React, { useState, useEffect } from 'react';
import { Row, Col, Button, Alert, Table, Badge, Modal, Form } from 'react-bootstrap';
import {
  getAllPlannedDays,
  getAllPatterns,
  applyPattern,
  deletePlannedDay,
  clearPlannedCalendar
} from '../services/api';

function PlannedCalendarView({ onDateSelect, selectedDate, onCalendarUpdate }) {
  const [plannedDays, setPlannedDays] = useState([]);
  const [patterns, setPatterns] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPatternModal, setShowPatternModal] = useState(false);

  // Form state for pattern application
  const [selectedPatternId, setSelectedPatternId] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  useEffect(() => {
    loadPlannedDays();
    loadPatterns();
  }, []);

  const loadPlannedDays = async () => {
    try {
      setLoading(true);
      const days = await getAllPlannedDays();
      setPlannedDays(days);
      setError('');
    } catch (err) {
      console.error('Error loading planned days:', err);
      setError('Error cargando días planificados: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const loadPatterns = async () => {
    try {
      const patternsData = await getAllPatterns();
      setPatterns(patternsData);
    } catch (err) {
      console.error('Error loading patterns:', err);
    }
  };

  const handleApplyPattern = async () => {
    if (!selectedPatternId || !startDate || !endDate) {
      setError('Todos los campos son obligatorios');
      return;
    }

    try {
      setLoading(true);
      setError('');

      await applyPattern(selectedPatternId, startDate, endDate);
      await loadPlannedDays();
      if (onCalendarUpdate) onCalendarUpdate();
      setShowPatternModal(false);
      resetPatternForm();
    } catch (err) {
      setError('Error aplicando patrón: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleClearCalendar = async () => {
    if (window.confirm('¿Estás seguro de que quieres limpiar todo el calendario planificado?')) {
      try {
        setLoading(true);
        await clearPlannedCalendar();
        await loadPlannedDays();
        if (onCalendarUpdate) onCalendarUpdate();
        setError('');
      } catch (err) {
        setError('Error limpiando calendario: ' + err.message);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleDeleteDay = async (dayId) => {
    if (window.confirm('¿Eliminar este día del calendario planificado?')) {
      try {
        setLoading(true);
        await deletePlannedDay(dayId);
        await loadPlannedDays();
        if (onCalendarUpdate) onCalendarUpdate();
      } catch (err) {
        setError('Error eliminando día: ' + err.message);
      } finally {
        setLoading(false);
      }
    }
  };

  const resetPatternForm = () => {
    setSelectedPatternId('');
    setStartDate('');
    setEndDate('');
  };

  const getTotalHours = () => {
    return plannedDays.reduce((total, day) => total + (day.hours || 0), 0);
  };

  return (
    <div>
      <Row className="mb-3">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <h4>Calendario Teórico (Planificado)</h4>
            <div>
              <Button
                variant="success"
                onClick={() => setShowPatternModal(true)}
                className="me-2"
                disabled={loading}
              >
                📋 Aplicar Patrón
              </Button>
              <Button
                variant="outline-danger"
                onClick={handleClearCalendar}
                disabled={loading}
              >
                🗑️ Limpiar Todo
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {error && (
        <Row className="mb-3">
          <Col>
            <Alert variant="danger" onClose={() => setError('')} dismissible>
              {error}
            </Alert>
          </Col>
        </Row>
      )}

      <Row className="mb-3">
        <Col md={8}>
          <div className="calendar-container">
            <h5>Calendario Planificado</h5>
            {loading ? (
              <p>Cargando días planificados...</p>
            ) : plannedDays.length > 0 ? (
              <Table striped bordered hover responsive>
                <thead>
                  <tr>
                    <th>Fecha</th>
                    <th>Turno</th>
                    <th>Horas</th>
                    <th>Patrón</th>
                    <th>Acciones</th>
                  </tr>
                </thead>
                <tbody>
                  {plannedDays
                    .sort((a, b) => new Date(a.date) - new Date(b.date))
                    .map(day => (
                      <tr key={day.id}>
                        <td>
                          {new Date(day.date).toLocaleDateString('es-ES', {
                            weekday: 'short',
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric'
                          })}
                        </td>
                        <td>
                          <Badge bg="info">
                            {day.shiftName || 'Sin turno'}
                          </Badge>
                        </td>
                        <td><strong>{day.hours}h</strong></td>
                        <td>
                          <small className="text-muted">
                            {day.patternName || 'Manual'}
                          </small>
                        </td>
                        <td>
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => handleDeleteDay(day.id)}
                          >
                            🗑️
                          </Button>
                        </td>
                      </tr>
                    ))}
                </tbody>
              </Table>
            ) : (
              <Alert variant="info">
                No hay días planificados. ¡Aplica un patrón para generar el calendario teórico!
              </Alert>
            )}
          </div>
        </Col>
        <Col md={4}>
          <div className="calendar-stats">
            <h5>Estadísticas Planificadas</h5>
            <div className="stat-item">
              <strong>Total de días planificados:</strong> {plannedDays.length}
            </div>
            <div className="stat-item">
              <strong>Total de horas:</strong> {getTotalHours().toFixed(1)}h
            </div>
          </div>

          <div className="pattern-info mt-3">
            <h6>Patrones disponibles:</h6>
            {patterns.length > 0 ? (
              <ul className="list-unstyled">
                {patterns.map(pattern => (
                  <li key={pattern.id} className="mb-1">
                    <small>
                      <strong>{pattern.name}</strong> ({pattern.days.length} días)
                    </small>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-muted">No hay patrones creados</p>
            )}
          </div>
        </Col>
      </Row>

      {/* Modal para aplicar patrón */}
      <Modal show={showPatternModal} onHide={() => setShowPatternModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Aplicar Patrón al Calendario Teórico</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={(e) => e.preventDefault()}>
            <Form.Group className="mb-3">
              <Form.Label>Patrón a aplicar *</Form.Label>
              <Form.Select
                value={selectedPatternId}
                onChange={(e) => setSelectedPatternId(e.target.value)}
                required
              >
                <option value="">Selecciona un patrón...</option>
                {patterns.map(pattern => (
                  <option key={pattern.id} value={pattern.id}>
                    {pattern.name} ({pattern.days.length} días, {pattern.days.reduce((total, day) => total + (day.hours || 0), 0)}h total)
                  </option>
                ))}
              </Form.Select>
              {patterns.length === 0 && (
                <Form.Text className="text-muted">
                  No hay patrones disponibles. Crea patrones en la sección de Patrones.
                </Form.Text>
              )}
            </Form.Group>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Fecha de inicio *</Form.Label>
                  <Form.Control
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Fecha de fin *</Form.Label>
                  <Form.Control
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    required
                  />
                </Form.Group>
              </Col>
            </Row>

            {selectedPatternId && patterns.find(p => p.id === selectedPatternId) && (
              <Alert variant="info">
                <h6>Vista previa del patrón seleccionado:</h6>
                <ul className="mb-0">
                  {patterns.find(p => p.id === selectedPatternId).days.map((day, index) => (
                    <li key={index}>
                      <strong>Día {day.dayNumber}:</strong> {day.shiftName} ({day.hours}h)
                    </li>
                  ))}
                </ul>
              </Alert>
            )}
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowPatternModal(false)}>
            Cancelar
          </Button>
          <Button
            variant="primary"
            onClick={handleApplyPattern}
            disabled={loading || !selectedPatternId || !startDate || !endDate}
          >
            {loading ? 'Aplicando...' : 'Aplicar Patrón'}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
}

export default PlannedCalendarView;
