{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\CalendarPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Row, Col, Tabs, Tab } from 'react-bootstrap';\nimport CalendarAnalysis from '../components/CalendarAnalysis';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CalendarPage() {\n  _s();\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('real');\n  const [calendarKey, setCalendarKey] = useState(0);\n  const handleDateSelect = date => {\n    setSelectedDate(date);\n  };\n  const handleCalendarUpdate = () => {\n    // Forzar actualización de todos los calendarios\n    setCalendarKey(prev => prev + 1);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Sistema de Horarios\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n      activeKey: activeTab,\n      onSelect: setActiveTab,\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"real\",\n        title: \"\\uD83D\\uDCC5 Calendario Real\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Calendario Real - En desarrollo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Esta secci\\xF3n estar\\xE1 disponible pronto.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"planned\",\n        title: \"\\uD83D\\uDCCB Calendario Te\\xF3rico\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Calendario Te\\xF3rico - En desarrollo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Esta secci\\xF3n estar\\xE1 disponible pronto.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"analysis\",\n        title: \"\\uD83D\\uDCCA An\\xE1lisis\",\n        children: /*#__PURE__*/_jsxDEV(CalendarAnalysis, {\n          selectedDate: selectedDate\n        }, `analysis-${calendarKey}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n}\n_s(CalendarPage, \"wzK0Ju2Td/fRUgmzeDqwaKhFGtc=\");\n_c = CalendarPage;\nexport default CalendarPage;\nvar _c;\n$RefreshReg$(_c, \"CalendarPage\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Row", "Col", "Tabs", "Tab", "CalendarAnalysis", "jsxDEV", "_jsxDEV", "CalendarPage", "_s", "selectedDate", "setSelectedDate", "Date", "activeTab", "setActiveTab", "calendarKey", "setCalendarKey", "handleDateSelect", "date", "handleCalendarUpdate", "prev", "fluid", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "active<PERSON><PERSON>", "onSelect", "eventKey", "title", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/CalendarPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Container, Row, Col, Tabs, Tab } from 'react-bootstrap';\nimport CalendarAnalysis from '../components/CalendarAnalysis';\n\nfunction CalendarPage() {\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('real');\n  const [calendarKey, setCalendarKey] = useState(0);\n\n  const handleDateSelect = (date) => {\n    setSelectedDate(date);\n  };\n\n  const handleCalendarUpdate = () => {\n    // Forzar actualización de todos los calendarios\n    setCalendarKey(prev => prev + 1);\n  };\n\n  return (\n    <Container fluid>\n      <Row className=\"mb-3\">\n        <Col>\n          <h2>Sistema de Horarios</h2>\n        </Col>\n      </Row>\n\n      <Tabs activeKey={activeTab} onSelect={setActiveTab} className=\"mb-3\">\n        <Tab eventKey=\"real\" title=\"📅 Calendario Real\">\n          <div>\n            <h4>Calendario Real - En desarrollo</h4>\n            <p>Esta sección estará disponible pronto.</p>\n          </div>\n        </Tab>\n        <Tab eventKey=\"planned\" title=\"📋 Calendario Teórico\">\n          <div>\n            <h4>Calendario Teórico - En desarrollo</h4>\n            <p>Esta sección estará disponible pronto.</p>\n          </div>\n        </Tab>\n        <Tab eventKey=\"analysis\" title=\"📊 Análisis\">\n          <CalendarAnalysis\n            key={`analysis-${calendarKey}`}\n            selectedDate={selectedDate}\n          />\n        </Tab>\n      </Tabs>\n    </Container>\n  );\n}\n\nexport default CalendarPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,QAAQ,iBAAiB;AAChE,OAAOC,gBAAgB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,IAAIa,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC;EAEjD,MAAMkB,gBAAgB,GAAIC,IAAI,IAAK;IACjCP,eAAe,CAACO,IAAI,CAAC;EACvB,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAH,cAAc,CAACI,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;EAClC,CAAC;EAED,oBACEb,OAAA,CAACP,SAAS;IAACqB,KAAK;IAAAC,QAAA,gBACdf,OAAA,CAACN,GAAG;MAACsB,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBf,OAAA,CAACL,GAAG;QAAAoB,QAAA,eACFf,OAAA;UAAAe,QAAA,EAAI;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpB,OAAA,CAACJ,IAAI;MAACyB,SAAS,EAAEf,SAAU;MAACgB,QAAQ,EAAEf,YAAa;MAACS,SAAS,EAAC,MAAM;MAAAD,QAAA,gBAClEf,OAAA,CAACH,GAAG;QAAC0B,QAAQ,EAAC,MAAM;QAACC,KAAK,EAAC,8BAAoB;QAAAT,QAAA,eAC7Cf,OAAA;UAAAe,QAAA,gBACEf,OAAA;YAAAe,QAAA,EAAI;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxCpB,OAAA;YAAAe,QAAA,EAAG;UAAsC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNpB,OAAA,CAACH,GAAG;QAAC0B,QAAQ,EAAC,SAAS;QAACC,KAAK,EAAC,oCAAuB;QAAAT,QAAA,eACnDf,OAAA;UAAAe,QAAA,gBACEf,OAAA;YAAAe,QAAA,EAAI;UAAkC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3CpB,OAAA;YAAAe,QAAA,EAAG;UAAsC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNpB,OAAA,CAACH,GAAG;QAAC0B,QAAQ,EAAC,UAAU;QAACC,KAAK,EAAC,0BAAa;QAAAT,QAAA,eAC1Cf,OAAA,CAACF,gBAAgB;UAEfK,YAAY,EAAEA;QAAa,GADtB,YAAYK,WAAW,EAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB;AAAClB,EAAA,CA5CQD,YAAY;AAAAwB,EAAA,GAAZxB,YAAY;AA8CrB,eAAeA,YAAY;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}