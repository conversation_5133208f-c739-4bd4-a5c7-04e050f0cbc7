import axios from 'axios';

const API_URL = 'http://localhost:5000/api/days';
const SHIFTS_API_URL = 'http://localhost:5000/api/shifts';
const PATTERNS_API_URL = 'http://localhost:5000/api/patterns';
const ANALYSIS_API_URL = 'http://localhost:5000/api/analysis';

export const getAllDays = async () => {
  try {
    const response = await axios.get(API_URL);
    return response.data;
  } catch (error) {
    console.error('Error fetching all days:', error);
    throw error;
  }
};

export const getDayByDate = async (date) => {
  try {
    const response = await axios.get(`${API_URL}/${date}`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.status === 404) {
      return null; // Day not found, return null instead of throwing error
    }
    console.error(`Error fetching day for ${date}:`, error);
    throw error;
  }
};

export const createOrUpdateDay = async (date, type, hours, description) => {
  try {
    const response = await axios.post(API_URL, { date, type, hours, description });
    return response.data;
  } catch (error) {
    console.error('Error creating or updating day:', error);
    throw error;
  }
};

// ===== FUNCIONES PARA TURNOS PREDEFINIDOS =====

/**
 * Obtiene todos los turnos predefinidos
 */
export const getAllShifts = async () => {
  try {
    const response = await axios.get(SHIFTS_API_URL);
    return response.data;
  } catch (error) {
    console.error('Error fetching all shifts:', error);
    throw error;
  }
};

/**
 * Obtiene un turno específico por ID
 */
export const getShiftById = async (shiftId) => {
  try {
    const response = await axios.get(`${SHIFTS_API_URL}/${shiftId}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching shift ${shiftId}:`, error);
    throw error;
  }
};

/**
 * Aplica un turno predefinido a una fecha específica
 */
export const applyShiftToDate = async (date, shiftId, description = '') => {
  try {
    const response = await axios.post(`${SHIFTS_API_URL}/apply`, {
      date,
      shiftId,
      description
    });
    return response.data;
  } catch (error) {
    console.error('Error applying shift to date:', error);
    throw error;
  }
};

/**
 * Aplica un turno a múltiples fechas
 */
export const applyShiftToMultipleDates = async (dates, shiftId, description = '') => {
  try {
    const response = await axios.post(`${SHIFTS_API_URL}/apply-multiple`, {
      dates,
      shiftId,
      description
    });
    return response.data;
  } catch (error) {
    console.error('Error applying shift to multiple dates:', error);
    throw error;
  }
};

/**
 * Obtiene estadísticas de turnos aplicados
 */
export const getShiftStatistics = async () => {
  try {
    const response = await axios.get(`${SHIFTS_API_URL}/statistics`);
    return response.data;
  } catch (error) {
    console.error('Error fetching shift statistics:', error);
    throw error;
  }
};

/**
 * Sugiere turnos apropiados para una fecha
 */
export const suggestShiftsForDate = async (date) => {
  try {
    const response = await axios.get(`${SHIFTS_API_URL}/suggest/${date}`);
    return response.data;
  } catch (error) {
    console.error(`Error suggesting shifts for date ${date}:`, error);
    throw error;
  }
};

/**
 * Aplica un patrón de días a un rango de fechas usando un ID de patrón.
 */
export const applyPattern = async (patternId, startDate, endDate) => {
  try {
    const response = await axios.post(`${API_URL}/planned/apply-pattern`, { patternId, startDate, endDate });
    return response.data;
  } catch (error) {
    console.error('Error applying pattern:', error);
    throw error;
  }
};

/**
 * Aplica un patrón de días a un rango de fechas usando un array de patrón (legacy).
 */
export const applyPatternArray = async (startDate, endDate, pattern) => {
  try {
    const response = await axios.post(`${API_URL}/apply-pattern`, { startDate, endDate, pattern });
    return response.data;
  } catch (error) {
    console.error('Error applying pattern array:', error);
    throw error;
  }
};

// ===== FUNCIONES PARA PATRONES =====

export const getAllPatterns = async () => {
  try {
    const response = await axios.get(PATTERNS_API_URL);
    return response.data;
  } catch (error) {
    console.error('Error fetching all patterns:', error);
    throw error;
  }
};

export const getPatternById = async (id) => {
  try {
    const response = await axios.get(`${PATTERNS_API_URL}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching pattern ${id}:`, error);
    throw error;
  }
};

export const createPattern = async (name, basePattern, overrides = []) => {
  try {
    const response = await axios.post(PATTERNS_API_URL, { name, basePattern, overrides });
    return response.data;
  } catch (error) {
    console.error('Error creating pattern:', error);
    throw error;
  }
};

export const updatePattern = async (id, name, basePattern, overrides = []) => {
  try {
    const response = await axios.put(`${PATTERNS_API_URL}/${id}`, { name, basePattern, overrides });
    return response.data;
  } catch (error) {
    console.error(`Error updating pattern ${id}:`, error);
    throw error;
  }
};

export const copyPattern = async (id, newName, newYear) => {
  try {
    const response = await axios.post(`${PATTERNS_API_URL}/${id}/copy`, { newName, newYear });
    return response.data;
  } catch (error) {
    console.error(`Error copying pattern ${id}:`, error);
    throw error;
  }
};

export const deletePattern = async (id) => {
  try {
    await axios.delete(`${PATTERNS_API_URL}/${id}`);
    return { message: 'Pattern deleted successfully' };
  } catch (error) {
    console.error(`Error deleting pattern ${id}:`, error);
    throw error;
  }
};

// ===== FUNCIONES PARA SISTEMA DUAL DE CALENDARIOS =====

// ***** CALENDARIO REAL *****
export const getAllRealDays = async () => {
  try {
    const response = await axios.get(`${API_URL}/real`);
    return response.data;
  } catch (error) {
    console.error('Error fetching all real days:', error);
    throw error;
  }
};

export const getRealDayByDate = async (date) => {
  try {
    const response = await axios.get(`${API_URL}/real/${date}`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.status === 404) {
      return null;
    }
    console.error(`Error fetching real day for ${date}:`, error);
    throw error;
  }
};

export const createOrUpdateRealDay = async (dayData) => {
  try {
    const response = await axios.post(`${API_URL}/real`, dayData);
    return response.data;
  } catch (error) {
    console.error('Error creating or updating real day:', error);
    throw error;
  }
};

export const deleteRealDay = async (dayId) => {
  try {
    await axios.delete(`${API_URL}/real/${dayId}`);
    return { message: 'Real day deleted successfully' };
  } catch (error) {
    console.error(`Error deleting real day ${dayId}:`, error);
    throw error;
  }
};

// ***** CALENDARIO PLANIFICADO *****
export const getAllPlannedDays = async () => {
  try {
    const response = await axios.get(`${API_URL}/planned`);
    return response.data;
  } catch (error) {
    console.error('Error fetching all planned days:', error);
    throw error;
  }
};

export const getPlannedDayByDate = async (date) => {
  try {
    const response = await axios.get(`${API_URL}/planned/${date}`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.status === 404) {
      return null;
    }
    console.error(`Error fetching planned day for ${date}:`, error);
    throw error;
  }
};

export const clearPlannedCalendar = async () => {
  try {
    const response = await axios.delete(`${API_URL}/planned`);
    return response.data;
  } catch (error) {
    console.error('Error clearing planned calendar:', error);
    throw error;
  }
};

export const deletePlannedDay = async (dayId) => {
  try {
    await axios.delete(`${API_URL}/planned/${dayId}`);
    return { message: 'Planned day deleted successfully' };
  } catch (error) {
    console.error(`Error deleting planned day ${dayId}:`, error);
    throw error;
  }
};

// ***** ANÁLISIS Y COMPARACIÓN *****
export const compareCalendars = async (startDate, endDate) => {
  try {
    const response = await axios.get(`${ANALYSIS_API_URL}/compare`, {
      params: { startDate, endDate }
    });
    return response.data;
  } catch (error) {
    console.error('Error comparing calendars:', error);
    throw error;
  }
};

export const getComplianceStats = async (startDate, endDate) => {
  try {
    const response = await axios.get(`${ANALYSIS_API_URL}/compliance`, {
      params: { startDate, endDate }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching compliance stats:', error);
    throw error;
  }
};

export const getVarianceAnalysis = async (startDate, endDate) => {
  try {
    const response = await axios.get(`${ANALYSIS_API_URL}/variance`, {
      params: { startDate, endDate }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching variance analysis:', error);
    throw error;
  }
};
