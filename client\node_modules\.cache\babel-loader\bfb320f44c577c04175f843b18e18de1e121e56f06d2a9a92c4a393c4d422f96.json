{"ast": null, "code": "/**\n * react-router-dom v7.6.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n\"use strict\";\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from)) if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {\n      get: () => from[key],\n      enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable\n    });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\nvar __toCommonJS = mod => __copyProps(__defProp({}, \"__esModule\", {\n  value: true\n}), mod);\n\n// index.ts\nvar react_router_dom_exports = {};\n__export(react_router_dom_exports, {\n  HydratedRouter: () => import_dom.HydratedRouter,\n  RouterProvider: () => import_dom.RouterProvider\n});\nmodule.exports = __toCommonJS(react_router_dom_exports);\nvar import_dom = require(\"react-router/dom\");\n__reExport(react_router_dom_exports, require(\"react-router\"), module.exports);\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  HydratedRouter,\n  RouterProvider,\n  ...require(\"react-router\")\n});", "map": {"version": 3, "names": ["__defProp", "Object", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__hasOwnProp", "prototype", "hasOwnProperty", "__export", "target", "all", "name", "get", "enumerable", "__copyProps", "to", "from", "except", "desc", "key", "call", "__reExport", "mod", "second<PERSON><PERSON><PERSON>", "__toCommonJS", "value", "react_router_dom_exports", "HydratedRouter", "import_dom", "RouterProvider", "module", "exports", "require"], "sources": ["D:/Proyectos Python/Horario/client/node_modules/react-router-dom/dist/index.js"], "sourcesContent": ["/**\n * react-router-dom v7.6.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n\"use strict\";\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// index.ts\nvar react_router_dom_exports = {};\n__export(react_router_dom_exports, {\n  HydratedRouter: () => import_dom.HydratedRouter,\n  RouterProvider: () => import_dom.RouterProvider\n});\nmodule.exports = __toCommonJS(react_router_dom_exports);\nvar import_dom = require(\"react-router/dom\");\n__reExport(react_router_dom_exports, require(\"react-router\"), module.exports);\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  HydratedRouter,\n  RouterProvider,\n  ...require(\"react-router\")\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AACZ,IAAIA,SAAS,GAAGC,MAAM,CAACC,cAAc;AACrC,IAAIC,gBAAgB,GAAGF,MAAM,CAACG,wBAAwB;AACtD,IAAIC,iBAAiB,GAAGJ,MAAM,CAACK,mBAAmB;AAClD,IAAIC,YAAY,GAAGN,MAAM,CAACO,SAAS,CAACC,cAAc;AAClD,IAAIC,QAAQ,GAAGA,CAACC,MAAM,EAAEC,GAAG,KAAK;EAC9B,KAAK,IAAIC,IAAI,IAAID,GAAG,EAClBZ,SAAS,CAACW,MAAM,EAAEE,IAAI,EAAE;IAAEC,GAAG,EAAEF,GAAG,CAACC,IAAI,CAAC;IAAEE,UAAU,EAAE;EAAK,CAAC,CAAC;AACjE,CAAC;AACD,IAAIC,WAAW,GAAGA,CAACC,EAAE,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,KAAK;EAC5C,IAAIF,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;IAClE,KAAK,IAAIG,GAAG,IAAIhB,iBAAiB,CAACa,IAAI,CAAC,EACrC,IAAI,CAACX,YAAY,CAACe,IAAI,CAACL,EAAE,EAAEI,GAAG,CAAC,IAAIA,GAAG,KAAKF,MAAM,EAC/CnB,SAAS,CAACiB,EAAE,EAAEI,GAAG,EAAE;MAAEP,GAAG,EAAEA,CAAA,KAAMI,IAAI,CAACG,GAAG,CAAC;MAAEN,UAAU,EAAE,EAAEK,IAAI,GAAGjB,gBAAgB,CAACe,IAAI,EAAEG,GAAG,CAAC,CAAC,IAAID,IAAI,CAACL;IAAW,CAAC,CAAC;EACxH;EACA,OAAOE,EAAE;AACX,CAAC;AACD,IAAIM,UAAU,GAAGA,CAACZ,MAAM,EAAEa,GAAG,EAAEC,YAAY,MAAMT,WAAW,CAACL,MAAM,EAAEa,GAAG,EAAE,SAAS,CAAC,EAAEC,YAAY,IAAIT,WAAW,CAACS,YAAY,EAAED,GAAG,EAAE,SAAS,CAAC,CAAC;AAChJ,IAAIE,YAAY,GAAIF,GAAG,IAAKR,WAAW,CAAChB,SAAS,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE;EAAE2B,KAAK,EAAE;AAAK,CAAC,CAAC,EAAEH,GAAG,CAAC;;AAE1F;AACA,IAAII,wBAAwB,GAAG,CAAC,CAAC;AACjClB,QAAQ,CAACkB,wBAAwB,EAAE;EACjCC,cAAc,EAAEA,CAAA,KAAMC,UAAU,CAACD,cAAc;EAC/CE,cAAc,EAAEA,CAAA,KAAMD,UAAU,CAACC;AACnC,CAAC,CAAC;AACFC,MAAM,CAACC,OAAO,GAAGP,YAAY,CAACE,wBAAwB,CAAC;AACvD,IAAIE,UAAU,GAAGI,OAAO,CAAC,kBAAkB,CAAC;AAC5CX,UAAU,CAACK,wBAAwB,EAAEM,OAAO,CAAC,cAAc,CAAC,EAAEF,MAAM,CAACC,OAAO,CAAC;AAC7E;AACA,CAAC,KAAKD,MAAM,CAACC,OAAO,GAAG;EACrBJ,cAAc;EACdE,cAAc;EACd,GAAGG,OAAO,CAAC,cAAc;AAC3B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}