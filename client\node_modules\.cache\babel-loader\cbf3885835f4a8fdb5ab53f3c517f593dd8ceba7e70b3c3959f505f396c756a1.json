{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\components\\\\RealCalendarManager.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Modal, Form, Button, Alert, Row, Col, Card, Badge } from 'react-bootstrap';\nimport { createOrUpdateRealDay, getRealDayByDate, getAllShifts, applyShiftToDate, suggestShiftsForDate } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction RealCalendarManager({\n  show,\n  onHide,\n  selectedDate,\n  onDayUpdated\n}) {\n  _s();\n  const [dayType, setDayType] = useState('');\n  const [hours, setHours] = useState(0);\n  const [description, setDescription] = useState('');\n  const [startTime, setStartTime] = useState('');\n  const [endTime, setEndTime] = useState('');\n  const [selectedShift, setSelectedShift] = useState('');\n  const [useShift, setUseShift] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [suggestedShifts, setSuggestedShifts] = useState([]);\n  const [existingDay, setExistingDay] = useState(null);\n  useEffect(() => {\n    if (show) {\n      loadAvailableShifts();\n      if (selectedDate) {\n        loadExistingDay();\n        loadSuggestedShifts();\n      }\n    }\n  }, [show, selectedDate]);\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (err) {\n      console.error('Error loading shifts:', err);\n    }\n  };\n  const loadExistingDay = async () => {\n    if (!selectedDate) return;\n    try {\n      const dateStr = selectedDate.toISOString().split('T')[0];\n      const day = await getRealDayByDate(dateStr);\n      if (day) {\n        setExistingDay(day);\n        setDayType(day.type || '');\n        setHours(day.hours || 0);\n        setDescription(day.description || '');\n        if (day.shiftId) {\n          setSelectedShift(day.shiftId);\n          setUseShift(true);\n          setStartTime(day.startTime || '');\n          setEndTime(day.endTime || '');\n        } else {\n          setStartTime('');\n          setEndTime('');\n        }\n      } else {\n        resetForm();\n      }\n    } catch (err) {\n      console.error('Error loading existing day:', err);\n      resetForm();\n    }\n  };\n  const loadSuggestedShifts = async () => {\n    if (!selectedDate) return;\n    try {\n      const dateStr = selectedDate.toISOString().split('T')[0];\n      const suggestions = await suggestShiftsForDate(dateStr);\n      setSuggestedShifts(suggestions);\n    } catch (err) {\n      console.error('Error loading suggested shifts:', err);\n    }\n  };\n  const resetForm = () => {\n    setExistingDay(null);\n    setDayType('');\n    setHours(0);\n    setDescription('');\n    setSelectedShift('');\n    setUseShift(false);\n    setStartTime('');\n    setEndTime('');\n    setError('');\n  };\n  const handleShiftSelect = shift => {\n    setSelectedShift(shift.id);\n    setUseShift(true);\n    setDayType('worked');\n    setHours(shift.totalHours); // Use totalHours from shift\n    setDescription(`${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`);\n    setStartTime(shift.startTime);\n    setEndTime(shift.endTime);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const dateStr = selectedDate.toISOString().split('T')[0];\n      if (useShift && selectedShift) {\n        // Usar turno predefinido\n        await applyShiftToDate(dateStr, selectedShift, description);\n      } else {\n        // Entrada manual\n        await createOrUpdateRealDay(dateStr, dayType, hours, description);\n      }\n      if (onDayUpdated) {\n        onDayUpdated();\n      }\n      onHide();\n    } catch (err) {\n      setError('Error guardando el día: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatDate = date => {\n    return date === null || date === void 0 ? void 0 : date.toLocaleDateString('es-ES', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    show: show,\n    onHide: onHide,\n    size: \"lg\",\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n        children: [existingDay ? 'Editar' : 'Registrar', \" D\\xEDa Real\", selectedDate && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-muted fs-6 mt-1\",\n          children: formatDate(selectedDate)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        onSubmit: handleSubmit,\n        children: [suggestedShifts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Turnos Sugeridos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: suggestedShifts.map(shift => /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              className: \"mb-2\",\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: `shift-suggestion-card ${selectedShift === shift.id ? 'selected' : ''}`,\n                onClick: () => handleShiftSelect(shift),\n                children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                  className: \"p-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: shift.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 175,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-muted small\",\n                        children: [shift.startTime, \" - \", shift.endTime]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 176,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"primary\",\n                      children: [shift.hours, \"h\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 21\n              }, this)\n            }, shift.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: /*#__PURE__*/_jsxDEV(Form.Check, {\n            type: \"checkbox\",\n            id: \"useShift\",\n            label: \"Usar turno predefinido\",\n            checked: useShift,\n            onChange: e => setUseShift(e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), useShift && /*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Turno\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n            value: selectedShift,\n            onChange: e => setSelectedShift(e.target.value),\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Selecciona un turno\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), availableShifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: shift.id,\n              children: [shift.name, \" (\", shift.startTime, \" - \", shift.endTime, \") - \", shift.hours, \"h\"]\n            }, shift.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this), !useShift && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Tipo de d\\xEDa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: dayType,\n              onChange: e => setDayType(e.target.value),\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Selecciona el tipo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"worked\",\n                children: \"Trabajado\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"rest\",\n                children: \"Descanso\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"vacation\",\n                children: \"Vacaciones\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"sick\",\n                children: \"Baja m\\xE9dica\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"holiday\",\n                children: \"Festivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Horas trabajadas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"number\",\n              step: \"0.5\",\n              min: \"0\",\n              max: \"24\",\n              value: hours,\n              onChange: e => setHours(parseFloat(e.target.value) || 0),\n              required: dayType === 'worked'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Descripci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n            as: \"textarea\",\n            rows: 3,\n            value: description,\n            onChange: e => setDescription(e.target.value),\n            placeholder: \"Descripci\\xF3n opcional del d\\xEDa...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this), existingDay && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"info\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"D\\xEDa existente:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), \" \", existingDay.type, \" - \", existingDay.hours, \"h\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            children: existingDay.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: onHide,\n        children: \"Cancelar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: handleSubmit,\n        disabled: loading || !useShift && !dayType,\n        children: loading ? 'Guardando...' : existingDay ? 'Actualizar' : 'Guardar'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n}\n_s(RealCalendarManager, \"4/O0F/pDvQn/+wZEeL8FItqoQCI=\");\n_c = RealCalendarManager;\nexport default RealCalendarManager;\nvar _c;\n$RefreshReg$(_c, \"RealCalendarManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Modal", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Row", "Col", "Card", "Badge", "createOrUpdateRealDay", "getRealDayByDate", "getAllShifts", "applyShiftToDate", "suggestShiftsForDate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RealCalendarManager", "show", "onHide", "selectedDate", "onDayUpdated", "_s", "dayType", "setDayType", "hours", "setHours", "description", "setDescription", "startTime", "setStartTime", "endTime", "setEndTime", "selectedShift", "setSelectedShift", "useShift", "setUseShift", "loading", "setLoading", "error", "setError", "availableShifts", "setAvailableShifts", "suggestedShifts", "setSuggestedShifts", "existingDay", "setExistingDay", "loadAvailableShifts", "loadExistingDay", "loadSuggestedShifts", "shifts", "err", "console", "dateStr", "toISOString", "split", "day", "type", "shiftId", "resetForm", "suggestions", "handleShiftSelect", "shift", "id", "totalHours", "name", "breakMinutes", "handleSubmit", "e", "preventDefault", "message", "formatDate", "date", "toLocaleDateString", "weekday", "year", "month", "size", "children", "Header", "closeButton", "Title", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "variant", "onSubmit", "length", "map", "md", "onClick", "bg", "Group", "Check", "label", "checked", "onChange", "target", "Label", "Select", "value", "required", "Control", "step", "min", "max", "parseFloat", "as", "rows", "placeholder", "Footer", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/components/RealCalendarManager.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Modal, Form, Button, Alert, Row, Col, Card, Badge } from 'react-bootstrap';\nimport {\n  createOrUpdateRealDay,\n  getRealDayByDate,\n  getAllShifts,\n  applyShiftToDate,\n  suggestShiftsForDate\n} from '../services/api';\n\nfunction RealCalendarManager({ show, onHide, selectedDate, onDayUpdated }) {\n  const [dayType, setDayType] = useState('');\n  const [hours, setHours] = useState(0);\n  const [description, setDescription] = useState('');\n  const [startTime, setStartTime] = useState('');\n  const [endTime, setEndTime] = useState('');\n  const [selectedShift, setSelectedShift] = useState('');\n  const [useShift, setUseShift] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [suggestedShifts, setSuggestedShifts] = useState([]);\n  const [existingDay, setExistingDay] = useState(null);\n\n  useEffect(() => {\n    if (show) {\n      loadAvailableShifts();\n      if (selectedDate) {\n        loadExistingDay();\n        loadSuggestedShifts();\n      }\n    }\n  }, [show, selectedDate]);\n\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (err) {\n      console.error('Error loading shifts:', err);\n    }\n  };\n\n  const loadExistingDay = async () => {\n    if (!selectedDate) return;\n    \n    try {\n      const dateStr = selectedDate.toISOString().split('T')[0];\n      const day = await getRealDayByDate(dateStr);\n      \n      if (day) {\n        setExistingDay(day);\n        setDayType(day.type || '');\n        setHours(day.hours || 0);\n        setDescription(day.description || '');\n        if (day.shiftId) {\n          setSelectedShift(day.shiftId);\n          setUseShift(true);\n          setStartTime(day.startTime || '');\n          setEndTime(day.endTime || '');\n        } else {\n          setStartTime('');\n          setEndTime('');\n        }\n      } else {\n        resetForm();\n      }\n    } catch (err) {\n      console.error('Error loading existing day:', err);\n      resetForm();\n    }\n  };\n\n  const loadSuggestedShifts = async () => {\n    if (!selectedDate) return;\n    \n    try {\n      const dateStr = selectedDate.toISOString().split('T')[0];\n      const suggestions = await suggestShiftsForDate(dateStr);\n      setSuggestedShifts(suggestions);\n    } catch (err) {\n      console.error('Error loading suggested shifts:', err);\n    }\n  };\n\n  const resetForm = () => {\n    setExistingDay(null);\n    setDayType('');\n    setHours(0);\n    setDescription('');\n    setSelectedShift('');\n    setUseShift(false);\n    setStartTime('');\n    setEndTime('');\n    setError('');\n  };\n\n  const handleShiftSelect = (shift) => {\n    setSelectedShift(shift.id);\n    setUseShift(true);\n    setDayType('worked');\n    setHours(shift.totalHours); // Use totalHours from shift\n    setDescription(`${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`);\n    setStartTime(shift.startTime);\n    setEndTime(shift.endTime);\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const dateStr = selectedDate.toISOString().split('T')[0];\n      \n      if (useShift && selectedShift) {\n        // Usar turno predefinido\n        await applyShiftToDate(dateStr, selectedShift, description);\n      } else {\n        // Entrada manual\n        await createOrUpdateRealDay(dateStr, dayType, hours, description);\n      }\n\n      if (onDayUpdated) {\n        onDayUpdated();\n      }\n      onHide();\n    } catch (err) {\n      setError('Error guardando el día: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatDate = (date) => {\n    return date?.toLocaleDateString('es-ES', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  return (\n    <Modal show={show} onHide={onHide} size=\"lg\">\n      <Modal.Header closeButton>\n        <Modal.Title>\n          {existingDay ? 'Editar' : 'Registrar'} Día Real\n          {selectedDate && (\n            <div className=\"text-muted fs-6 mt-1\">\n              {formatDate(selectedDate)}\n            </div>\n          )}\n        </Modal.Title>\n      </Modal.Header>\n\n      <Modal.Body>\n        {error && <Alert variant=\"danger\">{error}</Alert>}\n\n        <Form onSubmit={handleSubmit}>\n          {/* Sección de turnos sugeridos */}\n          {suggestedShifts.length > 0 && (\n            <div className=\"mb-4\">\n              <h6>Turnos Sugeridos</h6>\n              <Row>\n                {suggestedShifts.map((shift) => (\n                  <Col md={6} key={shift.id} className=\"mb-2\">\n                    <Card \n                      className={`shift-suggestion-card ${selectedShift === shift.id ? 'selected' : ''}`}\n                      onClick={() => handleShiftSelect(shift)}\n                    >\n                      <Card.Body className=\"p-2\">\n                        <div className=\"d-flex justify-content-between align-items-center\">\n                          <div>\n                            <strong>{shift.name}</strong>\n                            <div className=\"text-muted small\">\n                              {shift.startTime} - {shift.endTime}\n                            </div>\n                          </div>\n                          <Badge bg=\"primary\">{shift.hours}h</Badge>\n                        </div>\n                      </Card.Body>\n                    </Card>\n                  </Col>\n                ))}\n              </Row>\n            </div>\n          )}\n\n          {/* Opción de usar turno predefinido */}\n          <Form.Group className=\"mb-3\">\n            <Form.Check\n              type=\"checkbox\"\n              id=\"useShift\"\n              label=\"Usar turno predefinido\"\n              checked={useShift}\n              onChange={(e) => setUseShift(e.target.checked)}\n            />\n          </Form.Group>\n\n          {useShift && (\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Turno</Form.Label>\n              <Form.Select\n                value={selectedShift}\n                onChange={(e) => setSelectedShift(e.target.value)}\n                required\n              >\n                <option value=\"\">Selecciona un turno</option>\n                {availableShifts.map((shift) => (\n                  <option key={shift.id} value={shift.id}>\n                    {shift.name} ({shift.startTime} - {shift.endTime}) - {shift.hours}h\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n          )}\n\n          {!useShift && (\n            <>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Tipo de día</Form.Label>\n                <Form.Select\n                  value={dayType}\n                  onChange={(e) => setDayType(e.target.value)}\n                  required\n                >\n                  <option value=\"\">Selecciona el tipo</option>\n                  <option value=\"worked\">Trabajado</option>\n                  <option value=\"rest\">Descanso</option>\n                  <option value=\"vacation\">Vacaciones</option>\n                  <option value=\"sick\">Baja médica</option>\n                  <option value=\"holiday\">Festivo</option>\n                </Form.Select>\n              </Form.Group>\n\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Horas trabajadas</Form.Label>\n                <Form.Control\n                  type=\"number\"\n                  step=\"0.5\"\n                  min=\"0\"\n                  max=\"24\"\n                  value={hours}\n                  onChange={(e) => setHours(parseFloat(e.target.value) || 0)}\n                  required={dayType === 'worked'}\n                />\n              </Form.Group>\n            </>\n          )}\n\n          <Form.Group className=\"mb-3\">\n            <Form.Label>Descripción</Form.Label>\n            <Form.Control\n              as=\"textarea\"\n              rows={3}\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              placeholder=\"Descripción opcional del día...\"\n            />\n          </Form.Group>\n\n          {existingDay && (\n            <Alert variant=\"info\">\n              <strong>Día existente:</strong> {existingDay.type} - {existingDay.hours}h\n              <br />\n              <small>{existingDay.description}</small>\n            </Alert>\n          )}\n        </Form>\n      </Modal.Body>\n\n      <Modal.Footer>\n        <Button variant=\"secondary\" onClick={onHide}>\n          Cancelar\n        </Button>\n        <Button \n          variant=\"primary\" \n          onClick={handleSubmit}\n          disabled={loading || (!useShift && !dayType)}\n        >\n          {loading ? 'Guardando...' : (existingDay ? 'Actualizar' : 'Guardar')}\n        </Button>\n      </Modal.Footer>\n    </Modal>\n  );\n}\n\nexport default RealCalendarManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,QAAQ,iBAAiB;AACnF,SACEC,qBAAqB,EACrBC,gBAAgB,EAChBC,YAAY,EACZC,gBAAgB,EAChBC,oBAAoB,QACf,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,SAASC,mBAAmBA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,YAAY;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EACzE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACd,IAAImB,IAAI,EAAE;MACR6B,mBAAmB,CAAC,CAAC;MACrB,IAAI3B,YAAY,EAAE;QAChB4B,eAAe,CAAC,CAAC;QACjBC,mBAAmB,CAAC,CAAC;MACvB;IACF;EACF,CAAC,EAAE,CAAC/B,IAAI,EAAEE,YAAY,CAAC,CAAC;EAExB,MAAM2B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMG,MAAM,GAAG,MAAMxC,YAAY,CAAC,CAAC;MACnCgC,kBAAkB,CAACQ,MAAM,CAAC;IAC5B,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACb,KAAK,CAAC,uBAAuB,EAAEY,GAAG,CAAC;IAC7C;EACF,CAAC;EAED,MAAMH,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAAC5B,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMiC,OAAO,GAAGjC,YAAY,CAACkC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACxD,MAAMC,GAAG,GAAG,MAAM/C,gBAAgB,CAAC4C,OAAO,CAAC;MAE3C,IAAIG,GAAG,EAAE;QACPV,cAAc,CAACU,GAAG,CAAC;QACnBhC,UAAU,CAACgC,GAAG,CAACC,IAAI,IAAI,EAAE,CAAC;QAC1B/B,QAAQ,CAAC8B,GAAG,CAAC/B,KAAK,IAAI,CAAC,CAAC;QACxBG,cAAc,CAAC4B,GAAG,CAAC7B,WAAW,IAAI,EAAE,CAAC;QACrC,IAAI6B,GAAG,CAACE,OAAO,EAAE;UACfxB,gBAAgB,CAACsB,GAAG,CAACE,OAAO,CAAC;UAC7BtB,WAAW,CAAC,IAAI,CAAC;UACjBN,YAAY,CAAC0B,GAAG,CAAC3B,SAAS,IAAI,EAAE,CAAC;UACjCG,UAAU,CAACwB,GAAG,CAACzB,OAAO,IAAI,EAAE,CAAC;QAC/B,CAAC,MAAM;UACLD,YAAY,CAAC,EAAE,CAAC;UAChBE,UAAU,CAAC,EAAE,CAAC;QAChB;MACF,CAAC,MAAM;QACL2B,SAAS,CAAC,CAAC;MACb;IACF,CAAC,CAAC,OAAOR,GAAG,EAAE;MACZC,OAAO,CAACb,KAAK,CAAC,6BAA6B,EAAEY,GAAG,CAAC;MACjDQ,SAAS,CAAC,CAAC;IACb;EACF,CAAC;EAED,MAAMV,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAAC7B,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMiC,OAAO,GAAGjC,YAAY,CAACkC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACxD,MAAMK,WAAW,GAAG,MAAMhD,oBAAoB,CAACyC,OAAO,CAAC;MACvDT,kBAAkB,CAACgB,WAAW,CAAC;IACjC,CAAC,CAAC,OAAOT,GAAG,EAAE;MACZC,OAAO,CAACb,KAAK,CAAC,iCAAiC,EAAEY,GAAG,CAAC;IACvD;EACF,CAAC;EAED,MAAMQ,SAAS,GAAGA,CAAA,KAAM;IACtBb,cAAc,CAAC,IAAI,CAAC;IACpBtB,UAAU,CAAC,EAAE,CAAC;IACdE,QAAQ,CAAC,CAAC,CAAC;IACXE,cAAc,CAAC,EAAE,CAAC;IAClBM,gBAAgB,CAAC,EAAE,CAAC;IACpBE,WAAW,CAAC,KAAK,CAAC;IAClBN,YAAY,CAAC,EAAE,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IACdQ,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMqB,iBAAiB,GAAIC,KAAK,IAAK;IACnC5B,gBAAgB,CAAC4B,KAAK,CAACC,EAAE,CAAC;IAC1B3B,WAAW,CAAC,IAAI,CAAC;IACjBZ,UAAU,CAAC,QAAQ,CAAC;IACpBE,QAAQ,CAACoC,KAAK,CAACE,UAAU,CAAC,CAAC,CAAC;IAC5BpC,cAAc,CAAC,GAAGkC,KAAK,CAACG,IAAI,KAAKH,KAAK,CAACjC,SAAS,MAAMiC,KAAK,CAAC/B,OAAO,KAAK+B,KAAK,CAACI,YAAY,eAAe,CAAC;IAC1GpC,YAAY,CAACgC,KAAK,CAACjC,SAAS,CAAC;IAC7BG,UAAU,CAAC8B,KAAK,CAAC/B,OAAO,CAAC;EAC3B,CAAC;EAED,MAAMoC,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB/B,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMa,OAAO,GAAGjC,YAAY,CAACkC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAExD,IAAIpB,QAAQ,IAAIF,aAAa,EAAE;QAC7B;QACA,MAAMtB,gBAAgB,CAAC0C,OAAO,EAAEpB,aAAa,EAAEN,WAAW,CAAC;MAC7D,CAAC,MAAM;QACL;QACA,MAAMnB,qBAAqB,CAAC6C,OAAO,EAAE9B,OAAO,EAAEE,KAAK,EAAEE,WAAW,CAAC;MACnE;MAEA,IAAIN,YAAY,EAAE;QAChBA,YAAY,CAAC,CAAC;MAChB;MACAF,MAAM,CAAC,CAAC;IACV,CAAC,CAAC,OAAOgC,GAAG,EAAE;MACZX,QAAQ,CAAC,0BAA0B,GAAGW,GAAG,CAACmB,OAAO,CAAC;IACpD,CAAC,SAAS;MACRhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiC,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAOA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,kBAAkB,CAAC,OAAO,EAAE;MACvCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbpB,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,oBACE1C,OAAA,CAACd,KAAK;IAACkB,IAAI,EAAEA,IAAK;IAACC,MAAM,EAAEA,MAAO;IAAC0D,IAAI,EAAC,IAAI;IAAAC,QAAA,gBAC1ChE,OAAA,CAACd,KAAK,CAAC+E,MAAM;MAACC,WAAW;MAAAF,QAAA,eACvBhE,OAAA,CAACd,KAAK,CAACiF,KAAK;QAAAH,QAAA,GACTjC,WAAW,GAAG,QAAQ,GAAG,WAAW,EAAC,cACtC,EAACzB,YAAY,iBACXN,OAAA;UAAKoE,SAAS,EAAC,sBAAsB;UAAAJ,QAAA,EAClCP,UAAU,CAACnD,YAAY;QAAC;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEfxE,OAAA,CAACd,KAAK,CAACuF,IAAI;MAAAT,QAAA,GACRvC,KAAK,iBAAIzB,OAAA,CAACX,KAAK;QAACqF,OAAO,EAAC,QAAQ;QAAAV,QAAA,EAAEvC;MAAK;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAEjDxE,OAAA,CAACb,IAAI;QAACwF,QAAQ,EAAEtB,YAAa;QAAAW,QAAA,GAE1BnC,eAAe,CAAC+C,MAAM,GAAG,CAAC,iBACzB5E,OAAA;UAAKoE,SAAS,EAAC,MAAM;UAAAJ,QAAA,gBACnBhE,OAAA;YAAAgE,QAAA,EAAI;UAAgB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBxE,OAAA,CAACV,GAAG;YAAA0E,QAAA,EACDnC,eAAe,CAACgD,GAAG,CAAE7B,KAAK,iBACzBhD,OAAA,CAACT,GAAG;cAACuF,EAAE,EAAE,CAAE;cAAgBV,SAAS,EAAC,MAAM;cAAAJ,QAAA,eACzChE,OAAA,CAACR,IAAI;gBACH4E,SAAS,EAAE,yBAAyBjD,aAAa,KAAK6B,KAAK,CAACC,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;gBACnF8B,OAAO,EAAEA,CAAA,KAAMhC,iBAAiB,CAACC,KAAK,CAAE;gBAAAgB,QAAA,eAExChE,OAAA,CAACR,IAAI,CAACiF,IAAI;kBAACL,SAAS,EAAC,KAAK;kBAAAJ,QAAA,eACxBhE,OAAA;oBAAKoE,SAAS,EAAC,mDAAmD;oBAAAJ,QAAA,gBAChEhE,OAAA;sBAAAgE,QAAA,gBACEhE,OAAA;wBAAAgE,QAAA,EAAShB,KAAK,CAACG;sBAAI;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,eAC7BxE,OAAA;wBAAKoE,SAAS,EAAC,kBAAkB;wBAAAJ,QAAA,GAC9BhB,KAAK,CAACjC,SAAS,EAAC,KAAG,EAACiC,KAAK,CAAC/B,OAAO;sBAAA;wBAAAoD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNxE,OAAA,CAACP,KAAK;sBAACuF,EAAE,EAAC,SAAS;sBAAAhB,QAAA,GAAEhB,KAAK,CAACrC,KAAK,EAAC,GAAC;oBAAA;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC,GAhBQxB,KAAK,CAACC,EAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBpB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDxE,OAAA,CAACb,IAAI,CAAC8F,KAAK;UAACb,SAAS,EAAC,MAAM;UAAAJ,QAAA,eAC1BhE,OAAA,CAACb,IAAI,CAAC+F,KAAK;YACTvC,IAAI,EAAC,UAAU;YACfM,EAAE,EAAC,UAAU;YACbkC,KAAK,EAAC,wBAAwB;YAC9BC,OAAO,EAAE/D,QAAS;YAClBgE,QAAQ,EAAG/B,CAAC,IAAKhC,WAAW,CAACgC,CAAC,CAACgC,MAAM,CAACF,OAAO;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,EAEZnD,QAAQ,iBACPrB,OAAA,CAACb,IAAI,CAAC8F,KAAK;UAACb,SAAS,EAAC,MAAM;UAAAJ,QAAA,gBAC1BhE,OAAA,CAACb,IAAI,CAACoG,KAAK;YAAAvB,QAAA,EAAC;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9BxE,OAAA,CAACb,IAAI,CAACqG,MAAM;YACVC,KAAK,EAAEtE,aAAc;YACrBkE,QAAQ,EAAG/B,CAAC,IAAKlC,gBAAgB,CAACkC,CAAC,CAACgC,MAAM,CAACG,KAAK,CAAE;YAClDC,QAAQ;YAAA1B,QAAA,gBAERhE,OAAA;cAAQyF,KAAK,EAAC,EAAE;cAAAzB,QAAA,EAAC;YAAmB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAC5C7C,eAAe,CAACkD,GAAG,CAAE7B,KAAK,iBACzBhD,OAAA;cAAuByF,KAAK,EAAEzC,KAAK,CAACC,EAAG;cAAAe,QAAA,GACpChB,KAAK,CAACG,IAAI,EAAC,IAAE,EAACH,KAAK,CAACjC,SAAS,EAAC,KAAG,EAACiC,KAAK,CAAC/B,OAAO,EAAC,MAAI,EAAC+B,KAAK,CAACrC,KAAK,EAAC,GACpE;YAAA,GAFaqC,KAAK,CAACC,EAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACb,EAEA,CAACnD,QAAQ,iBACRrB,OAAA,CAAAE,SAAA;UAAA8D,QAAA,gBACEhE,OAAA,CAACb,IAAI,CAAC8F,KAAK;YAACb,SAAS,EAAC,MAAM;YAAAJ,QAAA,gBAC1BhE,OAAA,CAACb,IAAI,CAACoG,KAAK;cAAAvB,QAAA,EAAC;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpCxE,OAAA,CAACb,IAAI,CAACqG,MAAM;cACVC,KAAK,EAAEhF,OAAQ;cACf4E,QAAQ,EAAG/B,CAAC,IAAK5C,UAAU,CAAC4C,CAAC,CAACgC,MAAM,CAACG,KAAK,CAAE;cAC5CC,QAAQ;cAAA1B,QAAA,gBAERhE,OAAA;gBAAQyF,KAAK,EAAC,EAAE;gBAAAzB,QAAA,EAAC;cAAkB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CxE,OAAA;gBAAQyF,KAAK,EAAC,QAAQ;gBAAAzB,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzCxE,OAAA;gBAAQyF,KAAK,EAAC,MAAM;gBAAAzB,QAAA,EAAC;cAAQ;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCxE,OAAA;gBAAQyF,KAAK,EAAC,UAAU;gBAAAzB,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CxE,OAAA;gBAAQyF,KAAK,EAAC,MAAM;gBAAAzB,QAAA,EAAC;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzCxE,OAAA;gBAAQyF,KAAK,EAAC,SAAS;gBAAAzB,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEbxE,OAAA,CAACb,IAAI,CAAC8F,KAAK;YAACb,SAAS,EAAC,MAAM;YAAAJ,QAAA,gBAC1BhE,OAAA,CAACb,IAAI,CAACoG,KAAK;cAAAvB,QAAA,EAAC;YAAgB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACzCxE,OAAA,CAACb,IAAI,CAACwG,OAAO;cACXhD,IAAI,EAAC,QAAQ;cACbiD,IAAI,EAAC,KAAK;cACVC,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC,IAAI;cACRL,KAAK,EAAE9E,KAAM;cACb0E,QAAQ,EAAG/B,CAAC,IAAK1C,QAAQ,CAACmF,UAAU,CAACzC,CAAC,CAACgC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,CAAE;cAC3DC,QAAQ,EAAEjF,OAAO,KAAK;YAAS;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA,eACb,CACH,eAEDxE,OAAA,CAACb,IAAI,CAAC8F,KAAK;UAACb,SAAS,EAAC,MAAM;UAAAJ,QAAA,gBAC1BhE,OAAA,CAACb,IAAI,CAACoG,KAAK;YAAAvB,QAAA,EAAC;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACpCxE,OAAA,CAACb,IAAI,CAACwG,OAAO;YACXK,EAAE,EAAC,UAAU;YACbC,IAAI,EAAE,CAAE;YACRR,KAAK,EAAE5E,WAAY;YACnBwE,QAAQ,EAAG/B,CAAC,IAAKxC,cAAc,CAACwC,CAAC,CAACgC,MAAM,CAACG,KAAK,CAAE;YAChDS,WAAW,EAAC;UAAiC;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,EAEZzC,WAAW,iBACV/B,OAAA,CAACX,KAAK;UAACqF,OAAO,EAAC,MAAM;UAAAV,QAAA,gBACnBhE,OAAA;YAAAgE,QAAA,EAAQ;UAAc;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACzC,WAAW,CAACY,IAAI,EAAC,KAAG,EAACZ,WAAW,CAACpB,KAAK,EAAC,GACxE,eAAAX,OAAA;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxE,OAAA;YAAAgE,QAAA,EAAQjC,WAAW,CAAClB;UAAW;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEbxE,OAAA,CAACd,KAAK,CAACiH,MAAM;MAAAnC,QAAA,gBACXhE,OAAA,CAACZ,MAAM;QAACsF,OAAO,EAAC,WAAW;QAACK,OAAO,EAAE1E,MAAO;QAAA2D,QAAA,EAAC;MAE7C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxE,OAAA,CAACZ,MAAM;QACLsF,OAAO,EAAC,SAAS;QACjBK,OAAO,EAAE1B,YAAa;QACtB+C,QAAQ,EAAE7E,OAAO,IAAK,CAACF,QAAQ,IAAI,CAACZ,OAAS;QAAAuD,QAAA,EAE5CzC,OAAO,GAAG,cAAc,GAAIQ,WAAW,GAAG,YAAY,GAAG;MAAU;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEZ;AAAChE,EAAA,CApRQL,mBAAmB;AAAAkG,EAAA,GAAnBlG,mBAAmB;AAsR5B,eAAeA,mBAAmB;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}