{"ast": null, "code": "/**\n * react-router v7.6.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n\"use strict\";\n\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from)) if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {\n      get: () => from[key],\n      enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable\n    });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n// If the importer is in node compatibility mode or this is not an ESM\n// file that has been converted to a CommonJS file using a Babel-\n// compatible transform (i.e. \"__esModule\" has not been set), then set\n// \"default\" to the CommonJS \"module.exports\" for node compatibility.\nisNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", {\n  value: mod,\n  enumerable: true\n}) : target, mod));\nvar __toCommonJS = mod => __copyProps(__defProp({}, \"__esModule\", {\n  value: true\n}), mod);\n\n// dom-export.ts\nvar dom_export_exports = {};\n__export(dom_export_exports, {\n  HydratedRouter: () => HydratedRouter,\n  RouterProvider: () => RouterProvider\n});\nmodule.exports = __toCommonJS(dom_export_exports);\n\n// lib/dom-export/dom-router-provider.tsx\nvar React = __toESM(require(\"react\"));\nvar ReactDOM = __toESM(require(\"react-dom\"));\nvar import_react_router = require(\"react-router\");\nfunction RouterProvider(props) {\n  return /* @__PURE__ */React.createElement(import_react_router.RouterProvider, {\n    flushSync: ReactDOM.flushSync,\n    ...props\n  });\n}\n\n// lib/dom-export/hydrated-router.tsx\nvar React2 = __toESM(require(\"react\"));\nvar import_react_router2 = require(\"react-router\");\nvar ssrInfo = null;\nvar router = null;\nfunction initSsrInfo() {\n  if (!ssrInfo && window.__reactRouterContext && window.__reactRouterManifest && window.__reactRouterRouteModules) {\n    if (window.__reactRouterManifest.sri === true) {\n      const importMap = document.querySelector(\"script[rr-importmap]\");\n      if (importMap?.textContent) {\n        try {\n          window.__reactRouterManifest.sri = JSON.parse(importMap.textContent).integrity;\n        } catch (err) {\n          console.error(\"Failed to parse import map\", err);\n        }\n      }\n    }\n    ssrInfo = {\n      context: window.__reactRouterContext,\n      manifest: window.__reactRouterManifest,\n      routeModules: window.__reactRouterRouteModules,\n      stateDecodingPromise: void 0,\n      router: void 0,\n      routerInitialized: false\n    };\n  }\n}\nfunction createHydratedRouter({\n  unstable_getContext\n}) {\n  initSsrInfo();\n  if (!ssrInfo) {\n    throw new Error(\"You must be using the SSR features of React Router in order to skip passing a `router` prop to `<RouterProvider>`\");\n  }\n  let localSsrInfo = ssrInfo;\n  if (!ssrInfo.stateDecodingPromise) {\n    let stream = ssrInfo.context.stream;\n    (0, import_react_router2.UNSAFE_invariant)(stream, \"No stream found for single fetch decoding\");\n    ssrInfo.context.stream = void 0;\n    ssrInfo.stateDecodingPromise = (0, import_react_router2.UNSAFE_decodeViaTurboStream)(stream, window).then(value => {\n      ssrInfo.context.state = value.value;\n      localSsrInfo.stateDecodingPromise.value = true;\n    }).catch(e => {\n      localSsrInfo.stateDecodingPromise.error = e;\n    });\n  }\n  if (ssrInfo.stateDecodingPromise.error) {\n    throw ssrInfo.stateDecodingPromise.error;\n  }\n  if (!ssrInfo.stateDecodingPromise.value) {\n    throw ssrInfo.stateDecodingPromise;\n  }\n  let routes = (0, import_react_router2.UNSAFE_createClientRoutes)(ssrInfo.manifest.routes, ssrInfo.routeModules, ssrInfo.context.state, ssrInfo.context.ssr, ssrInfo.context.isSpaMode);\n  let hydrationData = void 0;\n  if (ssrInfo.context.isSpaMode) {\n    let {\n      loaderData\n    } = ssrInfo.context.state;\n    if (ssrInfo.manifest.routes.root?.hasLoader && loaderData && \"root\" in loaderData) {\n      hydrationData = {\n        loaderData: {\n          root: loaderData.root\n        }\n      };\n    }\n  } else {\n    hydrationData = (0, import_react_router2.UNSAFE_getHydrationData)(ssrInfo.context.state, routes, routeId => ({\n      clientLoader: ssrInfo.routeModules[routeId]?.clientLoader,\n      hasLoader: ssrInfo.manifest.routes[routeId]?.hasLoader === true,\n      hasHydrateFallback: ssrInfo.routeModules[routeId]?.HydrateFallback != null\n    }), window.location, window.__reactRouterContext?.basename, ssrInfo.context.isSpaMode);\n    if (hydrationData && hydrationData.errors) {\n      hydrationData.errors = (0, import_react_router2.UNSAFE_deserializeErrors)(hydrationData.errors);\n    }\n  }\n  let router2 = (0, import_react_router2.UNSAFE_createRouter)({\n    routes,\n    history: (0, import_react_router2.UNSAFE_createBrowserHistory)(),\n    basename: ssrInfo.context.basename,\n    unstable_getContext,\n    hydrationData,\n    hydrationRouteProperties: import_react_router2.UNSAFE_hydrationRouteProperties,\n    mapRouteProperties: import_react_router2.UNSAFE_mapRouteProperties,\n    future: {\n      unstable_middleware: ssrInfo.context.future.unstable_middleware\n    },\n    dataStrategy: (0, import_react_router2.UNSAFE_getTurboStreamSingleFetchDataStrategy)(() => router2, ssrInfo.manifest, ssrInfo.routeModules, ssrInfo.context.ssr, ssrInfo.context.basename),\n    patchRoutesOnNavigation: (0, import_react_router2.UNSAFE_getPatchRoutesOnNavigationFunction)(ssrInfo.manifest, ssrInfo.routeModules, ssrInfo.context.ssr, ssrInfo.context.routeDiscovery, ssrInfo.context.isSpaMode, ssrInfo.context.basename)\n  });\n  ssrInfo.router = router2;\n  if (router2.state.initialized) {\n    ssrInfo.routerInitialized = true;\n    router2.initialize();\n  }\n  router2.createRoutesForHMR = /* spacer so ts-ignore does not affect the right hand of the assignment */\n  import_react_router2.UNSAFE_createClientRoutesWithHMRRevalidationOptOut;\n  window.__reactRouterDataRouter = router2;\n  return router2;\n}\nfunction HydratedRouter(props) {\n  if (!router) {\n    router = createHydratedRouter({\n      unstable_getContext: props.unstable_getContext\n    });\n  }\n  let [criticalCss, setCriticalCss] = React2.useState(process.env.NODE_ENV === \"development\" ? ssrInfo?.context.criticalCss : void 0);\n  if (process.env.NODE_ENV === \"development\") {\n    if (ssrInfo) {\n      window.__reactRouterClearCriticalCss = () => setCriticalCss(void 0);\n    }\n  }\n  let [location, setLocation] = React2.useState(router.state.location);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router && !ssrInfo.routerInitialized) {\n      ssrInfo.routerInitialized = true;\n      ssrInfo.router.initialize();\n    }\n  }, []);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router) {\n      return ssrInfo.router.subscribe(newState => {\n        if (newState.location !== location) {\n          setLocation(newState.location);\n        }\n      });\n    }\n  }, [location]);\n  (0, import_react_router2.UNSAFE_invariant)(ssrInfo, \"ssrInfo unavailable for HydratedRouter\");\n  (0, import_react_router2.UNSAFE_useFogOFWarDiscovery)(router, ssrInfo.manifest, ssrInfo.routeModules, ssrInfo.context.ssr, ssrInfo.context.routeDiscovery, ssrInfo.context.isSpaMode);\n  return (\n    // This fragment is important to ensure we match the <ServerRouter> JSX\n    // structure so that useId values hydrate correctly\n    /* @__PURE__ */\n    React2.createElement(React2.Fragment, null, /* @__PURE__ */React2.createElement(import_react_router2.UNSAFE_FrameworkContext.Provider, {\n      value: {\n        manifest: ssrInfo.manifest,\n        routeModules: ssrInfo.routeModules,\n        future: ssrInfo.context.future,\n        criticalCss,\n        ssr: ssrInfo.context.ssr,\n        isSpaMode: ssrInfo.context.isSpaMode,\n        routeDiscovery: ssrInfo.context.routeDiscovery\n      }\n    }, /* @__PURE__ */React2.createElement(import_react_router2.UNSAFE_RemixErrorBoundary, {\n      location\n    }, /* @__PURE__ */React2.createElement(RouterProvider, {\n      router\n    }))), /* @__PURE__ */React2.createElement(React2.Fragment, null))\n  );\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  HydratedRouter,\n  RouterProvider\n});", "map": {"version": 3, "names": ["__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__export", "target", "all", "name", "get", "enumerable", "__copyProps", "to", "from", "except", "desc", "key", "call", "__toESM", "mod", "isNodeMode", "__esModule", "value", "__toCommonJS", "dom_export_exports", "HydratedRouter", "RouterProvider", "module", "exports", "React", "require", "ReactDOM", "import_react_router", "props", "createElement", "flushSync", "React2", "import_react_router2", "ssrInfo", "router", "initSsrInfo", "window", "__reactRouterContext", "__reactRouterManifest", "__reactRouterRouteModules", "sri", "importMap", "document", "querySelector", "textContent", "JSON", "parse", "integrity", "err", "console", "error", "context", "manifest", "routeModules", "stateDecodingPromise", "routerInitialized", "createHydratedRouter", "unstable_getContext", "Error", "localSsrInfo", "stream", "UNSAFE_invariant", "UNSAFE_decodeViaTurboStream", "then", "state", "catch", "e", "routes", "UNSAFE_createClientRoutes", "ssr", "isSpaMode", "hydrationData", "loaderData", "root", "<PERSON><PERSON><PERSON><PERSON>", "UNSAFE_getHydrationData", "routeId", "clientLoader", "hasHydrateFallback", "HydrateFallback", "location", "basename", "errors", "UNSAFE_deserializeErrors", "router2", "UNSAFE_createRouter", "history", "UNSAFE_createBrowserHistory", "hydrationRouteProperties", "UNSAFE_hydrationRouteProperties", "mapRouteProperties", "UNSAFE_mapRouteProperties", "future", "unstable_middleware", "dataStrategy", "UNSAFE_getTurboStreamSingleFetchDataStrategy", "patchRoutesOnNavigation", "UNSAFE_getPatchRoutesOnNavigationFunction", "routeDiscovery", "initialized", "initialize", "createRoutesForHMR", "UNSAFE_createClientRoutesWithHMRRevalidationOptOut", "__reactRouterDataRouter", "criticalCss", "setCriticalCss", "useState", "process", "env", "NODE_ENV", "__reactRouterClearCriticalCss", "setLocation", "useLayoutEffect", "subscribe", "newState", "UNSAFE_useFogOFWarDiscovery", "Fragment", "UNSAFE_FrameworkContext", "Provider", "UNSAFE_RemixErrorBoundary"], "sources": ["D:/Proyectos Python/Horario/client/node_modules/react-router/dist/development/dom-export.js"], "sourcesContent": ["/**\n * react-router v7.6.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n\"use strict\";\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// dom-export.ts\nvar dom_export_exports = {};\n__export(dom_export_exports, {\n  HydratedRouter: () => HydratedRouter,\n  RouterProvider: () => RouterProvider\n});\nmodule.exports = __toCommonJS(dom_export_exports);\n\n// lib/dom-export/dom-router-provider.tsx\nvar React = __toESM(require(\"react\"));\nvar ReactDOM = __toESM(require(\"react-dom\"));\nvar import_react_router = require(\"react-router\");\nfunction RouterProvider(props) {\n  return /* @__PURE__ */ React.createElement(import_react_router.RouterProvider, { flushSync: ReactDOM.flushSync, ...props });\n}\n\n// lib/dom-export/hydrated-router.tsx\nvar React2 = __toESM(require(\"react\"));\nvar import_react_router2 = require(\"react-router\");\nvar ssrInfo = null;\nvar router = null;\nfunction initSsrInfo() {\n  if (!ssrInfo && window.__reactRouterContext && window.__reactRouterManifest && window.__reactRouterRouteModules) {\n    if (window.__reactRouterManifest.sri === true) {\n      const importMap = document.querySelector(\"script[rr-importmap]\");\n      if (importMap?.textContent) {\n        try {\n          window.__reactRouterManifest.sri = JSON.parse(\n            importMap.textContent\n          ).integrity;\n        } catch (err) {\n          console.error(\"Failed to parse import map\", err);\n        }\n      }\n    }\n    ssrInfo = {\n      context: window.__reactRouterContext,\n      manifest: window.__reactRouterManifest,\n      routeModules: window.__reactRouterRouteModules,\n      stateDecodingPromise: void 0,\n      router: void 0,\n      routerInitialized: false\n    };\n  }\n}\nfunction createHydratedRouter({\n  unstable_getContext\n}) {\n  initSsrInfo();\n  if (!ssrInfo) {\n    throw new Error(\n      \"You must be using the SSR features of React Router in order to skip passing a `router` prop to `<RouterProvider>`\"\n    );\n  }\n  let localSsrInfo = ssrInfo;\n  if (!ssrInfo.stateDecodingPromise) {\n    let stream = ssrInfo.context.stream;\n    (0, import_react_router2.UNSAFE_invariant)(stream, \"No stream found for single fetch decoding\");\n    ssrInfo.context.stream = void 0;\n    ssrInfo.stateDecodingPromise = (0, import_react_router2.UNSAFE_decodeViaTurboStream)(stream, window).then((value) => {\n      ssrInfo.context.state = value.value;\n      localSsrInfo.stateDecodingPromise.value = true;\n    }).catch((e) => {\n      localSsrInfo.stateDecodingPromise.error = e;\n    });\n  }\n  if (ssrInfo.stateDecodingPromise.error) {\n    throw ssrInfo.stateDecodingPromise.error;\n  }\n  if (!ssrInfo.stateDecodingPromise.value) {\n    throw ssrInfo.stateDecodingPromise;\n  }\n  let routes = (0, import_react_router2.UNSAFE_createClientRoutes)(\n    ssrInfo.manifest.routes,\n    ssrInfo.routeModules,\n    ssrInfo.context.state,\n    ssrInfo.context.ssr,\n    ssrInfo.context.isSpaMode\n  );\n  let hydrationData = void 0;\n  if (ssrInfo.context.isSpaMode) {\n    let { loaderData } = ssrInfo.context.state;\n    if (ssrInfo.manifest.routes.root?.hasLoader && loaderData && \"root\" in loaderData) {\n      hydrationData = {\n        loaderData: {\n          root: loaderData.root\n        }\n      };\n    }\n  } else {\n    hydrationData = (0, import_react_router2.UNSAFE_getHydrationData)(\n      ssrInfo.context.state,\n      routes,\n      (routeId) => ({\n        clientLoader: ssrInfo.routeModules[routeId]?.clientLoader,\n        hasLoader: ssrInfo.manifest.routes[routeId]?.hasLoader === true,\n        hasHydrateFallback: ssrInfo.routeModules[routeId]?.HydrateFallback != null\n      }),\n      window.location,\n      window.__reactRouterContext?.basename,\n      ssrInfo.context.isSpaMode\n    );\n    if (hydrationData && hydrationData.errors) {\n      hydrationData.errors = (0, import_react_router2.UNSAFE_deserializeErrors)(hydrationData.errors);\n    }\n  }\n  let router2 = (0, import_react_router2.UNSAFE_createRouter)({\n    routes,\n    history: (0, import_react_router2.UNSAFE_createBrowserHistory)(),\n    basename: ssrInfo.context.basename,\n    unstable_getContext,\n    hydrationData,\n    hydrationRouteProperties: import_react_router2.UNSAFE_hydrationRouteProperties,\n    mapRouteProperties: import_react_router2.UNSAFE_mapRouteProperties,\n    future: {\n      unstable_middleware: ssrInfo.context.future.unstable_middleware\n    },\n    dataStrategy: (0, import_react_router2.UNSAFE_getTurboStreamSingleFetchDataStrategy)(\n      () => router2,\n      ssrInfo.manifest,\n      ssrInfo.routeModules,\n      ssrInfo.context.ssr,\n      ssrInfo.context.basename\n    ),\n    patchRoutesOnNavigation: (0, import_react_router2.UNSAFE_getPatchRoutesOnNavigationFunction)(\n      ssrInfo.manifest,\n      ssrInfo.routeModules,\n      ssrInfo.context.ssr,\n      ssrInfo.context.routeDiscovery,\n      ssrInfo.context.isSpaMode,\n      ssrInfo.context.basename\n    )\n  });\n  ssrInfo.router = router2;\n  if (router2.state.initialized) {\n    ssrInfo.routerInitialized = true;\n    router2.initialize();\n  }\n  router2.createRoutesForHMR = /* spacer so ts-ignore does not affect the right hand of the assignment */\n  import_react_router2.UNSAFE_createClientRoutesWithHMRRevalidationOptOut;\n  window.__reactRouterDataRouter = router2;\n  return router2;\n}\nfunction HydratedRouter(props) {\n  if (!router) {\n    router = createHydratedRouter({\n      unstable_getContext: props.unstable_getContext\n    });\n  }\n  let [criticalCss, setCriticalCss] = React2.useState(\n    process.env.NODE_ENV === \"development\" ? ssrInfo?.context.criticalCss : void 0\n  );\n  if (process.env.NODE_ENV === \"development\") {\n    if (ssrInfo) {\n      window.__reactRouterClearCriticalCss = () => setCriticalCss(void 0);\n    }\n  }\n  let [location, setLocation] = React2.useState(router.state.location);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router && !ssrInfo.routerInitialized) {\n      ssrInfo.routerInitialized = true;\n      ssrInfo.router.initialize();\n    }\n  }, []);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router) {\n      return ssrInfo.router.subscribe((newState) => {\n        if (newState.location !== location) {\n          setLocation(newState.location);\n        }\n      });\n    }\n  }, [location]);\n  (0, import_react_router2.UNSAFE_invariant)(ssrInfo, \"ssrInfo unavailable for HydratedRouter\");\n  (0, import_react_router2.UNSAFE_useFogOFWarDiscovery)(\n    router,\n    ssrInfo.manifest,\n    ssrInfo.routeModules,\n    ssrInfo.context.ssr,\n    ssrInfo.context.routeDiscovery,\n    ssrInfo.context.isSpaMode\n  );\n  return (\n    // This fragment is important to ensure we match the <ServerRouter> JSX\n    // structure so that useId values hydrate correctly\n    /* @__PURE__ */ React2.createElement(React2.Fragment, null, /* @__PURE__ */ React2.createElement(\n      import_react_router2.UNSAFE_FrameworkContext.Provider,\n      {\n        value: {\n          manifest: ssrInfo.manifest,\n          routeModules: ssrInfo.routeModules,\n          future: ssrInfo.context.future,\n          criticalCss,\n          ssr: ssrInfo.context.ssr,\n          isSpaMode: ssrInfo.context.isSpaMode,\n          routeDiscovery: ssrInfo.context.routeDiscovery\n        }\n      },\n      /* @__PURE__ */ React2.createElement(import_react_router2.UNSAFE_RemixErrorBoundary, { location }, /* @__PURE__ */ React2.createElement(RouterProvider, { router }))\n    ), /* @__PURE__ */ React2.createElement(React2.Fragment, null))\n  );\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  HydratedRouter,\n  RouterProvider\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AACZ,IAAIA,QAAQ,GAAGC,MAAM,CAACC,MAAM;AAC5B,IAAIC,SAAS,GAAGF,MAAM,CAACG,cAAc;AACrC,IAAIC,gBAAgB,GAAGJ,MAAM,CAACK,wBAAwB;AACtD,IAAIC,iBAAiB,GAAGN,MAAM,CAACO,mBAAmB;AAClD,IAAIC,YAAY,GAAGR,MAAM,CAACS,cAAc;AACxC,IAAIC,YAAY,GAAGV,MAAM,CAACW,SAAS,CAACC,cAAc;AAClD,IAAIC,QAAQ,GAAGA,CAACC,MAAM,EAAEC,GAAG,KAAK;EAC9B,KAAK,IAAIC,IAAI,IAAID,GAAG,EAClBb,SAAS,CAACY,MAAM,EAAEE,IAAI,EAAE;IAAEC,GAAG,EAAEF,GAAG,CAACC,IAAI,CAAC;IAAEE,UAAU,EAAE;EAAK,CAAC,CAAC;AACjE,CAAC;AACD,IAAIC,WAAW,GAAGA,CAACC,EAAE,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,KAAK;EAC5C,IAAIF,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;IAClE,KAAK,IAAIG,GAAG,IAAIlB,iBAAiB,CAACe,IAAI,CAAC,EACrC,IAAI,CAACX,YAAY,CAACe,IAAI,CAACL,EAAE,EAAEI,GAAG,CAAC,IAAIA,GAAG,KAAKF,MAAM,EAC/CpB,SAAS,CAACkB,EAAE,EAAEI,GAAG,EAAE;MAAEP,GAAG,EAAEA,CAAA,KAAMI,IAAI,CAACG,GAAG,CAAC;MAAEN,UAAU,EAAE,EAAEK,IAAI,GAAGnB,gBAAgB,CAACiB,IAAI,EAAEG,GAAG,CAAC,CAAC,IAAID,IAAI,CAACL;IAAW,CAAC,CAAC;EACxH;EACA,OAAOE,EAAE;AACX,CAAC;AACD,IAAIM,OAAO,GAAGA,CAACC,GAAG,EAAEC,UAAU,EAAEd,MAAM,MAAMA,MAAM,GAAGa,GAAG,IAAI,IAAI,GAAG5B,QAAQ,CAACS,YAAY,CAACmB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAER,WAAW;AAC9G;AACA;AACA;AACA;AACAS,UAAU,IAAI,CAACD,GAAG,IAAI,CAACA,GAAG,CAACE,UAAU,GAAG3B,SAAS,CAACY,MAAM,EAAE,SAAS,EAAE;EAAEgB,KAAK,EAAEH,GAAG;EAAET,UAAU,EAAE;AAAK,CAAC,CAAC,GAAGJ,MAAM,EAC/Ga,GACF,CAAC,CAAC;AACF,IAAII,YAAY,GAAIJ,GAAG,IAAKR,WAAW,CAACjB,SAAS,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE;EAAE4B,KAAK,EAAE;AAAK,CAAC,CAAC,EAAEH,GAAG,CAAC;;AAE1F;AACA,IAAIK,kBAAkB,GAAG,CAAC,CAAC;AAC3BnB,QAAQ,CAACmB,kBAAkB,EAAE;EAC3BC,cAAc,EAAEA,CAAA,KAAMA,cAAc;EACpCC,cAAc,EAAEA,CAAA,KAAMA;AACxB,CAAC,CAAC;AACFC,MAAM,CAACC,OAAO,GAAGL,YAAY,CAACC,kBAAkB,CAAC;;AAEjD;AACA,IAAIK,KAAK,GAAGX,OAAO,CAACY,OAAO,CAAC,OAAO,CAAC,CAAC;AACrC,IAAIC,QAAQ,GAAGb,OAAO,CAACY,OAAO,CAAC,WAAW,CAAC,CAAC;AAC5C,IAAIE,mBAAmB,GAAGF,OAAO,CAAC,cAAc,CAAC;AACjD,SAASJ,cAAcA,CAACO,KAAK,EAAE;EAC7B,OAAO,eAAgBJ,KAAK,CAACK,aAAa,CAACF,mBAAmB,CAACN,cAAc,EAAE;IAAES,SAAS,EAAEJ,QAAQ,CAACI,SAAS;IAAE,GAAGF;EAAM,CAAC,CAAC;AAC7H;;AAEA;AACA,IAAIG,MAAM,GAAGlB,OAAO,CAACY,OAAO,CAAC,OAAO,CAAC,CAAC;AACtC,IAAIO,oBAAoB,GAAGP,OAAO,CAAC,cAAc,CAAC;AAClD,IAAIQ,OAAO,GAAG,IAAI;AAClB,IAAIC,MAAM,GAAG,IAAI;AACjB,SAASC,WAAWA,CAAA,EAAG;EACrB,IAAI,CAACF,OAAO,IAAIG,MAAM,CAACC,oBAAoB,IAAID,MAAM,CAACE,qBAAqB,IAAIF,MAAM,CAACG,yBAAyB,EAAE;IAC/G,IAAIH,MAAM,CAACE,qBAAqB,CAACE,GAAG,KAAK,IAAI,EAAE;MAC7C,MAAMC,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC;MAChE,IAAIF,SAAS,EAAEG,WAAW,EAAE;QAC1B,IAAI;UACFR,MAAM,CAACE,qBAAqB,CAACE,GAAG,GAAGK,IAAI,CAACC,KAAK,CAC3CL,SAAS,CAACG,WACZ,CAAC,CAACG,SAAS;QACb,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZC,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC;QAClD;MACF;IACF;IACAf,OAAO,GAAG;MACRkB,OAAO,EAAEf,MAAM,CAACC,oBAAoB;MACpCe,QAAQ,EAAEhB,MAAM,CAACE,qBAAqB;MACtCe,YAAY,EAAEjB,MAAM,CAACG,yBAAyB;MAC9Ce,oBAAoB,EAAE,KAAK,CAAC;MAC5BpB,MAAM,EAAE,KAAK,CAAC;MACdqB,iBAAiB,EAAE;IACrB,CAAC;EACH;AACF;AACA,SAASC,oBAAoBA,CAAC;EAC5BC;AACF,CAAC,EAAE;EACDtB,WAAW,CAAC,CAAC;EACb,IAAI,CAACF,OAAO,EAAE;IACZ,MAAM,IAAIyB,KAAK,CACb,mHACF,CAAC;EACH;EACA,IAAIC,YAAY,GAAG1B,OAAO;EAC1B,IAAI,CAACA,OAAO,CAACqB,oBAAoB,EAAE;IACjC,IAAIM,MAAM,GAAG3B,OAAO,CAACkB,OAAO,CAACS,MAAM;IACnC,CAAC,CAAC,EAAE5B,oBAAoB,CAAC6B,gBAAgB,EAAED,MAAM,EAAE,2CAA2C,CAAC;IAC/F3B,OAAO,CAACkB,OAAO,CAACS,MAAM,GAAG,KAAK,CAAC;IAC/B3B,OAAO,CAACqB,oBAAoB,GAAG,CAAC,CAAC,EAAEtB,oBAAoB,CAAC8B,2BAA2B,EAAEF,MAAM,EAAExB,MAAM,CAAC,CAAC2B,IAAI,CAAE9C,KAAK,IAAK;MACnHgB,OAAO,CAACkB,OAAO,CAACa,KAAK,GAAG/C,KAAK,CAACA,KAAK;MACnC0C,YAAY,CAACL,oBAAoB,CAACrC,KAAK,GAAG,IAAI;IAChD,CAAC,CAAC,CAACgD,KAAK,CAAEC,CAAC,IAAK;MACdP,YAAY,CAACL,oBAAoB,CAACJ,KAAK,GAAGgB,CAAC;IAC7C,CAAC,CAAC;EACJ;EACA,IAAIjC,OAAO,CAACqB,oBAAoB,CAACJ,KAAK,EAAE;IACtC,MAAMjB,OAAO,CAACqB,oBAAoB,CAACJ,KAAK;EAC1C;EACA,IAAI,CAACjB,OAAO,CAACqB,oBAAoB,CAACrC,KAAK,EAAE;IACvC,MAAMgB,OAAO,CAACqB,oBAAoB;EACpC;EACA,IAAIa,MAAM,GAAG,CAAC,CAAC,EAAEnC,oBAAoB,CAACoC,yBAAyB,EAC7DnC,OAAO,CAACmB,QAAQ,CAACe,MAAM,EACvBlC,OAAO,CAACoB,YAAY,EACpBpB,OAAO,CAACkB,OAAO,CAACa,KAAK,EACrB/B,OAAO,CAACkB,OAAO,CAACkB,GAAG,EACnBpC,OAAO,CAACkB,OAAO,CAACmB,SAClB,CAAC;EACD,IAAIC,aAAa,GAAG,KAAK,CAAC;EAC1B,IAAItC,OAAO,CAACkB,OAAO,CAACmB,SAAS,EAAE;IAC7B,IAAI;MAAEE;IAAW,CAAC,GAAGvC,OAAO,CAACkB,OAAO,CAACa,KAAK;IAC1C,IAAI/B,OAAO,CAACmB,QAAQ,CAACe,MAAM,CAACM,IAAI,EAAEC,SAAS,IAAIF,UAAU,IAAI,MAAM,IAAIA,UAAU,EAAE;MACjFD,aAAa,GAAG;QACdC,UAAU,EAAE;UACVC,IAAI,EAAED,UAAU,CAACC;QACnB;MACF,CAAC;IACH;EACF,CAAC,MAAM;IACLF,aAAa,GAAG,CAAC,CAAC,EAAEvC,oBAAoB,CAAC2C,uBAAuB,EAC9D1C,OAAO,CAACkB,OAAO,CAACa,KAAK,EACrBG,MAAM,EACLS,OAAO,KAAM;MACZC,YAAY,EAAE5C,OAAO,CAACoB,YAAY,CAACuB,OAAO,CAAC,EAAEC,YAAY;MACzDH,SAAS,EAAEzC,OAAO,CAACmB,QAAQ,CAACe,MAAM,CAACS,OAAO,CAAC,EAAEF,SAAS,KAAK,IAAI;MAC/DI,kBAAkB,EAAE7C,OAAO,CAACoB,YAAY,CAACuB,OAAO,CAAC,EAAEG,eAAe,IAAI;IACxE,CAAC,CAAC,EACF3C,MAAM,CAAC4C,QAAQ,EACf5C,MAAM,CAACC,oBAAoB,EAAE4C,QAAQ,EACrChD,OAAO,CAACkB,OAAO,CAACmB,SAClB,CAAC;IACD,IAAIC,aAAa,IAAIA,aAAa,CAACW,MAAM,EAAE;MACzCX,aAAa,CAACW,MAAM,GAAG,CAAC,CAAC,EAAElD,oBAAoB,CAACmD,wBAAwB,EAAEZ,aAAa,CAACW,MAAM,CAAC;IACjG;EACF;EACA,IAAIE,OAAO,GAAG,CAAC,CAAC,EAAEpD,oBAAoB,CAACqD,mBAAmB,EAAE;IAC1DlB,MAAM;IACNmB,OAAO,EAAE,CAAC,CAAC,EAAEtD,oBAAoB,CAACuD,2BAA2B,EAAE,CAAC;IAChEN,QAAQ,EAAEhD,OAAO,CAACkB,OAAO,CAAC8B,QAAQ;IAClCxB,mBAAmB;IACnBc,aAAa;IACbiB,wBAAwB,EAAExD,oBAAoB,CAACyD,+BAA+B;IAC9EC,kBAAkB,EAAE1D,oBAAoB,CAAC2D,yBAAyB;IAClEC,MAAM,EAAE;MACNC,mBAAmB,EAAE5D,OAAO,CAACkB,OAAO,CAACyC,MAAM,CAACC;IAC9C,CAAC;IACDC,YAAY,EAAE,CAAC,CAAC,EAAE9D,oBAAoB,CAAC+D,4CAA4C,EACjF,MAAMX,OAAO,EACbnD,OAAO,CAACmB,QAAQ,EAChBnB,OAAO,CAACoB,YAAY,EACpBpB,OAAO,CAACkB,OAAO,CAACkB,GAAG,EACnBpC,OAAO,CAACkB,OAAO,CAAC8B,QAClB,CAAC;IACDe,uBAAuB,EAAE,CAAC,CAAC,EAAEhE,oBAAoB,CAACiE,yCAAyC,EACzFhE,OAAO,CAACmB,QAAQ,EAChBnB,OAAO,CAACoB,YAAY,EACpBpB,OAAO,CAACkB,OAAO,CAACkB,GAAG,EACnBpC,OAAO,CAACkB,OAAO,CAAC+C,cAAc,EAC9BjE,OAAO,CAACkB,OAAO,CAACmB,SAAS,EACzBrC,OAAO,CAACkB,OAAO,CAAC8B,QAClB;EACF,CAAC,CAAC;EACFhD,OAAO,CAACC,MAAM,GAAGkD,OAAO;EACxB,IAAIA,OAAO,CAACpB,KAAK,CAACmC,WAAW,EAAE;IAC7BlE,OAAO,CAACsB,iBAAiB,GAAG,IAAI;IAChC6B,OAAO,CAACgB,UAAU,CAAC,CAAC;EACtB;EACAhB,OAAO,CAACiB,kBAAkB,GAAG;EAC7BrE,oBAAoB,CAACsE,kDAAkD;EACvElE,MAAM,CAACmE,uBAAuB,GAAGnB,OAAO;EACxC,OAAOA,OAAO;AAChB;AACA,SAAShE,cAAcA,CAACQ,KAAK,EAAE;EAC7B,IAAI,CAACM,MAAM,EAAE;IACXA,MAAM,GAAGsB,oBAAoB,CAAC;MAC5BC,mBAAmB,EAAE7B,KAAK,CAAC6B;IAC7B,CAAC,CAAC;EACJ;EACA,IAAI,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAG1E,MAAM,CAAC2E,QAAQ,CACjDC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,GAAG5E,OAAO,EAAEkB,OAAO,CAACqD,WAAW,GAAG,KAAK,CAC/E,CAAC;EACD,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1C,IAAI5E,OAAO,EAAE;MACXG,MAAM,CAAC0E,6BAA6B,GAAG,MAAML,cAAc,CAAC,KAAK,CAAC,CAAC;IACrE;EACF;EACA,IAAI,CAACzB,QAAQ,EAAE+B,WAAW,CAAC,GAAGhF,MAAM,CAAC2E,QAAQ,CAACxE,MAAM,CAAC8B,KAAK,CAACgB,QAAQ,CAAC;EACpEjD,MAAM,CAACiF,eAAe,CAAC,MAAM;IAC3B,IAAI/E,OAAO,IAAIA,OAAO,CAACC,MAAM,IAAI,CAACD,OAAO,CAACsB,iBAAiB,EAAE;MAC3DtB,OAAO,CAACsB,iBAAiB,GAAG,IAAI;MAChCtB,OAAO,CAACC,MAAM,CAACkE,UAAU,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EACNrE,MAAM,CAACiF,eAAe,CAAC,MAAM;IAC3B,IAAI/E,OAAO,IAAIA,OAAO,CAACC,MAAM,EAAE;MAC7B,OAAOD,OAAO,CAACC,MAAM,CAAC+E,SAAS,CAAEC,QAAQ,IAAK;QAC5C,IAAIA,QAAQ,CAAClC,QAAQ,KAAKA,QAAQ,EAAE;UAClC+B,WAAW,CAACG,QAAQ,CAAClC,QAAQ,CAAC;QAChC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACd,CAAC,CAAC,EAAEhD,oBAAoB,CAAC6B,gBAAgB,EAAE5B,OAAO,EAAE,wCAAwC,CAAC;EAC7F,CAAC,CAAC,EAAED,oBAAoB,CAACmF,2BAA2B,EAClDjF,MAAM,EACND,OAAO,CAACmB,QAAQ,EAChBnB,OAAO,CAACoB,YAAY,EACpBpB,OAAO,CAACkB,OAAO,CAACkB,GAAG,EACnBpC,OAAO,CAACkB,OAAO,CAAC+C,cAAc,EAC9BjE,OAAO,CAACkB,OAAO,CAACmB,SAClB,CAAC;EACD;IACE;IACA;IACA;IAAgBvC,MAAM,CAACF,aAAa,CAACE,MAAM,CAACqF,QAAQ,EAAE,IAAI,EAAE,eAAgBrF,MAAM,CAACF,aAAa,CAC9FG,oBAAoB,CAACqF,uBAAuB,CAACC,QAAQ,EACrD;MACErG,KAAK,EAAE;QACLmC,QAAQ,EAAEnB,OAAO,CAACmB,QAAQ;QAC1BC,YAAY,EAAEpB,OAAO,CAACoB,YAAY;QAClCuC,MAAM,EAAE3D,OAAO,CAACkB,OAAO,CAACyC,MAAM;QAC9BY,WAAW;QACXnC,GAAG,EAAEpC,OAAO,CAACkB,OAAO,CAACkB,GAAG;QACxBC,SAAS,EAAErC,OAAO,CAACkB,OAAO,CAACmB,SAAS;QACpC4B,cAAc,EAAEjE,OAAO,CAACkB,OAAO,CAAC+C;MAClC;IACF,CAAC,EACD,eAAgBnE,MAAM,CAACF,aAAa,CAACG,oBAAoB,CAACuF,yBAAyB,EAAE;MAAEvC;IAAS,CAAC,EAAE,eAAgBjD,MAAM,CAACF,aAAa,CAACR,cAAc,EAAE;MAAEa;IAAO,CAAC,CAAC,CACrK,CAAC,EAAE,eAAgBH,MAAM,CAACF,aAAa,CAACE,MAAM,CAACqF,QAAQ,EAAE,IAAI,CAAC;EAAC;AAEnE;AACA;AACA,CAAC,KAAK9F,MAAM,CAACC,OAAO,GAAG;EACrBH,cAAc;EACdC;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}