{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\CalendarPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Modal, Form, Alert, Badge, Tabs, Tab } from 'react-bootstrap';\nimport DualCalendarView from '../components/DualCalendarView';\nimport RealCalendarManager from '../components/RealCalendarManager';\nimport CalendarAnalysis from '../components/CalendarAnalysis';\nimport { getDayByDate, createOrUpdateDay, getAllShifts, applyShiftToDate, suggestShiftsForDate, applyPattern, getAllPatterns, clearPlannedCalendar } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CalendarPage() {\n  _s();\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('calendar');\n\n  // Estados para modales\n  const [showRealDayModal, setShowRealDayModal] = useState(false);\n  const [showPatternModal, setShowPatternModal] = useState(false);\n\n  // Estados para aplicación de patrones\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estado para forzar actualización del calendario\n  const [calendarKey, setCalendarKey] = useState(0);\n  useEffect(() => {\n    loadAllPatterns();\n  }, []);\n  const loadAllPatterns = async () => {\n    try {\n      const patterns = await getAllPatterns();\n      setAllPatterns(patterns);\n    } catch (error) {\n      console.error('Error loading patterns:', error);\n      setError('Error cargando patrones.');\n    }\n  };\n  const handleDateSelect = date => {\n    setSelectedDate(date);\n  };\n  const handleRealDayUpdated = () => {\n    // Forzar actualización del calendario\n    setCalendarKey(prev => prev + 1);\n  };\n  const handleClearPlannedCalendar = async () => {\n    if (window.confirm('¿Estás seguro de que quieres limpiar todo el calendario planificado?')) {\n      try {\n        setLoading(true);\n        await clearPlannedCalendar();\n        setCalendarKey(prev => prev + 1);\n        setError('');\n      } catch (err) {\n        setError('Error limpiando calendario planificado: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  const handlePatternDateClick = value => {\n    if (!selectionStartDate) {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    } else if (!selectionEndDate) {\n      if (value < selectionStartDate) {\n        setSelectionEndDate(selectionStartDate);\n        setSelectionStartDate(value);\n      } else {\n        setSelectionEndDate(value);\n      }\n    } else {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Sistema de Horarios Dual\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-primary\",\n              size: \"sm\",\n              onClick: () => setShowRealDayModal(true),\n              disabled: !selectedDate,\n              className: \"me-2\",\n              children: \"Registrar D\\xEDa Real\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-danger\",\n              size: \"sm\",\n              onClick: handleClearPlannedCalendar,\n              disabled: loading,\n              children: \"Limpiar Planificado\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Aplicar Patr\\xF3n a Rango Seleccionado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"mb-3\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de inicio\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : '',\n                  onChange: e => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                controlId: \"patternEndDate\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de fin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : '',\n                  onChange: e => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Seleccionar Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: selectedPatternId,\n              onChange: e => setSelectedPatternId(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Seleccionar patr\\xF3n...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this), allPatterns.map(pattern => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: pattern.id,\n                children: pattern.name\n              }, pattern.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleApplyPattern,\n            disabled: loading || !selectedPatternId || !selectionStartDate || !selectionEndDate,\n            children: loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => {\n              setSelectionStartDate(null);\n              setSelectionEndDate(null);\n              setSelectedPatternId('');\n              setError('');\n            },\n            className: \"ms-2\",\n            children: \"Limpiar Selecci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Aplicar Patr\\xF3n a Rango Seleccionado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"mb-3\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de inicio\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : '',\n                  onChange: e => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de fin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : '',\n                  onChange: e => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Seleccionar Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: selectedPatternId,\n              onChange: e => setSelectedPatternId(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Seleccionar patr\\xF3n...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), allPatterns.map(pattern => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: pattern.id,\n                children: pattern.name\n              }, pattern.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleApplyPattern,\n            disabled: loading || !selectedPatternId || !selectionStartDate || !selectionEndDate,\n            children: loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => {\n              setSelectionStartDate(null);\n              setSelectionEndDate(null);\n              setSelectedPatternId('');\n              setError('');\n            },\n            className: \"ms-2\",\n            children: \"Limpiar Selecci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n      activeKey: activeTab,\n      onSelect: setActiveTab,\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"calendar\",\n        title: \"\\uD83D\\uDCC5 Calendario Dual\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dual-calendar\",\n          children: /*#__PURE__*/_jsxDEV(DualCalendarView, {\n            onDateSelect: handleDateSelect,\n            selectedDate: selectedDate\n          }, calendarKey, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"analysis\",\n        title: \"\\uD83D\\uDCCA An\\xE1lisis\",\n        children: /*#__PURE__*/_jsxDEV(CalendarAnalysis, {\n          selectedDate: selectedDate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RealCalendarManager, {\n      show: showRealDayModal,\n      onHide: () => setShowRealDayModal(false),\n      selectedDate: selectedDate,\n      onDayUpdated: handleRealDayUpdated\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n}\n_s(CalendarPage, \"RPnkoQhZSRWPIChm15GuPVPX19Q=\");\n_c = CalendarPage;\nexport default CalendarPage;\nvar _c;\n$RefreshReg$(_c, \"CalendarPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "<PERSON><PERSON>", "Modal", "Form", "<PERSON><PERSON>", "Badge", "Tabs", "Tab", "DualCalendarView", "RealCalendarManager", "CalendarAnalysis", "getDayByDate", "createOrUpdateDay", "getAllShifts", "applyShiftToDate", "suggestShiftsForDate", "applyPattern", "getAllPatterns", "clearPlannedCalendar", "jsxDEV", "_jsxDEV", "CalendarPage", "_s", "selectedDate", "setSelectedDate", "Date", "activeTab", "setActiveTab", "showRealDayModal", "setShowRealDayModal", "showPatternModal", "setShowPatternModal", "loading", "setLoading", "error", "setError", "calendarKey", "setCalendarKey", "loadAllPatterns", "patterns", "setAllPatterns", "console", "handleDateSelect", "date", "handleRealDayUpdated", "prev", "handleClearPlannedCalendar", "window", "confirm", "err", "message", "handlePatternDateClick", "value", "selectionStartDate", "setSelectionStartDate", "setSelectionEndDate", "selectionEndDate", "fluid", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "size", "onClick", "disabled", "md", "Group", "Label", "Control", "type", "toISOString", "split", "onChange", "e", "target", "controlId", "Select", "selectedPatternId", "setSelectedPatternId", "allPatterns", "map", "pattern", "id", "name", "handleApplyPattern", "active<PERSON><PERSON>", "onSelect", "eventKey", "title", "onDateSelect", "show", "onHide", "onDayUpdated", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/CalendarPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON>er, Row, Col, Button, Modal, Form, Alert, Badge, Tabs, Tab } from 'react-bootstrap';\nimport DualCalendarView from '../components/DualCalendarView';\nimport RealCalendarManager from '../components/RealCalendarManager';\nimport CalendarAnalysis from '../components/CalendarAnalysis';\nimport {\n  getDayByDate,\n  createOrUpdateDay,\n  getAllShifts,\n  applyShiftToDate,\n  suggestShiftsForDate,\n  applyPattern,\n  getAllPatterns,\n  clearPlannedCalendar\n} from '../services/api';\n\nfunction CalendarPage() {\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('calendar');\n\n  // Estados para modales\n  const [showRealDayModal, setShowRealDayModal] = useState(false);\n  const [showPatternModal, setShowPatternModal] = useState(false);\n\n  // Estados para aplicación de patrones\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estado para forzar actualización del calendario\n  const [calendarKey, setCalendarKey] = useState(0);\n\n  useEffect(() => {\n    loadAllPatterns();\n  }, []);\n\n  const loadAllPatterns = async () => {\n    try {\n      const patterns = await getAllPatterns();\n      setAllPatterns(patterns);\n    } catch (error) {\n      console.error('Error loading patterns:', error);\n      setError('Error cargando patrones.');\n    }\n  };\n\n  const handleDateSelect = (date) => {\n    setSelectedDate(date);\n  };\n\n  const handleRealDayUpdated = () => {\n    // Forzar actualización del calendario\n    setCalendarKey(prev => prev + 1);\n  };\n\n  const handleClearPlannedCalendar = async () => {\n    if (window.confirm('¿Estás seguro de que quieres limpiar todo el calendario planificado?')) {\n      try {\n        setLoading(true);\n        await clearPlannedCalendar();\n        setCalendarKey(prev => prev + 1);\n        setError('');\n      } catch (err) {\n        setError('Error limpiando calendario planificado: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  const handlePatternDateClick = (value) => {\n    if (!selectionStartDate) {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    } else if (!selectionEndDate) {\n      if (value < selectionStartDate) {\n        setSelectionEndDate(selectionStartDate);\n        setSelectionStartDate(value);\n      } else {\n        setSelectionEndDate(value);\n      }\n    } else {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    }\n  };\n\n  \n\n  return (\n    <Container fluid>\n      <Row className=\"mb-3\">\n        <Col>\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <h2>Sistema de Horarios Dual</h2>\n            <div>\n              <Button\n                variant=\"outline-primary\"\n                size=\"sm\"\n                onClick={() => setShowRealDayModal(true)}\n                disabled={!selectedDate}\n                className=\"me-2\"\n              >\n                Registrar Día Real\n              </Button>\n              <Button\n                variant=\"outline-danger\"\n                size=\"sm\"\n                onClick={handleClearPlannedCalendar}\n                disabled={loading}\n              >\n                Limpiar Planificado\n              </Button>\n            </div>\n          </div>\n        </Col>\n      </Row>\n\n      <Row className=\"mb-3\">\n        <Col>\n          <h3>Aplicar Patrón a Rango Seleccionado</h3>\n          {error && (\n            <Alert variant=\"danger\" className=\"mb-3\">\n              {error}\n            </Alert>\n          )}\n          <Form>\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Fecha de inicio</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : ''}\n                    onChange={(e) => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group controlId=\"patternEndDate\">\n                  <Form.Label>Fecha de fin</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : ''}\n                    onChange={(e) => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Seleccionar Patrón</Form.Label>\n              <Form.Select\n                value={selectedPatternId}\n                onChange={(e) => setSelectedPatternId(e.target.value)}\n              >\n                <option value=\"\">Seleccionar patrón...</option>\n                {allPatterns.map((pattern) => (\n                  <option key={pattern.id} value={pattern.id}>\n                    {pattern.name}\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n\n            <Button\n              variant=\"primary\"\n              onClick={handleApplyPattern}\n              disabled={loading || !selectedPatternId || !selectionStartDate || !selectionEndDate}\n            >\n              {loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'}\n            </Button>\n            <Button\n              variant=\"secondary\"\n              onClick={() => {\n                setSelectionStartDate(null);\n                setSelectionEndDate(null);\n                setSelectedPatternId('');\n                setError('');\n              }}\n              className=\"ms-2\"\n            >\n              Limpiar Selección\n            </Button>\n          </Form>\n        </Col>\n      </Row>\n\n      <Row className=\"mb-3\">\n        <Col>\n          <h3>Aplicar Patrón a Rango Seleccionado</h3>\n          {error && (\n            <Alert variant=\"danger\" className=\"mb-3\">\n              {error}\n            </Alert>\n          )}\n          <Form>\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Fecha de inicio</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : ''}\n                    onChange={(e) => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Fecha de fin</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : ''}\n                    onChange={(e) => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Seleccionar Patrón</Form.Label>\n              <Form.Select\n                value={selectedPatternId}\n                onChange={(e) => setSelectedPatternId(e.target.value)}\n              >\n                <option value=\"\">Seleccionar patrón...</option>\n                {allPatterns.map((pattern) => (\n                  <option key={pattern.id} value={pattern.id}>\n                    {pattern.name}\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n\n            <Button\n              variant=\"primary\"\n              onClick={handleApplyPattern}\n              disabled={loading || !selectedPatternId || !selectionStartDate || !selectionEndDate}\n            >\n              {loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'}\n            </Button>\n            <Button\n              variant=\"secondary\"\n              onClick={() => {\n                setSelectionStartDate(null);\n                setSelectionEndDate(null);\n                setSelectedPatternId('');\n                setError('');\n              }}\n              className=\"ms-2\"\n            >\n              Limpiar Selección\n            </Button>\n          </Form>\n        </Col>\n      </Row>\n\n      {error && (\n        <Row className=\"mb-3\">\n          <Col>\n            <Alert variant=\"danger\">{error}</Alert>\n          </Col>\n        </Row>\n      )}\n\n      <Tabs activeKey={activeTab} onSelect={setActiveTab} className=\"mb-3\">\n        <Tab eventKey=\"calendar\" title=\"📅 Calendario Dual\">\n          <div className=\"dual-calendar\">\n            <DualCalendarView\n              key={calendarKey}\n              onDateSelect={handleDateSelect}\n              selectedDate={selectedDate}\n            />\n          </div>\n        </Tab>\n\n        <Tab eventKey=\"analysis\" title=\"📊 Análisis\">\n          <CalendarAnalysis selectedDate={selectedDate} />\n        </Tab>\n      </Tabs>\n\n      {/* Modal para gestión de días reales */}\n      <RealCalendarManager\n        show={showRealDayModal}\n        onHide={() => setShowRealDayModal(false)}\n        selectedDate={selectedDate}\n        onDayUpdated={handleRealDayUpdated}\n      />\n\n      \n    </Container>\n  );\n}\n\nexport default CalendarPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,QAAQ,iBAAiB;AACnG,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,SACEC,YAAY,EACZC,iBAAiB,EACjBC,YAAY,EACZC,gBAAgB,EAChBC,oBAAoB,EACpBC,YAAY,EACZC,cAAc,EACdC,oBAAoB,QACf,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI6B,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,UAAU,CAAC;;EAEtD;EACA,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACdyC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMtB,cAAc,CAAC,CAAC;MACvCuB,cAAc,CAACD,QAAQ,CAAC;IAC1B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,0BAA0B,CAAC;IACtC;EACF,CAAC;EAED,MAAMO,gBAAgB,GAAIC,IAAI,IAAK;IACjCnB,eAAe,CAACmB,IAAI,CAAC;EACvB,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAP,cAAc,CAACQ,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;EAClC,CAAC;EAED,MAAMC,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAIC,MAAM,CAACC,OAAO,CAAC,sEAAsE,CAAC,EAAE;MAC1F,IAAI;QACFf,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMf,oBAAoB,CAAC,CAAC;QAC5BmB,cAAc,CAACQ,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAChCV,QAAQ,CAAC,EAAE,CAAC;MACd,CAAC,CAAC,OAAOc,GAAG,EAAE;QACZd,QAAQ,CAAC,0CAA0C,GAAGc,GAAG,CAACC,OAAO,CAAC;MACpE,CAAC,SAAS;QACRjB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;EAED,MAAMkB,sBAAsB,GAAIC,KAAK,IAAK;IACxC,IAAI,CAACC,kBAAkB,EAAE;MACvBC,qBAAqB,CAACF,KAAK,CAAC;MAC5BG,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM,IAAI,CAACC,gBAAgB,EAAE;MAC5B,IAAIJ,KAAK,GAAGC,kBAAkB,EAAE;QAC9BE,mBAAmB,CAACF,kBAAkB,CAAC;QACvCC,qBAAqB,CAACF,KAAK,CAAC;MAC9B,CAAC,MAAM;QACLG,mBAAmB,CAACH,KAAK,CAAC;MAC5B;IACF,CAAC,MAAM;MACLE,qBAAqB,CAACF,KAAK,CAAC;MAC5BG,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAID,oBACEnC,OAAA,CAACtB,SAAS;IAAC2D,KAAK;IAAAC,QAAA,gBACdtC,OAAA,CAACrB,GAAG;MAAC4D,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBtC,OAAA,CAACpB,GAAG;QAAA0D,QAAA,eACFtC,OAAA;UAAKuC,SAAS,EAAC,mDAAmD;UAAAD,QAAA,gBAChEtC,OAAA;YAAAsC,QAAA,EAAI;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjC3C,OAAA;YAAAsC,QAAA,gBACEtC,OAAA,CAACnB,MAAM;cACL+D,OAAO,EAAC,iBAAiB;cACzBC,IAAI,EAAC,IAAI;cACTC,OAAO,EAAEA,CAAA,KAAMrC,mBAAmB,CAAC,IAAI,CAAE;cACzCsC,QAAQ,EAAE,CAAC5C,YAAa;cACxBoC,SAAS,EAAC,MAAM;cAAAD,QAAA,EACjB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3C,OAAA,CAACnB,MAAM;cACL+D,OAAO,EAAC,gBAAgB;cACxBC,IAAI,EAAC,IAAI;cACTC,OAAO,EAAEpB,0BAA2B;cACpCqB,QAAQ,EAAEnC,OAAQ;cAAA0B,QAAA,EACnB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3C,OAAA,CAACrB,GAAG;MAAC4D,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBtC,OAAA,CAACpB,GAAG;QAAA0D,QAAA,gBACFtC,OAAA;UAAAsC,QAAA,EAAI;QAAmC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC3C7B,KAAK,iBACJd,OAAA,CAAChB,KAAK;UAAC4D,OAAO,EAAC,QAAQ;UAACL,SAAS,EAAC,MAAM;UAAAD,QAAA,EACrCxB;QAAK;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eACD3C,OAAA,CAACjB,IAAI;UAAAuD,QAAA,gBACHtC,OAAA,CAACrB,GAAG;YAAA2D,QAAA,gBACFtC,OAAA,CAACpB,GAAG;cAACoE,EAAE,EAAE,CAAE;cAAAV,QAAA,eACTtC,OAAA,CAACjB,IAAI,CAACkE,KAAK;gBAACV,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BtC,OAAA,CAACjB,IAAI,CAACmE,KAAK;kBAAAZ,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxC3C,OAAA,CAACjB,IAAI,CAACoE,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXpB,KAAK,EAAEC,kBAAkB,GAAGA,kBAAkB,CAACoB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;kBAChFC,QAAQ,EAAGC,CAAC,IAAKtB,qBAAqB,CAACsB,CAAC,CAACC,MAAM,CAACzB,KAAK,GAAG,IAAI3B,IAAI,CAACmD,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAC,GAAG,IAAI;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN3C,OAAA,CAACpB,GAAG;cAACoE,EAAE,EAAE,CAAE;cAAAV,QAAA,eACTtC,OAAA,CAACjB,IAAI,CAACkE,KAAK;gBAACS,SAAS,EAAC,gBAAgB;gBAAApB,QAAA,gBACpCtC,OAAA,CAACjB,IAAI,CAACmE,KAAK;kBAAAZ,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrC3C,OAAA,CAACjB,IAAI,CAACoE,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXpB,KAAK,EAAEI,gBAAgB,GAAGA,gBAAgB,CAACiB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;kBAC5EC,QAAQ,EAAGC,CAAC,IAAKrB,mBAAmB,CAACqB,CAAC,CAACC,MAAM,CAACzB,KAAK,GAAG,IAAI3B,IAAI,CAACmD,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAC,GAAG,IAAI;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3C,OAAA,CAACjB,IAAI,CAACkE,KAAK;YAACV,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BtC,OAAA,CAACjB,IAAI,CAACmE,KAAK;cAAAZ,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3C3C,OAAA,CAACjB,IAAI,CAAC4E,MAAM;cACV3B,KAAK,EAAE4B,iBAAkB;cACzBL,QAAQ,EAAGC,CAAC,IAAKK,oBAAoB,CAACL,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAE;cAAAM,QAAA,gBAEtDtC,OAAA;gBAAQgC,KAAK,EAAC,EAAE;gBAAAM,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9CmB,WAAW,CAACC,GAAG,CAAEC,OAAO,iBACvBhE,OAAA;gBAAyBgC,KAAK,EAAEgC,OAAO,CAACC,EAAG;gBAAA3B,QAAA,EACxC0B,OAAO,CAACE;cAAI,GADFF,OAAO,CAACC,EAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEb3C,OAAA,CAACnB,MAAM;YACL+D,OAAO,EAAC,SAAS;YACjBE,OAAO,EAAEqB,kBAAmB;YAC5BpB,QAAQ,EAAEnC,OAAO,IAAI,CAACgD,iBAAiB,IAAI,CAAC3B,kBAAkB,IAAI,CAACG,gBAAiB;YAAAE,QAAA,EAEnF1B,OAAO,GAAG,cAAc,GAAG;UAAwB;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACT3C,OAAA,CAACnB,MAAM;YACL+D,OAAO,EAAC,WAAW;YACnBE,OAAO,EAAEA,CAAA,KAAM;cACbZ,qBAAqB,CAAC,IAAI,CAAC;cAC3BC,mBAAmB,CAAC,IAAI,CAAC;cACzB0B,oBAAoB,CAAC,EAAE,CAAC;cACxB9C,QAAQ,CAAC,EAAE,CAAC;YACd,CAAE;YACFwB,SAAS,EAAC,MAAM;YAAAD,QAAA,EACjB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3C,OAAA,CAACrB,GAAG;MAAC4D,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBtC,OAAA,CAACpB,GAAG;QAAA0D,QAAA,gBACFtC,OAAA;UAAAsC,QAAA,EAAI;QAAmC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC3C7B,KAAK,iBACJd,OAAA,CAAChB,KAAK;UAAC4D,OAAO,EAAC,QAAQ;UAACL,SAAS,EAAC,MAAM;UAAAD,QAAA,EACrCxB;QAAK;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eACD3C,OAAA,CAACjB,IAAI;UAAAuD,QAAA,gBACHtC,OAAA,CAACrB,GAAG;YAAA2D,QAAA,gBACFtC,OAAA,CAACpB,GAAG;cAACoE,EAAE,EAAE,CAAE;cAAAV,QAAA,eACTtC,OAAA,CAACjB,IAAI,CAACkE,KAAK;gBAACV,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BtC,OAAA,CAACjB,IAAI,CAACmE,KAAK;kBAAAZ,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxC3C,OAAA,CAACjB,IAAI,CAACoE,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXpB,KAAK,EAAEC,kBAAkB,GAAGA,kBAAkB,CAACoB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;kBAChFC,QAAQ,EAAGC,CAAC,IAAKtB,qBAAqB,CAACsB,CAAC,CAACC,MAAM,CAACzB,KAAK,GAAG,IAAI3B,IAAI,CAACmD,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAC,GAAG,IAAI;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN3C,OAAA,CAACpB,GAAG;cAACoE,EAAE,EAAE,CAAE;cAAAV,QAAA,eACTtC,OAAA,CAACjB,IAAI,CAACkE,KAAK;gBAACV,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BtC,OAAA,CAACjB,IAAI,CAACmE,KAAK;kBAAAZ,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrC3C,OAAA,CAACjB,IAAI,CAACoE,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXpB,KAAK,EAAEI,gBAAgB,GAAGA,gBAAgB,CAACiB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;kBAC5EC,QAAQ,EAAGC,CAAC,IAAKrB,mBAAmB,CAACqB,CAAC,CAACC,MAAM,CAACzB,KAAK,GAAG,IAAI3B,IAAI,CAACmD,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAC,GAAG,IAAI;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3C,OAAA,CAACjB,IAAI,CAACkE,KAAK;YAACV,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BtC,OAAA,CAACjB,IAAI,CAACmE,KAAK;cAAAZ,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3C3C,OAAA,CAACjB,IAAI,CAAC4E,MAAM;cACV3B,KAAK,EAAE4B,iBAAkB;cACzBL,QAAQ,EAAGC,CAAC,IAAKK,oBAAoB,CAACL,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAE;cAAAM,QAAA,gBAEtDtC,OAAA;gBAAQgC,KAAK,EAAC,EAAE;gBAAAM,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9CmB,WAAW,CAACC,GAAG,CAAEC,OAAO,iBACvBhE,OAAA;gBAAyBgC,KAAK,EAAEgC,OAAO,CAACC,EAAG;gBAAA3B,QAAA,EACxC0B,OAAO,CAACE;cAAI,GADFF,OAAO,CAACC,EAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEb3C,OAAA,CAACnB,MAAM;YACL+D,OAAO,EAAC,SAAS;YACjBE,OAAO,EAAEqB,kBAAmB;YAC5BpB,QAAQ,EAAEnC,OAAO,IAAI,CAACgD,iBAAiB,IAAI,CAAC3B,kBAAkB,IAAI,CAACG,gBAAiB;YAAAE,QAAA,EAEnF1B,OAAO,GAAG,cAAc,GAAG;UAAwB;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACT3C,OAAA,CAACnB,MAAM;YACL+D,OAAO,EAAC,WAAW;YACnBE,OAAO,EAAEA,CAAA,KAAM;cACbZ,qBAAqB,CAAC,IAAI,CAAC;cAC3BC,mBAAmB,CAAC,IAAI,CAAC;cACzB0B,oBAAoB,CAAC,EAAE,CAAC;cACxB9C,QAAQ,CAAC,EAAE,CAAC;YACd,CAAE;YACFwB,SAAS,EAAC,MAAM;YAAAD,QAAA,EACjB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL7B,KAAK,iBACJd,OAAA,CAACrB,GAAG;MAAC4D,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBtC,OAAA,CAACpB,GAAG;QAAA0D,QAAA,eACFtC,OAAA,CAAChB,KAAK;UAAC4D,OAAO,EAAC,QAAQ;UAAAN,QAAA,EAAExB;QAAK;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED3C,OAAA,CAACd,IAAI;MAACkF,SAAS,EAAE9D,SAAU;MAAC+D,QAAQ,EAAE9D,YAAa;MAACgC,SAAS,EAAC,MAAM;MAAAD,QAAA,gBAClEtC,OAAA,CAACb,GAAG;QAACmF,QAAQ,EAAC,UAAU;QAACC,KAAK,EAAC,8BAAoB;QAAAjC,QAAA,eACjDtC,OAAA;UAAKuC,SAAS,EAAC,eAAe;UAAAD,QAAA,eAC5BtC,OAAA,CAACZ,gBAAgB;YAEfoF,YAAY,EAAElD,gBAAiB;YAC/BnB,YAAY,EAAEA;UAAa,GAFtBa,WAAW;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN3C,OAAA,CAACb,GAAG;QAACmF,QAAQ,EAAC,UAAU;QAACC,KAAK,EAAC,0BAAa;QAAAjC,QAAA,eAC1CtC,OAAA,CAACV,gBAAgB;UAACa,YAAY,EAAEA;QAAa;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP3C,OAAA,CAACX,mBAAmB;MAClBoF,IAAI,EAAEjE,gBAAiB;MACvBkE,MAAM,EAAEA,CAAA,KAAMjE,mBAAmB,CAAC,KAAK,CAAE;MACzCN,YAAY,EAAEA,YAAa;MAC3BwE,YAAY,EAAEnD;IAAqB;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGO,CAAC;AAEhB;AAACzC,EAAA,CApRQD,YAAY;AAAA2E,EAAA,GAAZ3E,YAAY;AAsRrB,eAAeA,YAAY;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}