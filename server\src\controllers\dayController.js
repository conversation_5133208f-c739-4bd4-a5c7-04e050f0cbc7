const calendarService = require('../services/calendarService');
const patternService = require('../services/patternService');

// ===== CALENDARIO REAL =====
const getAllRealDays = (req, res) => {
    try {
        const days = calendarService.getAllRealDays();
        res.json(days);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getRealDayByDate = (req, res) => {
    try {
        const { date } = req.params;
        const day = calendarService.getRealDayByDate(date);
        if (day) {
            res.json(day);
        } else {
            res.status(404).json({ message: 'Real day not found' });
        }
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

// ===== CALENDARIO PLANIFICADO =====
const getAllPlannedDays = (req, res) => {
    try {
        const days = calendarService.getAllPlannedDays();
        res.json(days);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getPlannedDayByDate = (req, res) => {
    try {
        const { date } = req.params;
        const day = calendarService.getPlannedDayByDate(date);
        if (day) {
            res.json(day);
        } else {
            res.status(404).json({ message: 'Planned day not found' });
        }
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

// ===== COMPATIBILIDAD =====
const getAllDays = (req, res) => getAllRealDays(req, res);
const getDayByDate = (req, res) => getRealDayByDate(req, res);

const createOrUpdateRealDay = (req, res) => {
    try {
        const { date, type, hours, description, shiftId, startTime, endTime } = req.body;
        if (!date || !type) {
            return res.status(400).json({ message: 'Date and type are required' });
        }
        const updatedDay = calendarService.createOrUpdateRealDay(date, type, hours, description, shiftId, startTime, endTime);
        res.status(200).json(updatedDay);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const applyPatternToDates = (req, res) => {
    try {
        const { startDate, endDate, pattern, patternId } = req.body;

        if (!startDate || !endDate) {
            return res.status(400).json({ message: 'Start date and end date are required.' });
        }

        // Support both pattern array and patternId
        if (patternId) {
            // Use pattern by ID - AHORA GENERA CALENDARIO PLANIFICADO
            const result = patternService.applyPatternToDates(startDate, endDate, patternId);
            res.status(200).json(result);
        } else if (pattern && Array.isArray(pattern)) {
            // Use pattern array directly (legacy support)
            // TODO: Implement legacy pattern array support if needed
            return res.status(400).json({ message: 'Pattern array support not implemented. Use patternId instead.' });
        } else {
            return res.status(400).json({ message: 'Either a valid pattern array or patternId is required.' });
        }
    } catch (error) {
        console.error('Error in applyPatternToDates:', error);
        res.status(500).json({ message: error.message });
    }
};

const clearPlannedCalendar = (req, res) => {
    try {
        const result = calendarService.clearPlannedCalendar();
        res.status(200).json({ message: 'Planned calendar cleared successfully', result });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const deleteRealDay = (req, res) => {
    try {
        const { date } = req.params;
        const deleted = calendarService.deleteRealDay(date);
        if (deleted) {
            res.status(204).send();
        } else {
            res.status(404).json({ message: 'Day not found' });
        }
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

module.exports = {
    // Calendario Real
    getAllRealDays,
    getRealDayByDate,
    createOrUpdateRealDay,
    deleteRealDay,

    // Calendario Planificado
    getAllPlannedDays,
    getPlannedDayByDate,
    clearPlannedCalendar,

    // Aplicación de patrones
    applyPatternToDates
};
