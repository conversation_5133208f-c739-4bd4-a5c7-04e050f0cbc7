const { readData, writeData } = require('../data/dataHandler');
const shiftService = require('./shiftService');

// ===== CALENDARIO REAL =====
const getAllRealDays = () => {
    const data = readData();
    return data.realCalendar || [];
};

const getRealDayByDate = (date) => {
    const data = readData();
    return data.realCalendar?.find(day => day.date === date);
};

const createOrUpdateRealDay = (date, type, hours = 0, description = '', shiftId = null, startTime = null, endTime = null) => {
    const data = readData();
    if (!data.realCalendar) data.realCalendar = [];

    let calculatedHours = hours;
    let finalDescription = description;

    if (shiftId) {
        const shift = shiftService.getShiftById(shiftId);
        if (shift) {
            // If start and end times are provided, calculate hours based on them
            if (startTime && endTime) {
                const [startHour, startMinute] = startTime.split(':').map(Number);
                const [endHour, endMinute] = endTime.split(':').map(Number);

                let totalMinutes = (endHour * 60 + endMinute) - (startHour * 60 + startMinute);
                if (totalMinutes < 0) { // Handle overnight shifts
                    totalMinutes += 24 * 60;
                }
                calculatedHours = (totalMinutes - shift.breakMinutes) / 60;
                finalDescription = `${shift.name}: ${startTime} - ${endTime} (${shift.breakMinutes}min descanso)`;
            } else {
                // If no start/end times, use shift's totalHours
                calculatedHours = shift.totalHours;
                finalDescription = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;
            }
            type = 'worked'; // A shift implies a worked day
        }
    }

    let day = data.realCalendar.find(d => d.date === date);

    if (day) {
        day.type = type;
        day.hours = calculatedHours;
        day.description = finalDescription;
        day.shiftId = shiftId;
        day.startTime = startTime;
        day.endTime = endTime;
    } else {
        day = { date, type, hours: calculatedHours, description: finalDescription, shiftId, startTime, endTime };
        data.realCalendar.push(day);
    }
    writeData(data);
    return day;
};

const deleteRealDay = (date) => {
    const data = readData();
    if (!data.realCalendar) data.realCalendar = [];

    const initialLength = data.realCalendar.length;
    data.realCalendar = data.realCalendar.filter(day => day.date !== date);
    writeData(data);
    return data.realCalendar.length < initialLength;
};

// ===== CALENDARIO PLANIFICADO =====
const getAllPlannedDays = () => {
    const data = readData();
    return data.plannedCalendar || [];
};

const getPlannedDayByDate = (date) => {
    const data = readData();
    return data.plannedCalendar?.find(day => day.date === date);
};

const createOrUpdatePlannedDay = (date, type, hours = 0, description = '', shiftId = null) => {
    const data = readData();
    if (!data.plannedCalendar) data.plannedCalendar = [];

    let day = data.plannedCalendar.find(d => d.date === date);

    if (day) {
        day.type = type;
        day.hours = hours;
        day.description = description;
        day.shiftId = shiftId;
    } else {
        day = { date, type, hours, description, shiftId };
        data.plannedCalendar.push(day);
    }
    writeData(data);
    return day;
};

const clearPlannedCalendar = () => {
    const data = readData();
    data.plannedCalendar = [];
    writeData(data);
    return true;
};

const deletePlannedDay = (date) => {
    const data = readData();
    if (!data.plannedCalendar) data.plannedCalendar = [];

    const initialLength = data.plannedCalendar.length;
    data.plannedCalendar = data.plannedCalendar.filter(day => day.date !== date);
    writeData(data);
    return data.plannedCalendar.length < initialLength;
};

// ===== COMPATIBILIDAD CON VERSIÓN ANTERIOR =====
const getAllDays = () => getAllRealDays();
const getDayByDate = (date) => getRealDayByDate(date);
const createOrUpdateDay = (date, type, hours, description, shiftId) =>
    createOrUpdateRealDay(date, type, hours, description, shiftId);

module.exports = {
    // Calendario Real
    getAllRealDays,
    getRealDayByDate,
    createOrUpdateRealDay,
    deleteRealDay,

    // Calendario Planificado
    getAllPlannedDays,
    getPlannedDayByDate,
    createOrUpdatePlannedDay,
    clearPlannedCalendar,
    deletePlannedDay,

    // Compatibilidad
    getAllDays,
    getDayByDate,
    createOrUpdateDay
};
