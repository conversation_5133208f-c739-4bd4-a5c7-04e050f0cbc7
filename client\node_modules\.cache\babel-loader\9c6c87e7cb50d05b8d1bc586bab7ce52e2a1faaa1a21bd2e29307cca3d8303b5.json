{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\CalendarPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Modal, Form, Alert, Badge, Tabs, Tab } from 'react-bootstrap';\nimport DualCalendarView from '../components/DualCalendarView';\nimport RealCalendarManager from '../components/RealCalendarManager';\nimport CalendarAnalysis from '../components/CalendarAnalysis';\nimport { getDayByDate, createOrUpdateDay, getAllShifts, applyShiftToDate, suggestShiftsForDate, applyPattern, getAllPatterns, clearPlannedCalendar } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CalendarPage() {\n  _s();\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('calendar');\n\n  // Estados para modales\n  const [showRealDayModal, setShowRealDayModal] = useState(false);\n  const [showPatternModal, setShowPatternModal] = useState(false);\n\n  // Estados para aplicación de patrones\n  const [selectionStartDate, setSelectionStartDate] = useState(null);\n  const [selectionEndDate, setSelectionEndDate] = useState(null);\n  const [allPatterns, setAllPatterns] = useState([]);\n  const [selectedPatternId, setSelectedPatternId] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estado para forzar actualización del calendario\n  const [calendarKey, setCalendarKey] = useState(0);\n  useEffect(() => {\n    loadAllPatterns();\n  }, []);\n  const loadAllPatterns = async () => {\n    try {\n      const patterns = await getAllPatterns();\n      setAllPatterns(patterns);\n    } catch (error) {\n      console.error('Error loading patterns:', error);\n      setError('Error cargando patrones.');\n    }\n  };\n  const handleDateSelect = date => {\n    setSelectedDate(date);\n  };\n  const handleRealDayUpdated = () => {\n    // Forzar actualización del calendario\n    setCalendarKey(prev => prev + 1);\n  };\n  const handleClearPlannedCalendar = async () => {\n    if (window.confirm('¿Estás seguro de que quieres limpiar todo el calendario planificado?')) {\n      try {\n        setLoading(true);\n        await clearPlannedCalendar();\n        setCalendarKey(prev => prev + 1);\n        setError('');\n      } catch (err) {\n        setError('Error limpiando calendario planificado: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  const handlePatternDateClick = value => {\n    if (!selectionStartDate) {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    } else if (!selectionEndDate) {\n      if (value < selectionStartDate) {\n        setSelectionEndDate(selectionStartDate);\n        setSelectionStartDate(value);\n      } else {\n        setSelectionEndDate(value);\n      }\n    } else {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    }\n  };\n  const handleApplyPattern = async () => {\n    if (!selectionStartDate || !selectionEndDate || !selectedPatternId) {\n      setError('Selecciona un rango de fechas y un patrón');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      const formattedStartDate = selectionStartDate.toISOString().split('T')[0];\n      const formattedEndDate = selectionEndDate.toISOString().split('T')[0];\n      await applyPattern(formattedStartDate, formattedEndDate, selectedPatternId);\n      setSelectionStartDate(null);\n      setSelectionEndDate(null);\n      setSelectedPatternId('');\n      setShowPatternModal(false);\n      setCalendarKey(prev => prev + 1); // Forzar actualización del calendario\n      alert('Patrón aplicado al calendario planificado exitosamente!');\n    } catch (error) {\n      console.error('Error applying pattern:', error);\n      setError('Error aplicando el patrón: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Sistema de Horarios Dual\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-primary\",\n              size: \"sm\",\n              onClick: () => setShowRealDayModal(true),\n              disabled: !selectedDate,\n              className: \"me-2\",\n              children: \"Registrar D\\xEDa Real\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-secondary\",\n              size: \"sm\",\n              onClick: () => setShowPatternModal(true),\n              className: \"me-2\",\n              children: \"Aplicar Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-danger\",\n              size: \"sm\",\n              onClick: handleClearPlannedCalendar,\n              disabled: loading,\n              children: \"Limpiar Planificado\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n      activeKey: activeTab,\n      onSelect: setActiveTab,\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"calendar\",\n        title: \"\\uD83D\\uDCC5 Calendario Dual\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dual-calendar\",\n          children: /*#__PURE__*/_jsxDEV(DualCalendarView, {\n            onDateSelect: handleDateSelect,\n            selectedDate: selectedDate\n          }, calendarKey, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"analysis\",\n        title: \"\\uD83D\\uDCCA An\\xE1lisis\",\n        children: /*#__PURE__*/_jsxDEV(CalendarAnalysis, {\n          selectedDate: selectedDate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RealCalendarManager, {\n      show: showRealDayModal,\n      onHide: () => setShowRealDayModal(false),\n      selectedDate: selectedDate,\n      onDayUpdated: handleRealDayUpdated\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPatternModal,\n      onHide: () => setShowPatternModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Aplicar Patr\\xF3n al Calendario Planificado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"info\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Importante:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), \" Los patrones se aplicar\\xE1n al calendario planificado, no al calendario real. Esto te permitir\\xE1 comparar tu trabajo real con el horario te\\xF3rico.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de inicio\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : '',\n                  onChange: e => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Fecha de fin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  value: selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : '',\n                  onChange: e => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Seleccionar Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: selectedPatternId,\n              onChange: e => setSelectedPatternId(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Seleccionar patr\\xF3n...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this), allPatterns.map(pattern => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: pattern.id,\n                children: [pattern.name, \" - \", pattern.description]\n              }, pattern.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), selectionStartDate && selectionEndDate && /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"secondary\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Rango seleccionado:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this), \" \", ' ', selectionStartDate.toLocaleDateString('es-ES'), \" - \", selectionEndDate.toLocaleDateString('es-ES'), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"D\\xEDas:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), \" \", Math.ceil((selectionEndDate - selectionStartDate) / (1000 * 60 * 60 * 24)) + 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => {\n            setShowPatternModal(false);\n            setSelectionStartDate(null);\n            setSelectionEndDate(null);\n            setSelectedPatternId('');\n            setError('');\n          },\n          children: \"Cancelar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleApplyPattern,\n          disabled: loading || !selectedPatternId || !selectionStartDate || !selectionEndDate,\n          children: loading ? 'Aplicando...' : 'Aplicar Patrón'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 5\n  }, this);\n}\n_s(CalendarPage, \"uITU9TjMMwqnQXFPHuKhrf0iCQM=\");\n_c = CalendarPage;\nexport default CalendarPage;\nvar _c;\n$RefreshReg$(_c, \"CalendarPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "<PERSON><PERSON>", "Modal", "Form", "<PERSON><PERSON>", "Badge", "Tabs", "Tab", "DualCalendarView", "RealCalendarManager", "CalendarAnalysis", "getDayByDate", "createOrUpdateDay", "getAllShifts", "applyShiftToDate", "suggestShiftsForDate", "applyPattern", "getAllPatterns", "clearPlannedCalendar", "jsxDEV", "_jsxDEV", "CalendarPage", "_s", "selectedDate", "setSelectedDate", "Date", "activeTab", "setActiveTab", "showRealDayModal", "setShowRealDayModal", "showPatternModal", "setShowPatternModal", "selectionStartDate", "setSelectionStartDate", "selectionEndDate", "setSelectionEndDate", "allPatterns", "setAllPatterns", "selectedPatternId", "setSelectedPatternId", "loading", "setLoading", "error", "setError", "calendarKey", "setCalendarKey", "loadAllPatterns", "patterns", "console", "handleDateSelect", "date", "handleRealDayUpdated", "prev", "handleClearPlannedCalendar", "window", "confirm", "err", "message", "handlePatternDateClick", "value", "handleApplyPattern", "formattedStartDate", "toISOString", "split", "formattedEndDate", "alert", "fluid", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "size", "onClick", "disabled", "active<PERSON><PERSON>", "onSelect", "eventKey", "title", "onDateSelect", "show", "onHide", "onDayUpdated", "Header", "closeButton", "Title", "Body", "md", "Group", "Label", "Control", "type", "onChange", "e", "target", "Select", "map", "pattern", "id", "name", "description", "toLocaleDateString", "Math", "ceil", "Footer", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/CalendarPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON>er, Row, Col, Button, Modal, Form, Alert, Badge, Tabs, Tab } from 'react-bootstrap';\nimport DualCalendarView from '../components/DualCalendarView';\nimport RealCalendarManager from '../components/RealCalendarManager';\nimport CalendarAnalysis from '../components/CalendarAnalysis';\nimport {\n  getDayByDate,\n  createOrUpdateDay,\n  getAllShifts,\n  applyShiftToDate,\n  suggestShiftsForDate,\n  applyPattern,\n  getAllPatterns,\n  clearPlannedCalendar\n} from '../services/api';\n\nfunction CalendarPage() {\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('calendar');\n\n  // Estados para modales\n  const [showRealDayModal, setShowRealDayModal] = useState(false);\n  const [showPatternModal, setShowPatternModal] = useState(false);\n\n  // Estados para aplicación de patrones\n  const [selectionStartDate, setSelectionStartDate] = useState(null);\n  const [selectionEndDate, setSelectionEndDate] = useState(null);\n  const [allPatterns, setAllPatterns] = useState([]);\n  const [selectedPatternId, setSelectedPatternId] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estado para forzar actualización del calendario\n  const [calendarKey, setCalendarKey] = useState(0);\n\n  useEffect(() => {\n    loadAllPatterns();\n  }, []);\n\n  const loadAllPatterns = async () => {\n    try {\n      const patterns = await getAllPatterns();\n      setAllPatterns(patterns);\n    } catch (error) {\n      console.error('Error loading patterns:', error);\n      setError('Error cargando patrones.');\n    }\n  };\n\n  const handleDateSelect = (date) => {\n    setSelectedDate(date);\n  };\n\n  const handleRealDayUpdated = () => {\n    // Forzar actualización del calendario\n    setCalendarKey(prev => prev + 1);\n  };\n\n  const handleClearPlannedCalendar = async () => {\n    if (window.confirm('¿Estás seguro de que quieres limpiar todo el calendario planificado?')) {\n      try {\n        setLoading(true);\n        await clearPlannedCalendar();\n        setCalendarKey(prev => prev + 1);\n        setError('');\n      } catch (err) {\n        setError('Error limpiando calendario planificado: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  const handlePatternDateClick = (value) => {\n    if (!selectionStartDate) {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    } else if (!selectionEndDate) {\n      if (value < selectionStartDate) {\n        setSelectionEndDate(selectionStartDate);\n        setSelectionStartDate(value);\n      } else {\n        setSelectionEndDate(value);\n      }\n    } else {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    }\n  };\n\n  const handleApplyPattern = async () => {\n    if (!selectionStartDate || !selectionEndDate || !selectedPatternId) {\n      setError('Selecciona un rango de fechas y un patrón');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    try {\n      const formattedStartDate = selectionStartDate.toISOString().split('T')[0];\n      const formattedEndDate = selectionEndDate.toISOString().split('T')[0];\n      await applyPattern(formattedStartDate, formattedEndDate, selectedPatternId);\n      setSelectionStartDate(null);\n      setSelectionEndDate(null);\n      setSelectedPatternId('');\n      setShowPatternModal(false);\n      setCalendarKey(prev => prev + 1); // Forzar actualización del calendario\n      alert('Patrón aplicado al calendario planificado exitosamente!');\n    } catch (error) {\n      console.error('Error applying pattern:', error);\n      setError('Error aplicando el patrón: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Container fluid>\n      <Row className=\"mb-3\">\n        <Col>\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <h2>Sistema de Horarios Dual</h2>\n            <div>\n              <Button\n                variant=\"outline-primary\"\n                size=\"sm\"\n                onClick={() => setShowRealDayModal(true)}\n                disabled={!selectedDate}\n                className=\"me-2\"\n              >\n                Registrar Día Real\n              </Button>\n              <Button\n                variant=\"outline-secondary\"\n                size=\"sm\"\n                onClick={() => setShowPatternModal(true)}\n                className=\"me-2\"\n              >\n                Aplicar Patrón\n              </Button>\n              <Button\n                variant=\"outline-danger\"\n                size=\"sm\"\n                onClick={handleClearPlannedCalendar}\n                disabled={loading}\n              >\n                Limpiar Planificado\n              </Button>\n            </div>\n          </div>\n        </Col>\n      </Row>\n\n      {error && (\n        <Row className=\"mb-3\">\n          <Col>\n            <Alert variant=\"danger\">{error}</Alert>\n          </Col>\n        </Row>\n      )}\n\n      <Tabs activeKey={activeTab} onSelect={setActiveTab} className=\"mb-3\">\n        <Tab eventKey=\"calendar\" title=\"📅 Calendario Dual\">\n          <div className=\"dual-calendar\">\n            <DualCalendarView\n              key={calendarKey}\n              onDateSelect={handleDateSelect}\n              selectedDate={selectedDate}\n            />\n          </div>\n        </Tab>\n\n        <Tab eventKey=\"analysis\" title=\"📊 Análisis\">\n          <CalendarAnalysis selectedDate={selectedDate} />\n        </Tab>\n      </Tabs>\n\n      {/* Modal para gestión de días reales */}\n      <RealCalendarManager\n        show={showRealDayModal}\n        onHide={() => setShowRealDayModal(false)}\n        selectedDate={selectedDate}\n        onDayUpdated={handleRealDayUpdated}\n      />\n\n      {/* Modal para aplicación de patrones */}\n      <Modal show={showPatternModal} onHide={() => setShowPatternModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>Aplicar Patrón al Calendario Planificado</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <Alert variant=\"info\">\n            <strong>Importante:</strong> Los patrones se aplicarán al calendario planificado,\n            no al calendario real. Esto te permitirá comparar tu trabajo real con el horario teórico.\n          </Alert>\n\n          <Form>\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Fecha de inicio</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={selectionStartDate ? selectionStartDate.toISOString().split('T')[0] : ''}\n                    onChange={(e) => setSelectionStartDate(e.target.value ? new Date(e.target.value) : null)}\n                  />\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Fecha de fin</Form.Label>\n                  <Form.Control\n                    type=\"date\"\n                    value={selectionEndDate ? selectionEndDate.toISOString().split('T')[0] : ''}\n                    onChange={(e) => setSelectionEndDate(e.target.value ? new Date(e.target.value) : null)}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Seleccionar Patrón</Form.Label>\n              <Form.Select\n                value={selectedPatternId}\n                onChange={(e) => setSelectedPatternId(e.target.value)}\n              >\n                <option value=\"\">Seleccionar patrón...</option>\n                {allPatterns.map((pattern) => (\n                  <option key={pattern.id} value={pattern.id}>\n                    {pattern.name} - {pattern.description}\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n\n            {selectionStartDate && selectionEndDate && (\n              <Alert variant=\"secondary\">\n                <strong>Rango seleccionado:</strong> {' '}\n                {selectionStartDate.toLocaleDateString('es-ES')} - {selectionEndDate.toLocaleDateString('es-ES')}\n                <br />\n                <strong>Días:</strong> {Math.ceil((selectionEndDate - selectionStartDate) / (1000 * 60 * 60 * 24)) + 1}\n              </Alert>\n            )}\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button\n            variant=\"secondary\"\n            onClick={() => {\n              setShowPatternModal(false);\n              setSelectionStartDate(null);\n              setSelectionEndDate(null);\n              setSelectedPatternId('');\n              setError('');\n            }}\n          >\n            Cancelar\n          </Button>\n          <Button\n            variant=\"primary\"\n            onClick={handleApplyPattern}\n            disabled={loading || !selectedPatternId || !selectionStartDate || !selectionEndDate}\n          >\n            {loading ? 'Aplicando...' : 'Aplicar Patrón'}\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n}\n\nexport default CalendarPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,QAAQ,iBAAiB;AACnG,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,SACEC,YAAY,EACZC,iBAAiB,EACjBC,YAAY,EACZC,gBAAgB,EAChBC,oBAAoB,EACpBC,YAAY,EACZC,cAAc,EACdC,oBAAoB,QACf,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI6B,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,UAAU,CAAC;;EAEtD;EACA,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACoC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACsC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACdiD,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM9B,cAAc,CAAC,CAAC;MACvCoB,cAAc,CAACU,QAAQ,CAAC;IAC1B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,0BAA0B,CAAC;IACtC;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAIC,IAAI,IAAK;IACjC1B,eAAe,CAAC0B,IAAI,CAAC;EACvB,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAN,cAAc,CAACO,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;EAClC,CAAC;EAED,MAAMC,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAIC,MAAM,CAACC,OAAO,CAAC,sEAAsE,CAAC,EAAE;MAC1F,IAAI;QACFd,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMvB,oBAAoB,CAAC,CAAC;QAC5B2B,cAAc,CAACO,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAChCT,QAAQ,CAAC,EAAE,CAAC;MACd,CAAC,CAAC,OAAOa,GAAG,EAAE;QACZb,QAAQ,CAAC,0CAA0C,GAAGa,GAAG,CAACC,OAAO,CAAC;MACpE,CAAC,SAAS;QACRhB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;EAED,MAAMiB,sBAAsB,GAAIC,KAAK,IAAK;IACxC,IAAI,CAAC3B,kBAAkB,EAAE;MACvBC,qBAAqB,CAAC0B,KAAK,CAAC;MAC5BxB,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM,IAAI,CAACD,gBAAgB,EAAE;MAC5B,IAAIyB,KAAK,GAAG3B,kBAAkB,EAAE;QAC9BG,mBAAmB,CAACH,kBAAkB,CAAC;QACvCC,qBAAqB,CAAC0B,KAAK,CAAC;MAC9B,CAAC,MAAM;QACLxB,mBAAmB,CAACwB,KAAK,CAAC;MAC5B;IACF,CAAC,MAAM;MACL1B,qBAAqB,CAAC0B,KAAK,CAAC;MAC5BxB,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAED,MAAMyB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAAC5B,kBAAkB,IAAI,CAACE,gBAAgB,IAAI,CAACI,iBAAiB,EAAE;MAClEK,QAAQ,CAAC,2CAA2C,CAAC;MACrD;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,MAAMkB,kBAAkB,GAAG7B,kBAAkB,CAAC8B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACzE,MAAMC,gBAAgB,GAAG9B,gBAAgB,CAAC4B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACrE,MAAM/C,YAAY,CAAC6C,kBAAkB,EAAEG,gBAAgB,EAAE1B,iBAAiB,CAAC;MAC3EL,qBAAqB,CAAC,IAAI,CAAC;MAC3BE,mBAAmB,CAAC,IAAI,CAAC;MACzBI,oBAAoB,CAAC,EAAE,CAAC;MACxBR,mBAAmB,CAAC,KAAK,CAAC;MAC1Bc,cAAc,CAACO,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;MAClCa,KAAK,CAAC,yDAAyD,CAAC;IAClE,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,6BAA6B,GAAGD,KAAK,CAACe,OAAO,CAAC;IACzD,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACErB,OAAA,CAACtB,SAAS;IAACoE,KAAK;IAAAC,QAAA,gBACd/C,OAAA,CAACrB,GAAG;MAACqE,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnB/C,OAAA,CAACpB,GAAG;QAAAmE,QAAA,eACF/C,OAAA;UAAKgD,SAAS,EAAC,mDAAmD;UAAAD,QAAA,gBAChE/C,OAAA;YAAA+C,QAAA,EAAI;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjCpD,OAAA;YAAA+C,QAAA,gBACE/C,OAAA,CAACnB,MAAM;cACLwE,OAAO,EAAC,iBAAiB;cACzBC,IAAI,EAAC,IAAI;cACTC,OAAO,EAAEA,CAAA,KAAM9C,mBAAmB,CAAC,IAAI,CAAE;cACzC+C,QAAQ,EAAE,CAACrD,YAAa;cACxB6C,SAAS,EAAC,MAAM;cAAAD,QAAA,EACjB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpD,OAAA,CAACnB,MAAM;cACLwE,OAAO,EAAC,mBAAmB;cAC3BC,IAAI,EAAC,IAAI;cACTC,OAAO,EAAEA,CAAA,KAAM5C,mBAAmB,CAAC,IAAI,CAAE;cACzCqC,SAAS,EAAC,MAAM;cAAAD,QAAA,EACjB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpD,OAAA,CAACnB,MAAM;cACLwE,OAAO,EAAC,gBAAgB;cACxBC,IAAI,EAAC,IAAI;cACTC,OAAO,EAAEtB,0BAA2B;cACpCuB,QAAQ,EAAEpC,OAAQ;cAAA2B,QAAA,EACnB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL9B,KAAK,iBACJtB,OAAA,CAACrB,GAAG;MAACqE,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnB/C,OAAA,CAACpB,GAAG;QAAAmE,QAAA,eACF/C,OAAA,CAAChB,KAAK;UAACqE,OAAO,EAAC,QAAQ;UAAAN,QAAA,EAAEzB;QAAK;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDpD,OAAA,CAACd,IAAI;MAACuE,SAAS,EAAEnD,SAAU;MAACoD,QAAQ,EAAEnD,YAAa;MAACyC,SAAS,EAAC,MAAM;MAAAD,QAAA,gBAClE/C,OAAA,CAACb,GAAG;QAACwE,QAAQ,EAAC,UAAU;QAACC,KAAK,EAAC,8BAAoB;QAAAb,QAAA,eACjD/C,OAAA;UAAKgD,SAAS,EAAC,eAAe;UAAAD,QAAA,eAC5B/C,OAAA,CAACZ,gBAAgB;YAEfyE,YAAY,EAAEhC,gBAAiB;YAC/B1B,YAAY,EAAEA;UAAa,GAFtBqB,WAAW;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpD,OAAA,CAACb,GAAG;QAACwE,QAAQ,EAAC,UAAU;QAACC,KAAK,EAAC,0BAAa;QAAAb,QAAA,eAC1C/C,OAAA,CAACV,gBAAgB;UAACa,YAAY,EAAEA;QAAa;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPpD,OAAA,CAACX,mBAAmB;MAClByE,IAAI,EAAEtD,gBAAiB;MACvBuD,MAAM,EAAEA,CAAA,KAAMtD,mBAAmB,CAAC,KAAK,CAAE;MACzCN,YAAY,EAAEA,YAAa;MAC3B6D,YAAY,EAAEjC;IAAqB;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eAGFpD,OAAA,CAAClB,KAAK;MAACgF,IAAI,EAAEpD,gBAAiB;MAACqD,MAAM,EAAEA,CAAA,KAAMpD,mBAAmB,CAAC,KAAK,CAAE;MAAC2C,IAAI,EAAC,IAAI;MAAAP,QAAA,gBAChF/C,OAAA,CAAClB,KAAK,CAACmF,MAAM;QAACC,WAAW;QAAAnB,QAAA,eACvB/C,OAAA,CAAClB,KAAK,CAACqF,KAAK;UAAApB,QAAA,EAAC;QAAwC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACfpD,OAAA,CAAClB,KAAK,CAACsF,IAAI;QAAArB,QAAA,gBACT/C,OAAA,CAAChB,KAAK;UAACqE,OAAO,EAAC,MAAM;UAAAN,QAAA,gBACnB/C,OAAA;YAAA+C,QAAA,EAAQ;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,4JAE9B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAERpD,OAAA,CAACjB,IAAI;UAAAgE,QAAA,gBACH/C,OAAA,CAACrB,GAAG;YAAAoE,QAAA,gBACF/C,OAAA,CAACpB,GAAG;cAACyF,EAAE,EAAE,CAAE;cAAAtB,QAAA,eACT/C,OAAA,CAACjB,IAAI,CAACuF,KAAK;gBAACtB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1B/C,OAAA,CAACjB,IAAI,CAACwF,KAAK;kBAAAxB,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxCpD,OAAA,CAACjB,IAAI,CAACyF,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXlC,KAAK,EAAE3B,kBAAkB,GAAGA,kBAAkB,CAAC8B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;kBAChF+B,QAAQ,EAAGC,CAAC,IAAK9D,qBAAqB,CAAC8D,CAAC,CAACC,MAAM,CAACrC,KAAK,GAAG,IAAIlC,IAAI,CAACsE,CAAC,CAACC,MAAM,CAACrC,KAAK,CAAC,GAAG,IAAI;gBAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNpD,OAAA,CAACpB,GAAG;cAACyF,EAAE,EAAE,CAAE;cAAAtB,QAAA,eACT/C,OAAA,CAACjB,IAAI,CAACuF,KAAK;gBAACtB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1B/C,OAAA,CAACjB,IAAI,CAACwF,KAAK;kBAAAxB,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCpD,OAAA,CAACjB,IAAI,CAACyF,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXlC,KAAK,EAAEzB,gBAAgB,GAAGA,gBAAgB,CAAC4B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;kBAC5E+B,QAAQ,EAAGC,CAAC,IAAK5D,mBAAmB,CAAC4D,CAAC,CAACC,MAAM,CAACrC,KAAK,GAAG,IAAIlC,IAAI,CAACsE,CAAC,CAACC,MAAM,CAACrC,KAAK,CAAC,GAAG,IAAI;gBAAE;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpD,OAAA,CAACjB,IAAI,CAACuF,KAAK;YAACtB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1B/C,OAAA,CAACjB,IAAI,CAACwF,KAAK;cAAAxB,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3CpD,OAAA,CAACjB,IAAI,CAAC8F,MAAM;cACVtC,KAAK,EAAErB,iBAAkB;cACzBwD,QAAQ,EAAGC,CAAC,IAAKxD,oBAAoB,CAACwD,CAAC,CAACC,MAAM,CAACrC,KAAK,CAAE;cAAAQ,QAAA,gBAEtD/C,OAAA;gBAAQuC,KAAK,EAAC,EAAE;gBAAAQ,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9CpC,WAAW,CAAC8D,GAAG,CAAEC,OAAO,iBACvB/E,OAAA;gBAAyBuC,KAAK,EAAEwC,OAAO,CAACC,EAAG;gBAAAjC,QAAA,GACxCgC,OAAO,CAACE,IAAI,EAAC,KAAG,EAACF,OAAO,CAACG,WAAW;cAAA,GAD1BH,OAAO,CAACC,EAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EAEZxC,kBAAkB,IAAIE,gBAAgB,iBACrCd,OAAA,CAAChB,KAAK;YAACqE,OAAO,EAAC,WAAW;YAAAN,QAAA,gBACxB/C,OAAA;cAAA+C,QAAA,EAAQ;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,GAAG,EACxCxC,kBAAkB,CAACuE,kBAAkB,CAAC,OAAO,CAAC,EAAC,KAAG,EAACrE,gBAAgB,CAACqE,kBAAkB,CAAC,OAAO,CAAC,eAChGnF,OAAA;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNpD,OAAA;cAAA+C,QAAA,EAAQ;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACgC,IAAI,CAACC,IAAI,CAAC,CAACvE,gBAAgB,GAAGF,kBAAkB,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC;UAAA;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjG,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbpD,OAAA,CAAClB,KAAK,CAACwG,MAAM;QAAAvC,QAAA,gBACX/C,OAAA,CAACnB,MAAM;UACLwE,OAAO,EAAC,WAAW;UACnBE,OAAO,EAAEA,CAAA,KAAM;YACb5C,mBAAmB,CAAC,KAAK,CAAC;YAC1BE,qBAAqB,CAAC,IAAI,CAAC;YAC3BE,mBAAmB,CAAC,IAAI,CAAC;YACzBI,oBAAoB,CAAC,EAAE,CAAC;YACxBI,QAAQ,CAAC,EAAE,CAAC;UACd,CAAE;UAAAwB,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpD,OAAA,CAACnB,MAAM;UACLwE,OAAO,EAAC,SAAS;UACjBE,OAAO,EAAEf,kBAAmB;UAC5BgB,QAAQ,EAAEpC,OAAO,IAAI,CAACF,iBAAiB,IAAI,CAACN,kBAAkB,IAAI,CAACE,gBAAiB;UAAAiC,QAAA,EAEnF3B,OAAO,GAAG,cAAc,GAAG;QAAgB;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB;AAAClD,EAAA,CA7PQD,YAAY;AAAAsF,EAAA,GAAZtF,YAAY;AA+PrB,eAAeA,YAAY;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}