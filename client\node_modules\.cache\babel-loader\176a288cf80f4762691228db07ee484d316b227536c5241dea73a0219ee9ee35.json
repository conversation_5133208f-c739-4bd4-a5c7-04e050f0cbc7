{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\ReportsPage.js\";\nimport React from 'react';\nimport { Container } from 'react-bootstrap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ReportsPage() {\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Gu\\xEDa de Uso: Calendario Patr\\xF3n Anual\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Esta funcionalidad te permite definir patrones de trabajo complejos (horarios semanales recurrentes y anulaciones para periodos espec\\xEDficos) y aplicarlos f\\xE1cilmente a rangos de fechas en tu calendario. Tambi\\xE9n puedes copiar patrones de un a\\xF1o a otro para simplificar la gesti\\xF3n anual.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n      children: \"1. Definir y Gestionar Patrones (P\\xE1gina de Configuraci\\xF3n)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Para crear o modificar tus patrones de calendario, ve a la p\\xE1gina de \", /*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Configuraci\\xF3n\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 79\n      }, this), \".\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Crear Nuevo Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 13\n        }, this), \":\", /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Haz clic en el bot\\xF3n \\\"Crear Nuevo Patr\\xF3n\\\".\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [\"Asigna un \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Nombre\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 16,\n              columnNumber: 27\n            }, this), \" descriptivo a tu patr\\xF3n (ej. \\\"Calendario 2025\\\", \\\"Patr\\xF3n Verano\\\").\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Patr\\xF3n Base (Semanal)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 17\n            }, this), \": Define tu horario semanal recurrente. Para cada d\\xEDa de la semana (Domingo a S\\xE1bado), puedes especificar:\", /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipo de D\\xEDa\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 19,\n                  columnNumber: 21\n                }, this), \": Trabajado, Vacaciones, Permiso, C\\xF3mputo Negativo.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 19,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Horas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 20,\n                  columnNumber: 21\n                }, this), \": Horas de c\\xF3mputo positivo o negativo.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 20,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Turno Predefinido\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 21,\n                  columnNumber: 21\n                }, this), \": Si aplica, selecciona un turno de tu lista de turnos predefinidos. Esto rellenar\\xE1 autom\\xE1ticamente las horas y una descripci\\xF3n.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descripci\\xF3n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 22,\n                  columnNumber: 21\n                }, this), \": Una descripci\\xF3n adicional para el d\\xEDa.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Anulaciones (Overrides)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 17\n            }, this), \": Para periodos espec\\xEDficos que difieren del patr\\xF3n base (ej. puentes, vacaciones con horarios especiales), puedes a\\xF1adir anulaciones:\", /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Haz clic en \\\"A\\xF1adir Anulaci\\xF3n\\\".\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"Define una \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Fecha de Inicio\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 28,\n                  columnNumber: 32\n                }, this), \" y \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Fecha de Fin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 28,\n                  columnNumber: 67\n                }, this), \" para esta anulaci\\xF3n.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Dentro de cada anulaci\\xF3n, puedes definir un mini-patr\\xF3n de d\\xEDas que se aplicar\\xE1 c\\xEDclicamente solo durante ese rango de fechas, anulando el patr\\xF3n base. Puedes a\\xF1adir y eliminar d\\xEDas dentro de la anulaci\\xF3n seg\\xFAn sea necesario.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Haz clic en \\\"Guardar Patr\\xF3n\\\" para guardar tu definici\\xF3n.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Editar Patr\\xF3n Existente\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 13\n        }, this), \":\", /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"En la lista de patrones, haz clic en \\\"Editar\\\" junto al patr\\xF3n que deseas modificar.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Realiza los cambios necesarios en el patr\\xF3n base o en las anulaciones.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Haz clic en \\\"Guardar Patr\\xF3n\\\".\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Copiar Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 13\n        }, this), \":\", /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Junto a un patr\\xF3n existente, haz clic en \\\"Copiar\\\".\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [\"Se abrir\\xE1 un modal donde puedes especificar un un \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Nuevo Nombre\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 67\n            }, this), \" (ej. \\\"Calendario 2026\\\") y el \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"A\\xF1o\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 126\n            }, this), \" para el nuevo patr\\xF3n.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"El sistema copiar\\xE1 el patr\\xF3n original y ajustar\\xE1 autom\\xE1ticamente las fechas de las anulaciones al nuevo a\\xF1o.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Haz clic en \\\"Copiar Patr\\xF3n\\\".\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Eliminar Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this), \":\", /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Haz clic en \\\"Eliminar\\\" junto al patr\\xF3n que deseas borrar. Se te pedir\\xE1 confirmaci\\xF3n.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n      children: \"2. Aplicar Patrones al Calendario (P\\xE1gina de Calendario)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Para aplicar un patr\\xF3n guardado a un rango de fechas en tu calendario:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Seleccionar Rango de Fechas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this), \":\", /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: [\"En la p\\xE1gina del Calendario, haz clic en la \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"fecha de inicio\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 61\n            }, this), \" deseada en el calendario.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [\"Luego, haz clic en la \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"fecha de fin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 39\n            }, this), \" deseada.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Ver\\xE1s que el rango de fechas seleccionado se resaltar\\xE1 visualmente en el calendario (verde oscuro para el inicio, verde claro para el rango). Las fechas seleccionadas tambi\\xE9n se mostrar\\xE1n en el campo \\\"Fechas Seleccionadas\\\".\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Si necesitas cambiar la selecci\\xF3n, simplemente haz clic en una nueva fecha de inicio.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Seleccionar Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this), \":\", /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Debajo del calendario, en la secci\\xF3n \\\"Aplicar Patr\\xF3n a Rango Seleccionado\\\", utiliza el desplegable \\\"Seleccionar Patr\\xF3n\\\" para elegir uno de los patrones que has definido en la p\\xE1gina de Configuraci\\xF3n.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Aplicar Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this), \":\", /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Una vez que hayas seleccionado un rango de fechas y un patr\\xF3n, el bot\\xF3n \\\"Aplicar Patr\\xF3n a Rango\\\" se activar\\xE1.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Haz clic en este bot\\xF3n. El sistema aplicar\\xE1 el patr\\xF3n seleccionado a cada d\\xEDa dentro del rango de fechas, respetando el patr\\xF3n base y las anulaciones definidas en el patr\\xF3n.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Recibir\\xE1s una alerta de confirmaci\\xF3n cuando el patr\\xF3n se haya aplicado exitosamente.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Limpiar Selecci\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this), \":\", /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Haz clic en el bot\\xF3n \\\"Limpiar Selecci\\xF3n\\\" para deseleccionar las fechas y el patr\\xF3n, y preparar el calendario para una nueva operaci\\xF3n.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n      children: \"Detalles Adicionales:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        children: \"La aplicaci\\xF3n de un patr\\xF3n sobrescribir\\xE1 cualquier entrada de d\\xEDa existente dentro del rango de fechas seleccionado.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: \"Los turnos predefinidos se utilizan para rellenar autom\\xE1ticamente las horas y descripciones cuando se seleccionan en la definici\\xF3n del patr\\xF3n.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = ReportsPage;\nexport default ReportsPage;\nvar _c;\n$RefreshReg$(_c, \"ReportsPage\");", "map": {"version": 3, "names": ["React", "Container", "jsxDEV", "_jsxDEV", "ReportsPage", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/ReportsPage.js"], "sourcesContent": ["import React from 'react';\nimport { Container } from 'react-bootstrap';\n\nfunction ReportsPage() {\n  return (\n    <Container>\n      <h2>Guía de Uso: Calendario Patrón Anual</h2>\n      <p>Esta funcionalidad te permite definir patrones de trabajo complejos (horarios semanales recurrentes y anulaciones para periodos específicos) y aplicarlos fácilmente a rangos de fechas en tu calendario. También puedes copiar patrones de un año a otro para simplificar la gestión anual.</p>\n\n      <h4>1. Definir y Gestionar Patrones (Página de Configuración)</h4>\n      <p>Para crear o modificar tus patrones de calendario, ve a la página de <strong>Configuración</strong>.</p>\n      <ul>\n        <li><strong>Crear Nuevo Patrón</strong>:\n          <ul>\n            <li>Haz clic en el botón \"Crear Nuevo Patrón\".</li>\n            <li>Asigna un <strong>Nombre</strong> descriptivo a tu patrón (ej. \"Calendario 2025\", \"Patrón Verano\").</li>\n            <li><strong>Patrón Base (Semanal)</strong>: Define tu horario semanal recurrente. Para cada día de la semana (Domingo a Sábado), puedes especificar:\n              <ul>\n                <li><strong>Tipo de Día</strong>: Trabajado, Vacaciones, Permiso, Cómputo Negativo.</li>\n                <li><strong>Horas</strong>: Horas de cómputo positivo o negativo.</li>\n                <li><strong>Turno Predefinido</strong>: Si aplica, selecciona un turno de tu lista de turnos predefinidos. Esto rellenará automáticamente las horas y una descripción.</li>\n                <li><strong>Descripción</strong>: Una descripción adicional para el día.</li>\n              </ul>\n            </li>\n            <li><strong>Anulaciones (Overrides)</strong>: Para periodos específicos que difieren del patrón base (ej. puentes, vacaciones con horarios especiales), puedes añadir anulaciones:\n              <ul>\n                <li>Haz clic en \"Añadir Anulación\".</li>\n                <li>Define una <strong>Fecha de Inicio</strong> y <strong>Fecha de Fin</strong> para esta anulación.</li>\n                <li>Dentro de cada anulación, puedes definir un mini-patrón de días que se aplicará cíclicamente solo durante ese rango de fechas, anulando el patrón base. Puedes añadir y eliminar días dentro de la anulación según sea necesario.</li>\n              </ul>\n            </li>\n            <li>Haz clic en \"Guardar Patrón\" para guardar tu definición.</li>\n          </ul>\n        </li>\n        <li><strong>Editar Patrón Existente</strong>:\n          <ul>\n            <li>En la lista de patrones, haz clic en \"Editar\" junto al patrón que deseas modificar.</li>\n            <li>Realiza los cambios necesarios en el patrón base o en las anulaciones.</li>\n            <li>Haz clic en \"Guardar Patrón\".</li>\n          </ul>\n        </li>\n        <li><strong>Copiar Patrón</strong>:\n          <ul>\n            <li>Junto a un patrón existente, haz clic en \"Copiar\".</li>\n            <li>Se abrirá un modal donde puedes especificar un un <strong>Nuevo Nombre</strong> (ej. \"Calendario 2026\") y el <strong>Año</strong> para el nuevo patrón.</li>\n            <li>El sistema copiará el patrón original y ajustará automáticamente las fechas de las anulaciones al nuevo año.</li>\n            <li>Haz clic en \"Copiar Patrón\".</li>\n          </ul>\n        </li>\n        <li><strong>Eliminar Patrón</strong>:\n          <ul>\n            <li>Haz clic en \"Eliminar\" junto al patrón que deseas borrar. Se te pedirá confirmación.</li>\n          </ul>\n        </li>\n      </ul>\n\n      <h4>2. Aplicar Patrones al Calendario (Página de Calendario)</h4>\n      <p>Para aplicar un patrón guardado a un rango de fechas en tu calendario:</p>\n      <ol>\n        <li><strong>Seleccionar Rango de Fechas</strong>:\n          <ul>\n            <li>En la página del Calendario, haz clic en la <strong>fecha de inicio</strong> deseada en el calendario.</li>\n            <li>Luego, haz clic en la <strong>fecha de fin</strong> deseada.</li>\n            <li>Verás que el rango de fechas seleccionado se resaltará visualmente en el calendario (verde oscuro para el inicio, verde claro para el rango). Las fechas seleccionadas también se mostrarán en el campo \"Fechas Seleccionadas\".</li>\n            <li>Si necesitas cambiar la selección, simplemente haz clic en una nueva fecha de inicio.</li>\n          </ul>\n        </li>\n        <li><strong>Seleccionar Patrón</strong>:\n          <ul>\n            <li>Debajo del calendario, en la sección \"Aplicar Patrón a Rango Seleccionado\", utiliza el desplegable \"Seleccionar Patrón\" para elegir uno de los patrones que has definido en la página de Configuración.</li>\n          </ul>\n        </li>\n        <li><strong>Aplicar Patrón</strong>:\n          <ul>\n            <li>Una vez que hayas seleccionado un rango de fechas y un patrón, el botón \"Aplicar Patrón a Rango\" se activará.</li>\n            <li>Haz clic en este botón. El sistema aplicará el patrón seleccionado a cada día dentro del rango de fechas, respetando el patrón base y las anulaciones definidas en el patrón.</li>\n            <li>Recibirás una alerta de confirmación cuando el patrón se haya aplicado exitosamente.</li>\n          </ul>\n        </li>\n        <li><strong>Limpiar Selección</strong>:\n          <ul>\n            <li>Haz clic en el botón \"Limpiar Selección\" para deseleccionar las fechas y el patrón, y preparar el calendario para una nueva operación.</li>\n          </ul>\n        </li>\n      </ol>\n\n      <h4>Detalles Adicionales:</h4>\n      <ul>\n        <li>La aplicación de un patrón sobrescribirá cualquier entrada de día existente dentro del rango de fechas seleccionado.</li>\n        <li>Los turnos predefinidos se utilizan para rellenar automáticamente las horas y descripciones cuando se seleccionan en la definición del patrón.</li>\n      </ul>\n    </Container>\n  );\n}\n\nexport default ReportsPage;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,SAASC,WAAWA,CAAA,EAAG;EACrB,oBACED,OAAA,CAACF,SAAS;IAAAI,QAAA,gBACRF,OAAA;MAAAE,QAAA,EAAI;IAAoC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7CN,OAAA;MAAAE,QAAA,EAAG;IAA4R;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAEnSN,OAAA;MAAAE,QAAA,EAAI;IAAyD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAClEN,OAAA;MAAAE,QAAA,GAAG,0EAAqE,eAAAF,OAAA;QAAAE,QAAA,EAAQ;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAC3GN,OAAA;MAAAE,QAAA,gBACEF,OAAA;QAAAE,QAAA,gBAAIF,OAAA;UAAAE,QAAA,EAAQ;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KACrC,eAAAN,OAAA;UAAAE,QAAA,gBACEF,OAAA;YAAAE,QAAA,EAAI;UAA0C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnDN,OAAA;YAAAE,QAAA,GAAI,YAAU,eAAAF,OAAA;cAAAE,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gFAAkE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5GN,OAAA;YAAAE,QAAA,gBAAIF,OAAA;cAAAE,QAAA,EAAQ;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,oHACxC,eAAAN,OAAA;cAAAE,QAAA,gBACEF,OAAA;gBAAAE,QAAA,gBAAIF,OAAA;kBAAAE,QAAA,EAAQ;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,0DAAmD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxFN,OAAA;gBAAAE,QAAA,gBAAIF,OAAA;kBAAAE,QAAA,EAAQ;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,8CAAuC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtEN,OAAA;gBAAAE,QAAA,gBAAIF,OAAA;kBAAAE,QAAA,EAAQ;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,6IAAgI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3KN,OAAA;gBAAAE,QAAA,gBAAIF,OAAA;kBAAAE,QAAA,EAAQ;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,kDAAwC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACLN,OAAA;YAAAE,QAAA,gBAAIF,OAAA;cAAAE,QAAA,EAAQ;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,mJAC1C,eAAAN,OAAA;cAAAE,QAAA,gBACEF,OAAA;gBAAAE,QAAA,EAAI;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxCN,OAAA;gBAAAE,QAAA,GAAI,aAAW,eAAAF,OAAA;kBAAAE,QAAA,EAAQ;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,OAAG,eAAAN,OAAA;kBAAAE,QAAA,EAAQ;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,4BAAqB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzGN,OAAA;gBAAAE,QAAA,EAAI;cAAiO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACLN,OAAA;YAAAE,QAAA,EAAI;UAAwD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACLN,OAAA;QAAAE,QAAA,gBAAIF,OAAA;UAAAE,QAAA,EAAQ;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAC1C,eAAAN,OAAA;UAAAE,QAAA,gBACEF,OAAA;YAAAE,QAAA,EAAI;UAAmF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5FN,OAAA;YAAAE,QAAA,EAAI;UAAsE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/EN,OAAA;YAAAE,QAAA,EAAI;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACLN,OAAA;QAAAE,QAAA,gBAAIF,OAAA;UAAAE,QAAA,EAAQ;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAChC,eAAAN,OAAA;UAAAE,QAAA,gBACEF,OAAA;YAAAE,QAAA,EAAI;UAAkD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3DN,OAAA;YAAAE,QAAA,GAAI,uDAAkD,eAAAF,OAAA;cAAAE,QAAA,EAAQ;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,oCAA8B,eAAAN,OAAA;cAAAE,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,6BAAsB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChKN,OAAA;YAAAE,QAAA,EAAI;UAA4G;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrHN,OAAA;YAAAE,QAAA,EAAI;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACLN,OAAA;QAAAE,QAAA,gBAAIF,OAAA;UAAAE,QAAA,EAAQ;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAClC,eAAAN,OAAA;UAAAE,QAAA,eACEF,OAAA;YAAAE,QAAA,EAAI;UAAoF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAELN,OAAA;MAAAE,QAAA,EAAI;IAAwD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACjEN,OAAA;MAAAE,QAAA,EAAG;IAAsE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAC7EN,OAAA;MAAAE,QAAA,gBACEF,OAAA;QAAAE,QAAA,gBAAIF,OAAA;UAAAE,QAAA,EAAQ;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAC9C,eAAAN,OAAA;UAAAE,QAAA,gBACEF,OAAA;YAAAE,QAAA,GAAI,iDAA4C,eAAAF,OAAA;cAAAE,QAAA,EAAQ;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,8BAA0B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/GN,OAAA;YAAAE,QAAA,GAAI,wBAAsB,eAAAF,OAAA;cAAAE,QAAA,EAAQ;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,aAAS;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrEN,OAAA;YAAAE,QAAA,EAAI;UAA+N;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxON,OAAA;YAAAE,QAAA,EAAI;UAAqF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACLN,OAAA;QAAAE,QAAA,gBAAIF,OAAA;UAAAE,QAAA,EAAQ;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KACrC,eAAAN,OAAA;UAAAE,QAAA,eACEF,OAAA;YAAAE,QAAA,EAAI;UAAuM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9M,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACLN,OAAA;QAAAE,QAAA,gBAAIF,OAAA;UAAAE,QAAA,EAAQ;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KACjC,eAAAN,OAAA;UAAAE,QAAA,gBACEF,OAAA;YAAAE,QAAA,EAAI;UAA6G;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtHN,OAAA;YAAAE,QAAA,EAAI;UAA6K;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtLN,OAAA;YAAAE,QAAA,EAAI;UAAoF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACLN,OAAA;QAAAE,QAAA,gBAAIF,OAAA;UAAAE,QAAA,EAAQ;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KACpC,eAAAN,OAAA;UAAAE,QAAA,eACEF,OAAA;YAAAE,QAAA,EAAI;UAAsI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7I,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAELN,OAAA;MAAAE,QAAA,EAAI;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC9BN,OAAA;MAAAE,QAAA,gBACEF,OAAA;QAAAE,QAAA,EAAI;MAAoH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7HN,OAAA;QAAAE,QAAA,EAAI;MAA8I;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEhB;AAACC,EAAA,GA1FQN,WAAW;AA4FpB,eAAeA,WAAW;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}