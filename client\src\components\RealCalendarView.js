import React, { useState, useEffect } from 'react';
import { Row, Col, Button, Alert } from 'react-bootstrap';
import Calendar from 'react-calendar';
import RealDayModal from './RealDayModal';
import { getAllRealDays } from '../services/api';

function RealCalendarView({ onDateSelect, selectedDate, onCalendarUpdate }) {
  const [realDays, setRealDays] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [selectedDay, setSelectedDay] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    loadRealDays();
  }, []);

  const loadRealDays = async () => {
    try {
      setLoading(true);
      const days = await getAllRealDays();
      setRealDays(days);
      setError('');
    } catch (err) {
      console.error('Error loading real days:', err);
      setError('Error cargando días reales: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDateClick = (date) => {
    onDateSelect(date);
    const existingDay = realDays.find(day => 
      new Date(day.date).toDateString() === date.toDateString()
    );
    setSelectedDay(existingDay);
    setShowModal(true);
  };

  const handleDayUpdated = () => {
    loadRealDays();
    onCalendarUpdate();
    setShowModal(false);
  };

  const getTileContent = ({ date, view }) => {
    if (view !== 'month') return null;
    
    const dayData = realDays.find(day => 
      new Date(day.date).toDateString() === date.toDateString()
    );
    
    if (dayData) {
      return (
        <div className="calendar-tile-content">
          <div className="badge bg-primary">{dayData.hours}h</div>
          {dayData.entryTime && dayData.exitTime && (
            <div className="time-range">
              {dayData.entryTime}-{dayData.exitTime}
            </div>
          )}
        </div>
      );
    }
    return null;
  };

  const getTileClassName = ({ date, view }) => {
    if (view !== 'month') return '';
    
    const dayData = realDays.find(day => 
      new Date(day.date).toDateString() === date.toDateString()
    );
    
    if (dayData) {
      return 'has-real-work';
    }
    return '';
  };

  const getTotalHours = () => {
    return realDays.reduce((total, day) => total + (day.hours || 0), 0);
  };

  const getCurrentMonthHours = () => {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    return realDays
      .filter(day => {
        const dayDate = new Date(day.date);
        return dayDate.getMonth() === currentMonth && dayDate.getFullYear() === currentYear;
      })
      .reduce((total, day) => total + (day.hours || 0), 0);
  };

  return (
    <div>
      <Row className="mb-3">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <h4>Calendario de Trabajo Real</h4>
            <div>
              <Button
                variant="primary"
                onClick={() => {
                  setSelectedDay(null);
                  setShowModal(true);
                }}
              >
                ➕ Registrar Día
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {error && (
        <Row className="mb-3">
          <Col>
            <Alert variant="danger" onClose={() => setError('')} dismissible>
              {error}
            </Alert>
          </Col>
        </Row>
      )}

      <Row className="mb-3">
        <Col md={8}>
          <div className="calendar-container">
            <Calendar
              onChange={onDateSelect}
              value={selectedDate}
              onClickDay={handleDateClick}
              tileContent={getTileContent}
              tileClassName={getTileClassName}
              locale="es-ES"
            />
          </div>
        </Col>
        <Col md={4}>
          <div className="calendar-stats">
            <h5>Estadísticas</h5>
            <div className="stat-item">
              <strong>Total de días registrados:</strong> {realDays.length}
            </div>
            <div className="stat-item">
              <strong>Total de horas:</strong> {getTotalHours().toFixed(1)}h
            </div>
            <div className="stat-item">
              <strong>Horas este mes:</strong> {getCurrentMonthHours().toFixed(1)}h
            </div>
            <div className="stat-item">
              <strong>Promedio por día:</strong> {realDays.length > 0 ? (getTotalHours() / realDays.length).toFixed(1) : 0}h
            </div>
          </div>

          {selectedDate && (
            <div className="selected-date-info mt-3">
              <h6>Fecha seleccionada:</h6>
              <p>{selectedDate.toLocaleDateString('es-ES', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}</p>
              
              {realDays.find(day => 
                new Date(day.date).toDateString() === selectedDate.toDateString()
              ) ? (
                <div className="day-details">
                  {(() => {
                    const dayData = realDays.find(day => 
                      new Date(day.date).toDateString() === selectedDate.toDateString()
                    );
                    return (
                      <div>
                        <p><strong>Horas:</strong> {dayData.hours}h</p>
                        {dayData.entryTime && <p><strong>Entrada:</strong> {dayData.entryTime}</p>}
                        {dayData.exitTime && <p><strong>Salida:</strong> {dayData.exitTime}</p>}
                        {dayData.description && <p><strong>Descripción:</strong> {dayData.description}</p>}
                        <Button 
                          variant="outline-primary" 
                          size="sm"
                          onClick={() => handleDateClick(selectedDate)}
                        >
                          ✏️ Editar
                        </Button>
                      </div>
                    );
                  })()}
                </div>
              ) : (
                <div>
                  <p className="text-muted">No hay registro para este día</p>
                  <Button 
                    variant="outline-success" 
                    size="sm"
                    onClick={() => handleDateClick(selectedDate)}
                  >
                    ➕ Registrar
                  </Button>
                </div>
              )}
            </div>
          )}
        </Col>
      </Row>

      <RealDayModal
        show={showModal}
        onHide={() => setShowModal(false)}
        selectedDate={selectedDate}
        existingDay={selectedDay}
        onDayUpdated={handleDayUpdated}
      />
    </div>
  );
}

export default RealCalendarView;
