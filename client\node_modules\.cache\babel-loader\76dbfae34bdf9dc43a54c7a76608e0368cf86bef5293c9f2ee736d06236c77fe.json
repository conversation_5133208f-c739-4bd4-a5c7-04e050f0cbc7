{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\CalendarPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Modal, Form, Alert, Badge, Tabs, Tab } from 'react-bootstrap';\nimport DualCalendarView from '../components/DualCalendarView';\nimport RealCalendarManager from '../components/RealCalendarManager';\nimport CalendarAnalysis from '../components/CalendarAnalysis';\nimport { getDayByDate, createOrUpdateDay, getAllShifts, applyShiftToDate, suggestShiftsForDate, applyPattern, getAllPatterns, clearPlannedCalendar } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction CalendarPage() {\n  _s();\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('calendar');\n\n  // Estados para modales\n  const [showRealDayModal, setShowRealDayModal] = useState(false);\n  const [showPatternModal, setShowPatternModal] = useState(false);\n\n  // Estados para aplicación de patrones\n  const [selectionStartDate, setSelectionStartDate] = useState(null);\n  const [selectionEndDate, setSelectionEndDate] = useState(null);\n  const [allPatterns, setAllPatterns] = useState([]);\n  const [selectedPatternId, setSelectedPatternId] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estado para forzar actualización del calendario\n  const [calendarKey, setCalendarKey] = useState(0);\n  useEffect(() => {\n    loadAllPatterns();\n  }, []);\n  const loadAllPatterns = async () => {\n    try {\n      const patterns = await getAllPatterns();\n      setAllPatterns(patterns);\n    } catch (error) {\n      console.error('Error loading patterns:', error);\n      setError('Error cargando patrones.');\n    }\n  };\n  const handleDateSelect = date => {\n    setSelectedDate(date);\n  };\n  const handleRealDayUpdated = () => {\n    // Forzar actualización del calendario\n    setCalendarKey(prev => prev + 1);\n  };\n  const handleClearPlannedCalendar = async () => {\n    if (window.confirm('¿Estás seguro de que quieres limpiar todo el calendario planificado?')) {\n      try {\n        setLoading(true);\n        await clearPlannedCalendar();\n        setCalendarKey(prev => prev + 1);\n        setError('');\n      } catch (err) {\n        setError('Error limpiando calendario planificado: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  const handlePatternDateClick = value => {\n    if (!selectionStartDate) {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    } else if (!selectionEndDate) {\n      if (value < selectionStartDate) {\n        setSelectionEndDate(selectionStartDate);\n        setSelectionStartDate(value);\n      } else {\n        setSelectionEndDate(value);\n      }\n    } else {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    }\n  };\n  const handleApplyPattern = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      const formattedStartDate = selectionStartDate.toISOString().split('T')[0];\n      const formattedEndDate = selectionEndDate.toISOString().split('T')[0];\n      await applyPattern(formattedStartDate, formattedEndDate, selectedPatternId);\n      setSelectionStartDate(null);\n      setSelectionEndDate(null);\n      setSelectedPatternId('');\n      // Optionally, refresh calendar data here if needed\n      alert('Patrón aplicado exitosamente!');\n    } catch (error) {\n      console.error('Error applying pattern:', error);\n      setError('Error aplicando el patrón. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"justify-content-md-center\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: \"auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Calendario de D\\xEDas Trabajados\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Calendar, {\n          onChange: setDate,\n          value: date,\n          onClickDay: handleDateClick,\n          tileClassName: ({\n            date,\n            view\n          }) => {\n            if (view === 'month') {\n              if (selectionStartDate && selectionEndDate) {\n                if (date >= selectionStartDate && date <= selectionEndDate) {\n                  return 'selected-range';\n                }\n              } else if (selectionStartDate && date.toDateString() === selectionStartDate.toDateString()) {\n                return 'selected-range-start';\n              }\n            }\n            return null;\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"justify-content-md-center mt-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: \"auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Aplicar Patr\\xF3n a Rango Seleccionado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"mb-3\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Fechas Seleccionadas:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              value: selectionStartDate && selectionEndDate ? `${selectionStartDate.toDateString()} - ${selectionEndDate.toDateString()}` : selectionStartDate ? selectionStartDate.toDateString() : 'Ninguna',\n              readOnly: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Seleccionar Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: selectedPatternId,\n              onChange: e => setSelectedPatternId(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Seleccionar patr\\xF3n...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), allPatterns.map(pattern => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: pattern.id,\n                children: pattern.name\n              }, pattern.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleApplyPattern,\n            disabled: loading || !selectedPatternId || !selectionStartDate || !selectionEndDate,\n            children: loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => {\n              setSelectionStartDate(null);\n              setSelectionEndDate(null);\n              setSelectedPatternId('');\n              setError('');\n            },\n            className: \"ms-2\",\n            children: \"Limpiar Selecci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [\"Detalles del D\\xEDa: \", selectedDay && selectedDay.toDateString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"mb-3\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n              type: \"radio\",\n              id: \"manual-entry\",\n              name: \"entry-method\",\n              label: \"Entrada manual\",\n              checked: !useShift,\n              onChange: () => setUseShift(false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n              type: \"radio\",\n              id: \"shift-entry\",\n              name: \"entry-method\",\n              label: \"Usar turno predefinido\",\n              checked: useShift,\n              onChange: () => setUseShift(true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), useShift && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [suggestedShifts.length > 0 && /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Turnos sugeridos para esta fecha:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex flex-wrap gap-2 mb-2\",\n                children: suggestedShifts.map(shift => /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: selectedShift === shift.id ? \"primary\" : \"secondary\",\n                  style: {\n                    cursor: 'pointer'\n                  },\n                  onClick: () => handleShiftChange(shift.id),\n                  children: [shift.name, \" (\", shift.startTime, \" - \", shift.endTime, \")\"]\n                }, shift.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Seleccionar Turno\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: selectedShift,\n                onChange: e => handleShiftChange(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Seleccionar turno...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this), availableShifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: shift.id,\n                  children: [shift.name, \" - \", shift.startTime, \" a \", shift.endTime, \" (\", shift.totalHours, \"h)\"]\n                }, shift.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), !useShift && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Tipo de D\\xEDa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: dayType,\n                onChange: e => setDayType(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Seleccionar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"worked\",\n                  children: \"Trabajado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"holiday\",\n                  children: \"Vacaciones\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"permit\",\n                  children: \"Permiso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"negative\",\n                  children: \"C\\xF3mputo Negativo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Horas (C\\xF3mputo Positivo/Negativo)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"number\",\n                value: hours,\n                onChange: e => setHours(parseFloat(e.target.value)),\n                placeholder: \"Introduce horas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Descripci\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              value: description,\n              onChange: e => setDescription(e.target.value),\n              placeholder: \"A\\xF1ade una descripci\\xF3n (opcional)\",\n              disabled: useShift && selectedShift && !description.includes('Personalizado:')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this), useShift && selectedShift && /*#__PURE__*/_jsxDEV(Form.Text, {\n              className: \"text-muted\",\n              children: \"La descripci\\xF3n se genera autom\\xE1ticamente. Puedes editarla si es necesario.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => {\n            setShowModal(false);\n            resetForm();\n          },\n          disabled: loading,\n          children: \"Cerrar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSave,\n          disabled: loading || !useShift && !dayType || useShift && !selectedShift,\n          children: loading ? 'Guardando...' : 'Guardar Cambios'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n}\n_s(CalendarPage, \"uITU9TjMMwqnQXFPHuKhrf0iCQM=\");\n_c = CalendarPage;\nexport default CalendarPage;\nvar _c;\n$RefreshReg$(_c, \"CalendarPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "<PERSON><PERSON>", "Modal", "Form", "<PERSON><PERSON>", "Badge", "Tabs", "Tab", "DualCalendarView", "RealCalendarManager", "CalendarAnalysis", "getDayByDate", "createOrUpdateDay", "getAllShifts", "applyShiftToDate", "suggestShiftsForDate", "applyPattern", "getAllPatterns", "clearPlannedCalendar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CalendarPage", "_s", "selectedDate", "setSelectedDate", "Date", "activeTab", "setActiveTab", "showRealDayModal", "setShowRealDayModal", "showPatternModal", "setShowPatternModal", "selectionStartDate", "setSelectionStartDate", "selectionEndDate", "setSelectionEndDate", "allPatterns", "setAllPatterns", "selectedPatternId", "setSelectedPatternId", "loading", "setLoading", "error", "setError", "calendarKey", "setCalendarKey", "loadAllPatterns", "patterns", "console", "handleDateSelect", "date", "handleRealDayUpdated", "prev", "handleClearPlannedCalendar", "window", "confirm", "err", "message", "handlePatternDateClick", "value", "handleApplyPattern", "formattedStartDate", "toISOString", "split", "formattedEndDate", "alert", "children", "className", "md", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Calendar", "onChange", "setDate", "onClickDay", "handleDateClick", "tileClassName", "view", "toDateString", "variant", "Group", "Label", "Control", "type", "readOnly", "Select", "e", "target", "map", "pattern", "id", "name", "onClick", "disabled", "show", "showModal", "onHide", "setShowModal", "Header", "closeButton", "Title", "selected<PERSON>ay", "Body", "Check", "label", "checked", "useShift", "setUseShift", "suggestedShifts", "length", "shift", "bg", "selectedShift", "style", "cursor", "handleShiftChange", "startTime", "endTime", "availableShifts", "totalHours", "dayType", "setDayType", "hours", "setHours", "parseFloat", "placeholder", "as", "rows", "description", "setDescription", "includes", "Text", "Footer", "resetForm", "handleSave", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/CalendarPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON>er, Row, Col, Button, Modal, Form, Alert, Badge, Tabs, Tab } from 'react-bootstrap';\nimport DualCalendarView from '../components/DualCalendarView';\nimport RealCalendarManager from '../components/RealCalendarManager';\nimport CalendarAnalysis from '../components/CalendarAnalysis';\nimport {\n  getDayByDate,\n  createOrUpdateDay,\n  getAllShifts,\n  applyShiftToDate,\n  suggestShiftsForDate,\n  applyPattern,\n  getAllPatterns,\n  clearPlannedCalendar\n} from '../services/api';\n\nfunction CalendarPage() {\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('calendar');\n\n  // Estados para modales\n  const [showRealDayModal, setShowRealDayModal] = useState(false);\n  const [showPatternModal, setShowPatternModal] = useState(false);\n\n  // Estados para aplicación de patrones\n  const [selectionStartDate, setSelectionStartDate] = useState(null);\n  const [selectionEndDate, setSelectionEndDate] = useState(null);\n  const [allPatterns, setAllPatterns] = useState([]);\n  const [selectedPatternId, setSelectedPatternId] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estado para forzar actualización del calendario\n  const [calendarKey, setCalendarKey] = useState(0);\n\n  useEffect(() => {\n    loadAllPatterns();\n  }, []);\n\n  const loadAllPatterns = async () => {\n    try {\n      const patterns = await getAllPatterns();\n      setAllPatterns(patterns);\n    } catch (error) {\n      console.error('Error loading patterns:', error);\n      setError('Error cargando patrones.');\n    }\n  };\n\n  const handleDateSelect = (date) => {\n    setSelectedDate(date);\n  };\n\n  const handleRealDayUpdated = () => {\n    // Forzar actualización del calendario\n    setCalendarKey(prev => prev + 1);\n  };\n\n  const handleClearPlannedCalendar = async () => {\n    if (window.confirm('¿Estás seguro de que quieres limpiar todo el calendario planificado?')) {\n      try {\n        setLoading(true);\n        await clearPlannedCalendar();\n        setCalendarKey(prev => prev + 1);\n        setError('');\n      } catch (err) {\n        setError('Error limpiando calendario planificado: ' + err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  const handlePatternDateClick = (value) => {\n    if (!selectionStartDate) {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    } else if (!selectionEndDate) {\n      if (value < selectionStartDate) {\n        setSelectionEndDate(selectionStartDate);\n        setSelectionStartDate(value);\n      } else {\n        setSelectionEndDate(value);\n      }\n    } else {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    }\n  };\n\n  const handleApplyPattern = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      const formattedStartDate = selectionStartDate.toISOString().split('T')[0];\n      const formattedEndDate = selectionEndDate.toISOString().split('T')[0];\n      await applyPattern(formattedStartDate, formattedEndDate, selectedPatternId);\n      setSelectionStartDate(null);\n      setSelectionEndDate(null);\n      setSelectedPatternId('');\n      // Optionally, refresh calendar data here if needed\n      alert('Patrón aplicado exitosamente!');\n    } catch (error) {\n      console.error('Error applying pattern:', error);\n      setError('Error aplicando el patrón. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Container>\n      <Row className=\"justify-content-md-center\">\n        <Col md=\"auto\">\n          <h2>Calendario de Días Trabajados</h2>\n          <Calendar\n            onChange={setDate}\n            value={date}\n            onClickDay={handleDateClick}\n            tileClassName={({ date, view }) => {\n              if (view === 'month') {\n                if (selectionStartDate && selectionEndDate) {\n                  if (date >= selectionStartDate && date <= selectionEndDate) {\n                    return 'selected-range';\n                  }\n                } else if (selectionStartDate && date.toDateString() === selectionStartDate.toDateString()) {\n                  return 'selected-range-start';\n                }\n              }\n              return null;\n            }}\n          />\n\n        </Col>\n      </Row>\n\n      <Row className=\"justify-content-md-center mt-3\">\n        <Col md=\"auto\">\n          <h3>Aplicar Patrón a Rango Seleccionado</h3>\n          {error && (\n            <Alert variant=\"danger\" className=\"mb-3\">\n              {error}\n            </Alert>\n          )}\n          <Form>\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Fechas Seleccionadas:</Form.Label>\n              <Form.Control\n                type=\"text\"\n                value={\n                  selectionStartDate && selectionEndDate\n                    ? `${selectionStartDate.toDateString()} - ${selectionEndDate.toDateString()}`\n                    : selectionStartDate\n                      ? selectionStartDate.toDateString()\n                      : 'Ninguna'\n                }\n                readOnly\n              />\n            </Form.Group>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Seleccionar Patrón</Form.Label>\n              <Form.Select\n                value={selectedPatternId}\n                onChange={(e) => setSelectedPatternId(e.target.value)}\n              >\n                <option value=\"\">Seleccionar patrón...</option>\n                {allPatterns.map((pattern) => (\n                  <option key={pattern.id} value={pattern.id}>\n                    {pattern.name}\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n\n            <Button\n              variant=\"primary\"\n              onClick={handleApplyPattern}\n              disabled={loading || !selectedPatternId || !selectionStartDate || !selectionEndDate}\n            >\n              {loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'}\n            </Button>\n            <Button\n              variant=\"secondary\"\n              onClick={() => {\n                setSelectionStartDate(null);\n                setSelectionEndDate(null);\n                setSelectedPatternId('');\n                setError('');\n              }}\n              className=\"ms-2\"\n            >\n              Limpiar Selección\n            </Button>\n          </Form>\n        </Col>\n      </Row>\n\n      <Modal show={showModal} onHide={() => setShowModal(false)}>\n        <Modal.Header closeButton>\n          <Modal.Title>Detalles del Día: {selectedDay && selectedDay.toDateString()}</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {error && (\n            <Alert variant=\"danger\" className=\"mb-3\">\n              {error}\n            </Alert>\n          )}\n\n          <Form>\n            {/* Selector de método de entrada */}\n            <Form.Group className=\"mb-3\">\n              <Form.Check\n                type=\"radio\"\n                id=\"manual-entry\"\n                name=\"entry-method\"\n                label=\"Entrada manual\"\n                checked={!useShift}\n                onChange={() => setUseShift(false)}\n              />\n              <Form.Check\n                type=\"radio\"\n                id=\"shift-entry\"\n                name=\"entry-method\"\n                label=\"Usar turno predefinido\"\n                checked={useShift}\n                onChange={() => setUseShift(true)}\n              />\n            </Form.Group>\n\n            {/* Sección de turnos predefinidos */}\n            {useShift && (\n              <>\n                {suggestedShifts.length > 0 && (\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Turnos sugeridos para esta fecha:</Form.Label>\n                    <div className=\"d-flex flex-wrap gap-2 mb-2\">\n                      {suggestedShifts.map((shift) => (\n                        <Badge\n                          key={shift.id}\n                          bg={selectedShift === shift.id ? \"primary\" : \"secondary\"}\n                          style={{ cursor: 'pointer' }}\n                          onClick={() => handleShiftChange(shift.id)}\n                        >\n                          {shift.name} ({shift.startTime} - {shift.endTime})\n                        </Badge>\n                      ))}\n                    </div>\n                  </Form.Group>\n                )}\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Seleccionar Turno</Form.Label>\n                  <Form.Select\n                    value={selectedShift}\n                    onChange={(e) => handleShiftChange(e.target.value)}\n                  >\n                    <option value=\"\">Seleccionar turno...</option>\n                    {availableShifts.map((shift) => (\n                      <option key={shift.id} value={shift.id}>\n                        {shift.name} - {shift.startTime} a {shift.endTime} ({shift.totalHours}h)\n                      </option>\n                    ))}\n                  </Form.Select>\n                </Form.Group>\n              </>\n            )}\n\n            {/* Sección de entrada manual */}\n            {!useShift && (\n              <>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Tipo de Día</Form.Label>\n                  <Form.Select value={dayType} onChange={(e) => setDayType(e.target.value)}>\n                    <option value=\"\">Seleccionar</option>\n                    <option value=\"worked\">Trabajado</option>\n                    <option value=\"holiday\">Vacaciones</option>\n                    <option value=\"permit\">Permiso</option>\n                    <option value=\"negative\">Cómputo Negativo</option>\n                  </Form.Select>\n                </Form.Group>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Horas (Cómputo Positivo/Negativo)</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    value={hours}\n                    onChange={(e) => setHours(parseFloat(e.target.value))}\n                    placeholder=\"Introduce horas\"\n                  />\n                </Form.Group>\n              </>\n            )}\n\n            {/* Descripción (común para ambos métodos) */}\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Descripción</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={3}\n                value={description}\n                onChange={(e) => setDescription(e.target.value)}\n                placeholder=\"Añade una descripción (opcional)\"\n                disabled={useShift && selectedShift && !description.includes('Personalizado:')}\n              />\n              {useShift && selectedShift && (\n                <Form.Text className=\"text-muted\">\n                  La descripción se genera automáticamente. Puedes editarla si es necesario.\n                </Form.Text>\n              )}\n            </Form.Group>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button\n            variant=\"secondary\"\n            onClick={() => {\n              setShowModal(false);\n              resetForm();\n            }}\n            disabled={loading}\n          >\n            Cerrar\n          </Button>\n          <Button\n            variant=\"primary\"\n            onClick={handleSave}\n            disabled={loading || (!useShift && !dayType) || (useShift && !selectedShift)}\n          >\n            {loading ? 'Guardando...' : 'Guardar Cambios'}\n          </Button>\n        </Modal.Footer>\n      </Modal>\n\n\n    </Container>\n  );\n}\n\nexport default CalendarPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,QAAQ,iBAAiB;AACnG,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,SACEC,YAAY,EACZC,iBAAiB,EACjBC,YAAY,EACZC,gBAAgB,EAChBC,oBAAoB,EACpBC,YAAY,EACZC,cAAc,EACdC,oBAAoB,QACf,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,IAAI+B,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,UAAU,CAAC;;EAEtD;EACA,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACsC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgD,KAAK,EAAEC,QAAQ,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACdmD,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMhC,cAAc,CAAC,CAAC;MACvCsB,cAAc,CAACU,QAAQ,CAAC;IAC1B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,0BAA0B,CAAC;IACtC;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAIC,IAAI,IAAK;IACjC1B,eAAe,CAAC0B,IAAI,CAAC;EACvB,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAN,cAAc,CAACO,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;EAClC,CAAC;EAED,MAAMC,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAIC,MAAM,CAACC,OAAO,CAAC,sEAAsE,CAAC,EAAE;MAC1F,IAAI;QACFd,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMzB,oBAAoB,CAAC,CAAC;QAC5B6B,cAAc,CAACO,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAChCT,QAAQ,CAAC,EAAE,CAAC;MACd,CAAC,CAAC,OAAOa,GAAG,EAAE;QACZb,QAAQ,CAAC,0CAA0C,GAAGa,GAAG,CAACC,OAAO,CAAC;MACpE,CAAC,SAAS;QACRhB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;EAED,MAAMiB,sBAAsB,GAAIC,KAAK,IAAK;IACxC,IAAI,CAAC3B,kBAAkB,EAAE;MACvBC,qBAAqB,CAAC0B,KAAK,CAAC;MAC5BxB,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM,IAAI,CAACD,gBAAgB,EAAE;MAC5B,IAAIyB,KAAK,GAAG3B,kBAAkB,EAAE;QAC9BG,mBAAmB,CAACH,kBAAkB,CAAC;QACvCC,qBAAqB,CAAC0B,KAAK,CAAC;MAC9B,CAAC,MAAM;QACLxB,mBAAmB,CAACwB,KAAK,CAAC;MAC5B;IACF,CAAC,MAAM;MACL1B,qBAAqB,CAAC0B,KAAK,CAAC;MAC5BxB,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAED,MAAMyB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrCnB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,MAAMkB,kBAAkB,GAAG7B,kBAAkB,CAAC8B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACzE,MAAMC,gBAAgB,GAAG9B,gBAAgB,CAAC4B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACrE,MAAMjD,YAAY,CAAC+C,kBAAkB,EAAEG,gBAAgB,EAAE1B,iBAAiB,CAAC;MAC3EL,qBAAqB,CAAC,IAAI,CAAC;MAC3BE,mBAAmB,CAAC,IAAI,CAAC;MACzBI,oBAAoB,CAAC,EAAE,CAAC;MACxB;MACA0B,KAAK,CAAC,+BAA+B,CAAC;IACxC,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,2DAA2D,CAAC;IACvE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEvB,OAAA,CAACtB,SAAS;IAAAsE,QAAA,gBACRhD,OAAA,CAACrB,GAAG;MAACsE,SAAS,EAAC,2BAA2B;MAAAD,QAAA,eACxChD,OAAA,CAACpB,GAAG;QAACsE,EAAE,EAAC,MAAM;QAAAF,QAAA,gBACZhD,OAAA;UAAAgD,QAAA,EAAI;QAA6B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCtD,OAAA,CAACuD,QAAQ;UACPC,QAAQ,EAAEC,OAAQ;UAClBhB,KAAK,EAAET,IAAK;UACZ0B,UAAU,EAAEC,eAAgB;UAC5BC,aAAa,EAAEA,CAAC;YAAE5B,IAAI;YAAE6B;UAAK,CAAC,KAAK;YACjC,IAAIA,IAAI,KAAK,OAAO,EAAE;cACpB,IAAI/C,kBAAkB,IAAIE,gBAAgB,EAAE;gBAC1C,IAAIgB,IAAI,IAAIlB,kBAAkB,IAAIkB,IAAI,IAAIhB,gBAAgB,EAAE;kBAC1D,OAAO,gBAAgB;gBACzB;cACF,CAAC,MAAM,IAAIF,kBAAkB,IAAIkB,IAAI,CAAC8B,YAAY,CAAC,CAAC,KAAKhD,kBAAkB,CAACgD,YAAY,CAAC,CAAC,EAAE;gBAC1F,OAAO,sBAAsB;cAC/B;YACF;YACA,OAAO,IAAI;UACb;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtD,OAAA,CAACrB,GAAG;MAACsE,SAAS,EAAC,gCAAgC;MAAAD,QAAA,eAC7ChD,OAAA,CAACpB,GAAG;QAACsE,EAAE,EAAC,MAAM;QAAAF,QAAA,gBACZhD,OAAA;UAAAgD,QAAA,EAAI;QAAmC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC3C9B,KAAK,iBACJxB,OAAA,CAAChB,KAAK;UAAC+E,OAAO,EAAC,QAAQ;UAACd,SAAS,EAAC,MAAM;UAAAD,QAAA,EACrCxB;QAAK;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eACDtD,OAAA,CAACjB,IAAI;UAAAiE,QAAA,gBACHhD,OAAA,CAACjB,IAAI,CAACiF,KAAK;YAACf,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BhD,OAAA,CAACjB,IAAI,CAACkF,KAAK;cAAAjB,QAAA,EAAC;YAAqB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9CtD,OAAA,CAACjB,IAAI,CAACmF,OAAO;cACXC,IAAI,EAAC,MAAM;cACX1B,KAAK,EACH3B,kBAAkB,IAAIE,gBAAgB,GAClC,GAAGF,kBAAkB,CAACgD,YAAY,CAAC,CAAC,MAAM9C,gBAAgB,CAAC8C,YAAY,CAAC,CAAC,EAAE,GAC3EhD,kBAAkB,GAChBA,kBAAkB,CAACgD,YAAY,CAAC,CAAC,GACjC,SACP;cACDM,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEbtD,OAAA,CAACjB,IAAI,CAACiF,KAAK;YAACf,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BhD,OAAA,CAACjB,IAAI,CAACkF,KAAK;cAAAjB,QAAA,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3CtD,OAAA,CAACjB,IAAI,CAACsF,MAAM;cACV5B,KAAK,EAAErB,iBAAkB;cACzBoC,QAAQ,EAAGc,CAAC,IAAKjD,oBAAoB,CAACiD,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;cAAAO,QAAA,gBAEtDhD,OAAA;gBAAQyC,KAAK,EAAC,EAAE;gBAAAO,QAAA,EAAC;cAAqB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9CpC,WAAW,CAACsD,GAAG,CAAEC,OAAO,iBACvBzE,OAAA;gBAAyByC,KAAK,EAAEgC,OAAO,CAACC,EAAG;gBAAA1B,QAAA,EACxCyB,OAAO,CAACE;cAAI,GADFF,OAAO,CAACC,EAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEbtD,OAAA,CAACnB,MAAM;YACLkF,OAAO,EAAC,SAAS;YACjBa,OAAO,EAAElC,kBAAmB;YAC5BmC,QAAQ,EAAEvD,OAAO,IAAI,CAACF,iBAAiB,IAAI,CAACN,kBAAkB,IAAI,CAACE,gBAAiB;YAAAgC,QAAA,EAEnF1B,OAAO,GAAG,cAAc,GAAG;UAAwB;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACTtD,OAAA,CAACnB,MAAM;YACLkF,OAAO,EAAC,WAAW;YACnBa,OAAO,EAAEA,CAAA,KAAM;cACb7D,qBAAqB,CAAC,IAAI,CAAC;cAC3BE,mBAAmB,CAAC,IAAI,CAAC;cACzBI,oBAAoB,CAAC,EAAE,CAAC;cACxBI,QAAQ,CAAC,EAAE,CAAC;YACd,CAAE;YACFwB,SAAS,EAAC,MAAM;YAAAD,QAAA,EACjB;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtD,OAAA,CAAClB,KAAK;MAACgG,IAAI,EAAEC,SAAU;MAACC,MAAM,EAAEA,CAAA,KAAMC,YAAY,CAAC,KAAK,CAAE;MAAAjC,QAAA,gBACxDhD,OAAA,CAAClB,KAAK,CAACoG,MAAM;QAACC,WAAW;QAAAnC,QAAA,eACvBhD,OAAA,CAAClB,KAAK,CAACsG,KAAK;UAAApC,QAAA,GAAC,uBAAkB,EAACqC,WAAW,IAAIA,WAAW,CAACvB,YAAY,CAAC,CAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,eACftD,OAAA,CAAClB,KAAK,CAACwG,IAAI;QAAAtC,QAAA,GACRxB,KAAK,iBACJxB,OAAA,CAAChB,KAAK;UAAC+E,OAAO,EAAC,QAAQ;UAACd,SAAS,EAAC,MAAM;UAAAD,QAAA,EACrCxB;QAAK;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eAEDtD,OAAA,CAACjB,IAAI;UAAAiE,QAAA,gBAEHhD,OAAA,CAACjB,IAAI,CAACiF,KAAK;YAACf,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BhD,OAAA,CAACjB,IAAI,CAACwG,KAAK;cACTpB,IAAI,EAAC,OAAO;cACZO,EAAE,EAAC,cAAc;cACjBC,IAAI,EAAC,cAAc;cACnBa,KAAK,EAAC,gBAAgB;cACtBC,OAAO,EAAE,CAACC,QAAS;cACnBlC,QAAQ,EAAEA,CAAA,KAAMmC,WAAW,CAAC,KAAK;YAAE;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACFtD,OAAA,CAACjB,IAAI,CAACwG,KAAK;cACTpB,IAAI,EAAC,OAAO;cACZO,EAAE,EAAC,aAAa;cAChBC,IAAI,EAAC,cAAc;cACnBa,KAAK,EAAC,wBAAwB;cAC9BC,OAAO,EAAEC,QAAS;cAClBlC,QAAQ,EAAEA,CAAA,KAAMmC,WAAW,CAAC,IAAI;YAAE;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,EAGZoC,QAAQ,iBACP1F,OAAA,CAAAE,SAAA;YAAA8C,QAAA,GACG4C,eAAe,CAACC,MAAM,GAAG,CAAC,iBACzB7F,OAAA,CAACjB,IAAI,CAACiF,KAAK;cAACf,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BhD,OAAA,CAACjB,IAAI,CAACkF,KAAK;gBAAAjB,QAAA,EAAC;cAAiC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1DtD,OAAA;gBAAKiD,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,EACzC4C,eAAe,CAACpB,GAAG,CAAEsB,KAAK,iBACzB9F,OAAA,CAACf,KAAK;kBAEJ8G,EAAE,EAAEC,aAAa,KAAKF,KAAK,CAACpB,EAAE,GAAG,SAAS,GAAG,WAAY;kBACzDuB,KAAK,EAAE;oBAAEC,MAAM,EAAE;kBAAU,CAAE;kBAC7BtB,OAAO,EAAEA,CAAA,KAAMuB,iBAAiB,CAACL,KAAK,CAACpB,EAAE,CAAE;kBAAA1B,QAAA,GAE1C8C,KAAK,CAACnB,IAAI,EAAC,IAAE,EAACmB,KAAK,CAACM,SAAS,EAAC,KAAG,EAACN,KAAK,CAACO,OAAO,EAAC,GACnD;gBAAA,GANOP,KAAK,CAACpB,EAAE;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMR,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAEDtD,OAAA,CAACjB,IAAI,CAACiF,KAAK;cAACf,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BhD,OAAA,CAACjB,IAAI,CAACkF,KAAK;gBAAAjB,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1CtD,OAAA,CAACjB,IAAI,CAACsF,MAAM;gBACV5B,KAAK,EAAEuD,aAAc;gBACrBxC,QAAQ,EAAGc,CAAC,IAAK6B,iBAAiB,CAAC7B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;gBAAAO,QAAA,gBAEnDhD,OAAA;kBAAQyC,KAAK,EAAC,EAAE;kBAAAO,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC7CgD,eAAe,CAAC9B,GAAG,CAAEsB,KAAK,iBACzB9F,OAAA;kBAAuByC,KAAK,EAAEqD,KAAK,CAACpB,EAAG;kBAAA1B,QAAA,GACpC8C,KAAK,CAACnB,IAAI,EAAC,KAAG,EAACmB,KAAK,CAACM,SAAS,EAAC,KAAG,EAACN,KAAK,CAACO,OAAO,EAAC,IAAE,EAACP,KAAK,CAACS,UAAU,EAAC,IACxE;gBAAA,GAFaT,KAAK,CAACpB,EAAE;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,eACb,CACH,EAGA,CAACoC,QAAQ,iBACR1F,OAAA,CAAAE,SAAA;YAAA8C,QAAA,gBACEhD,OAAA,CAACjB,IAAI,CAACiF,KAAK;cAACf,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BhD,OAAA,CAACjB,IAAI,CAACkF,KAAK;gBAAAjB,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpCtD,OAAA,CAACjB,IAAI,CAACsF,MAAM;gBAAC5B,KAAK,EAAE+D,OAAQ;gBAAChD,QAAQ,EAAGc,CAAC,IAAKmC,UAAU,CAACnC,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;gBAAAO,QAAA,gBACvEhD,OAAA;kBAAQyC,KAAK,EAAC,EAAE;kBAAAO,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCtD,OAAA;kBAAQyC,KAAK,EAAC,QAAQ;kBAAAO,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCtD,OAAA;kBAAQyC,KAAK,EAAC,SAAS;kBAAAO,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3CtD,OAAA;kBAAQyC,KAAK,EAAC,QAAQ;kBAAAO,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCtD,OAAA;kBAAQyC,KAAK,EAAC,UAAU;kBAAAO,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACbtD,OAAA,CAACjB,IAAI,CAACiF,KAAK;cAACf,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BhD,OAAA,CAACjB,IAAI,CAACkF,KAAK;gBAAAjB,QAAA,EAAC;cAAiC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1DtD,OAAA,CAACjB,IAAI,CAACmF,OAAO;gBACXC,IAAI,EAAC,QAAQ;gBACb1B,KAAK,EAAEiE,KAAM;gBACblD,QAAQ,EAAGc,CAAC,IAAKqC,QAAQ,CAACC,UAAU,CAACtC,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAC,CAAE;gBACtDoE,WAAW,EAAC;cAAiB;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA,eACb,CACH,eAGDtD,OAAA,CAACjB,IAAI,CAACiF,KAAK;YAACf,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BhD,OAAA,CAACjB,IAAI,CAACkF,KAAK;cAAAjB,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpCtD,OAAA,CAACjB,IAAI,CAACmF,OAAO;cACX4C,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRtE,KAAK,EAAEuE,WAAY;cACnBxD,QAAQ,EAAGc,CAAC,IAAK2C,cAAc,CAAC3C,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;cAChDoE,WAAW,EAAC,wCAAkC;cAC9ChC,QAAQ,EAAEa,QAAQ,IAAIM,aAAa,IAAI,CAACgB,WAAW,CAACE,QAAQ,CAAC,gBAAgB;YAAE;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,EACDoC,QAAQ,IAAIM,aAAa,iBACxBhG,OAAA,CAACjB,IAAI,CAACoI,IAAI;cAAClE,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAElC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CACZ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbtD,OAAA,CAAClB,KAAK,CAACsI,MAAM;QAAApE,QAAA,gBACXhD,OAAA,CAACnB,MAAM;UACLkF,OAAO,EAAC,WAAW;UACnBa,OAAO,EAAEA,CAAA,KAAM;YACbK,YAAY,CAAC,KAAK,CAAC;YACnBoC,SAAS,CAAC,CAAC;UACb,CAAE;UACFxC,QAAQ,EAAEvD,OAAQ;UAAA0B,QAAA,EACnB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtD,OAAA,CAACnB,MAAM;UACLkF,OAAO,EAAC,SAAS;UACjBa,OAAO,EAAE0C,UAAW;UACpBzC,QAAQ,EAAEvD,OAAO,IAAK,CAACoE,QAAQ,IAAI,CAACc,OAAQ,IAAKd,QAAQ,IAAI,CAACM,aAAe;UAAAhD,QAAA,EAE5E1B,OAAO,GAAG,cAAc,GAAG;QAAiB;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGC,CAAC;AAEhB;AAAClD,EAAA,CAhUQD,YAAY;AAAAoH,EAAA,GAAZpH,YAAY;AAkUrB,eAAeA,YAAY;AAAC,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}