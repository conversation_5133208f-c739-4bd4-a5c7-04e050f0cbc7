{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\CalendarPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Row, Col, Tabs, Tab } from 'react-bootstrap';\nimport RealCalendarView from '../components/RealCalendarView';\nimport PlannedCalendarView from '../components/PlannedCalendarView';\nimport CalendarAnalysis from '../components/CalendarAnalysis';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CalendarPage() {\n  _s();\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('real');\n  const [calendarKey, setCalendarKey] = useState(0);\n  const handleDateSelect = date => {\n    setSelectedDate(date);\n  };\n  const handleCalendarUpdate = () => {\n    // Forzar actualización de todos los calendarios\n    setCalendarKey(prev => prev + 1);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Sistema de Horarios\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n      activeKey: activeTab,\n      onSelect: setActiveTab,\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"real\",\n        title: \"\\uD83D\\uDCC5 Calendario Real\",\n        children: /*#__PURE__*/_jsxDEV(RealCalendarView, {\n          onDateSelect: handleDateSelect,\n          selectedDate: selectedDate,\n          onCalendarUpdate: handleCalendarUpdate\n        }, `real-${calendarKey}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"planned\",\n        title: \"\\uD83D\\uDCCB Calendario Te\\xF3rico\",\n        children: /*#__PURE__*/_jsxDEV(PlannedCalendarView, {\n          onDateSelect: handleDateSelect,\n          selectedDate: selectedDate,\n          onCalendarUpdate: handleCalendarUpdate\n        }, `planned-${calendarKey}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"analysis\",\n        title: \"\\uD83D\\uDCCA An\\xE1lisis\",\n        children: /*#__PURE__*/_jsxDEV(CalendarAnalysis, {\n          selectedDate: selectedDate\n        }, `analysis-${calendarKey}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n}\n_s(CalendarPage, \"wzK0Ju2Td/fRUgmzeDqwaKhFGtc=\");\n_c = CalendarPage;\nexport default CalendarPage;\nvar _c;\n$RefreshReg$(_c, \"CalendarPage\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Row", "Col", "Tabs", "Tab", "RealCalendarView", "PlannedCalendarView", "CalendarAnalysis", "jsxDEV", "_jsxDEV", "CalendarPage", "_s", "selectedDate", "setSelectedDate", "Date", "activeTab", "setActiveTab", "calendarKey", "setCalendarKey", "handleDateSelect", "date", "handleCalendarUpdate", "prev", "fluid", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "active<PERSON><PERSON>", "onSelect", "eventKey", "title", "onDateSelect", "onCalendarUpdate", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/CalendarPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Container, Row, Col, Tabs, Tab } from 'react-bootstrap';\nimport RealCalendarView from '../components/RealCalendarView';\nimport PlannedCalendarView from '../components/PlannedCalendarView';\nimport CalendarAnalysis from '../components/CalendarAnalysis';\n\nfunction CalendarPage() {\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [activeTab, setActiveTab] = useState('real');\n  const [calendarKey, setCalendarKey] = useState(0);\n\n  const handleDateSelect = (date) => {\n    setSelectedDate(date);\n  };\n\n  const handleCalendarUpdate = () => {\n    // Forzar actualización de todos los calendarios\n    setCalendarKey(prev => prev + 1);\n  };\n\n  return (\n    <Container fluid>\n      <Row className=\"mb-3\">\n        <Col>\n          <h2>Sistema de Horarios</h2>\n        </Col>\n      </Row>\n\n      <Tabs activeKey={activeTab} onSelect={setActiveTab} className=\"mb-3\">\n        <Tab eventKey=\"real\" title=\"📅 Calendario Real\">\n          <RealCalendarView\n            key={`real-${calendarKey}`}\n            onDateSelect={handleDateSelect}\n            selectedDate={selectedDate}\n            onCalendarUpdate={handleCalendarUpdate}\n          />\n        </Tab>\n        <Tab eventKey=\"planned\" title=\"📋 Calendario Teórico\">\n          <PlannedCalendarView\n            key={`planned-${calendarKey}`}\n            onDateSelect={handleDateSelect}\n            selectedDate={selectedDate}\n            onCalendarUpdate={handleCalendarUpdate}\n          />\n        </Tab>\n        <Tab eventKey=\"analysis\" title=\"📊 Análisis\">\n          <CalendarAnalysis\n            key={`analysis-${calendarKey}`}\n            selectedDate={selectedDate}\n          />\n        </Tab>\n      </Tabs>\n    </Container>\n  );\n}\n\nexport default CalendarPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,QAAQ,iBAAiB;AAChE,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,gBAAgB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,IAAIe,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EAEjD,MAAMoB,gBAAgB,GAAIC,IAAI,IAAK;IACjCP,eAAe,CAACO,IAAI,CAAC;EACvB,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAH,cAAc,CAACI,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;EAClC,CAAC;EAED,oBACEb,OAAA,CAACT,SAAS;IAACuB,KAAK;IAAAC,QAAA,gBACdf,OAAA,CAACR,GAAG;MAACwB,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBf,OAAA,CAACP,GAAG;QAAAsB,QAAA,eACFf,OAAA;UAAAe,QAAA,EAAI;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpB,OAAA,CAACN,IAAI;MAAC2B,SAAS,EAAEf,SAAU;MAACgB,QAAQ,EAAEf,YAAa;MAACS,SAAS,EAAC,MAAM;MAAAD,QAAA,gBAClEf,OAAA,CAACL,GAAG;QAAC4B,QAAQ,EAAC,MAAM;QAACC,KAAK,EAAC,8BAAoB;QAAAT,QAAA,eAC7Cf,OAAA,CAACJ,gBAAgB;UAEf6B,YAAY,EAAEf,gBAAiB;UAC/BP,YAAY,EAAEA,YAAa;UAC3BuB,gBAAgB,EAAEd;QAAqB,GAHlC,QAAQJ,WAAW,EAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAI3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNpB,OAAA,CAACL,GAAG;QAAC4B,QAAQ,EAAC,SAAS;QAACC,KAAK,EAAC,oCAAuB;QAAAT,QAAA,eACnDf,OAAA,CAACH,mBAAmB;UAElB4B,YAAY,EAAEf,gBAAiB;UAC/BP,YAAY,EAAEA,YAAa;UAC3BuB,gBAAgB,EAAEd;QAAqB,GAHlC,WAAWJ,WAAW,EAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAI9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNpB,OAAA,CAACL,GAAG;QAAC4B,QAAQ,EAAC,UAAU;QAACC,KAAK,EAAC,0BAAa;QAAAT,QAAA,eAC1Cf,OAAA,CAACF,gBAAgB;UAEfK,YAAY,EAAEA;QAAa,GADtB,YAAYK,WAAW,EAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB;AAAClB,EAAA,CAhDQD,YAAY;AAAA0B,EAAA,GAAZ1B,YAAY;AAkDrB,eAAeA,YAAY;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}