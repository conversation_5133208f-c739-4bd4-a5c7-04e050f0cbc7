const patternService = require('../services/patternService');

const getAllPatterns = (req, res) => {
    try {
        const patterns = patternService.getAllPatterns();
        res.json(patterns);
    } catch (error) {
        console.error('Error in getAllPatterns:', error);
        res.status(500).json({ message: error.message });
    }
};

const getPatternById = (req, res) => {
    try {
        const { id } = req.params;
        const pattern = patternService.getPatternById(id);
        if (pattern) {
            res.json(pattern);
        } else {
            res.status(404).json({ message: 'Pattern not found' });
        }
    } catch (error) {
        console.error('Error in getPatternById:', error);
        res.status(500).json({ message: error.message });
    }
};

const createPattern = (req, res) => {
    try {
        const { name, basePattern, overrides } = req.body;
        if (!name || !basePattern || !Array.isArray(basePattern)) {
            return res.status(400).json({ message: 'Name and basePattern array are required.' });
        }
        const newPattern = patternService.createPattern(name, basePattern, overrides || []);
        res.status(201).json(newPattern);
    } catch (error) {
        console.error('Error in createPattern:', error);
        res.status(500).json({ message: error.message });
    }
};

const updatePattern = (req, res) => {
    try {
        const { id } = req.params;
        const { name, basePattern, overrides } = req.body;
        if (!name || !basePattern || !Array.isArray(basePattern)) {
            return res.status(400).json({ message: 'Name and basePattern array are required.' });
        }
        const updatedPattern = patternService.updatePattern(id, name, basePattern, overrides || []);
        if (updatedPattern) {
            res.json(updatedPattern);
        } else {
            res.status(404).json({ message: 'Pattern not found' });
        }
    } catch (error) {
        console.error('Error in updatePattern:', error);
        res.status(500).json({ message: error.message });
    }
    
};

const copyPattern = (req, res) => {
    try {
        const { id } = req.params;
        const { newName, newYear } = req.body;
        if (!newName || !newYear) {
            return res.status(400).json({ message: 'New name and new year are required.' });
        }
        const copiedPattern = patternService.copyPattern(id, newName, newYear);
        if (copiedPattern) {
            res.status(201).json(copiedPattern);
        } else {
            res.status(404).json({ message: 'Original pattern not found' });
        }
    } catch (error) {
        console.error('Error in copyPattern:', error);
        res.status(500).json({ message: error.message });
    }
};

const deletePattern = (req, res) => {
    try {
        const { id } = req.params;
        const deleted = patternService.deletePattern(id);
        if (deleted) {
            res.status(204).send(); // No Content
        } else {
            res.status(404).json({ message: 'Pattern not found' });
        }
    } catch (error) {
        console.error('Error in deletePattern:', error);
        res.status(500).json({ message: error.message });
    }
};

module.exports = {
    getAllPatterns,
    getPatternById,
    createPattern,
    updatePattern,
    deletePattern,
    copyPattern
};