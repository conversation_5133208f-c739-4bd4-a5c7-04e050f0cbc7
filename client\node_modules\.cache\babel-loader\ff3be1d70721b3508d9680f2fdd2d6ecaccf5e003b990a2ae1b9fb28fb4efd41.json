{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\CalendarPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Calendar from 'react-calendar';\nimport 'react-calendar/dist/Calendar.css';\nimport { Container, Row, Col, Button, Modal, Form, Alert, Badge } from 'react-bootstrap';\nimport { getDayByDate, createOrUpdateDay, getAllShifts, applyShiftToDate, suggestShiftsForDate, applyPattern, getAllPatterns } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction CalendarPage() {\n  _s();\n  const [date, setDate] = useState(new Date());\n  const [showModal, setShowModal] = useState(false);\n  const [selectedDay, setSelectedDay] = useState(null);\n  const [dayType, setDayType] = useState('');\n  const [hours, setHours] = useState(0);\n  const [description, setDescription] = useState('');\n\n  // Estados para turnos predefinidos\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [selectedShift, setSelectedShift] = useState('');\n  const [suggestedShifts, setSuggestedShifts] = useState([]);\n  const [useShift, setUseShift] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estados para el calendario patrón\n  const [selectionStartDate, setSelectionStartDate] = useState(null);\n  const [selectionEndDate, setSelectionEndDate] = useState(null);\n  const [allPatterns, setAllPatterns] = useState([]);\n  const [selectedPatternId, setSelectedPatternId] = useState('');\n  useEffect(() => {\n    // Cargar turnos disponibles al montar el componente\n    loadAvailableShifts();\n    loadAllPatterns(); // Load all patterns\n  }, []);\n  useEffect(() => {\n    if (selectedDay) {\n      fetchDayDetails(selectedDay);\n      loadSuggestedShifts(selectedDay);\n    }\n  }, [selectedDay]);\n  const loadAllPatterns = async () => {\n    try {\n      const patterns = await getAllPatterns();\n      setAllPatterns(patterns);\n    } catch (error) {\n      console.error('Error loading patterns:', error);\n      setError('Error cargando patrones.');\n    }\n  };\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (error) {\n      console.error('Error loading shifts:', error);\n      setError('Error cargando turnos predefinidos');\n    }\n  };\n  const loadSuggestedShifts = async date => {\n    try {\n      const formattedDate = date.toISOString().split('T')[0];\n      const suggestions = await suggestShiftsForDate(formattedDate);\n      setSuggestedShifts(suggestions.suggestions || []);\n    } catch (error) {\n      console.error('Error loading suggested shifts:', error);\n      setSuggestedShifts([]);\n    }\n  };\n  const fetchDayDetails = async date => {\n    try {\n      const formattedDate = date.toISOString().split('T')[0];\n      const response = await getDayByDate(formattedDate);\n      if (response) {\n        setDayType(response.type || '');\n        setHours(response.hours || 0);\n        setDescription(response.description || '');\n        // Si el día tiene un turno asignado, mostrar esa información\n        if (response.shift) {\n          setSelectedShift(response.shift.id);\n          setUseShift(true);\n        } else {\n          setSelectedShift('');\n          setUseShift(false);\n        }\n      } else {\n        resetForm();\n      }\n    } catch (error) {\n      console.error('Error fetching day details:', error);\n      resetForm();\n    }\n  };\n  const resetForm = () => {\n    setDayType('');\n    setHours(0);\n    setDescription('');\n    setSelectedShift('');\n    setUseShift(false);\n    setError('');\n  };\n  const handleDateClick = value => {\n    if (!selectionStartDate) {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    } else if (!selectionEndDate) {\n      if (value < selectionStartDate) {\n        setSelectionEndDate(selectionStartDate);\n        setSelectionStartDate(value);\n      } else {\n        setSelectionEndDate(value);\n      }\n    } else {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    }\n    setSelectedDay(value);\n    setShowModal(true);\n    setError('');\n  };\n  const handleShiftChange = shiftId => {\n    setSelectedShift(shiftId);\n    if (shiftId && availableShifts.length > 0) {\n      const shift = availableShifts.find(s => s.id === shiftId);\n      if (shift) {\n        setDayType('worked');\n        setHours(shift.totalHours);\n        setDescription(`${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`);\n      }\n    }\n  };\n  const handleSave = async () => {\n    if (!selectedDay) return;\n    const formattedDate = selectedDay.toISOString().split('T')[0];\n    setLoading(true);\n    setError('');\n    try {\n      if (useShift && selectedShift) {\n        // Usar turno predefinido\n        await applyShiftToDate(formattedDate, selectedShift, description);\n      } else {\n        // Usar entrada manual\n        await createOrUpdateDay(formattedDate, dayType, hours, description);\n      }\n      setShowModal(false);\n      resetForm();\n    } catch (error) {\n      console.error('Error saving day:', error);\n      setError('Error guardando los datos. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleApplyPattern = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      const formattedStartDate = selectionStartDate.toISOString().split('T')[0];\n      const formattedEndDate = selectionEndDate.toISOString().split('T')[0];\n      await applyPattern(formattedStartDate, formattedEndDate, selectedPatternId);\n      setSelectionStartDate(null);\n      setSelectionEndDate(null);\n      setSelectedPatternId('');\n      // Optionally, refresh calendar data here if needed\n      alert('Patrón aplicado exitosamente!');\n    } catch (error) {\n      console.error('Error applying pattern:', error);\n      setError('Error aplicando el patrón. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"justify-content-md-center\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: \"auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Calendario de D\\xEDas Trabajados\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Calendar, {\n          onChange: setDate,\n          value: date,\n          onClickDay: handleDateClick\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"justify-content-md-center mt-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: \"auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Aplicar Patr\\xF3n a Rango Seleccionado\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"mb-3\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Fechas Seleccionadas:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              value: selectionStartDate && selectionEndDate ? `${selectionStartDate.toDateString()} - ${selectionEndDate.toDateString()}` : selectionStartDate ? selectionStartDate.toDateString() : 'Ninguna',\n              readOnly: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Seleccionar Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: selectedPatternId,\n              onChange: e => setSelectedPatternId(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Seleccionar patr\\xF3n...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this), allPatterns.map(pattern => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: pattern.id,\n                children: pattern.name\n              }, pattern.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleApplyPattern,\n            disabled: loading || !selectedPatternId || !selectionStartDate || !selectionEndDate,\n            children: loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => {\n              setSelectionStartDate(null);\n              setSelectionEndDate(null);\n              setSelectedPatternId('');\n              setError('');\n            },\n            className: \"ms-2\",\n            children: \"Limpiar Selecci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [\"Detalles del D\\xEDa: \", selectedDay && selectedDay.toDateString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"mb-3\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n              type: \"radio\",\n              id: \"manual-entry\",\n              name: \"entry-method\",\n              label: \"Entrada manual\",\n              checked: !useShift,\n              onChange: () => setUseShift(false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n              type: \"radio\",\n              id: \"shift-entry\",\n              name: \"entry-method\",\n              label: \"Usar turno predefinido\",\n              checked: useShift,\n              onChange: () => setUseShift(true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), useShift && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [suggestedShifts.length > 0 && /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Turnos sugeridos para esta fecha:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex flex-wrap gap-2 mb-2\",\n                children: suggestedShifts.map(shift => /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: selectedShift === shift.id ? \"primary\" : \"secondary\",\n                  style: {\n                    cursor: 'pointer'\n                  },\n                  onClick: () => handleShiftChange(shift.id),\n                  children: [shift.name, \" (\", shift.startTime, \" - \", shift.endTime, \")\"]\n                }, shift.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Seleccionar Turno\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: selectedShift,\n                onChange: e => handleShiftChange(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Seleccionar turno...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this), availableShifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: shift.id,\n                  children: [shift.name, \" - \", shift.startTime, \" a \", shift.endTime, \" (\", shift.totalHours, \"h)\"]\n                }, shift.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), !useShift && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Tipo de D\\xEDa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: dayType,\n                onChange: e => setDayType(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Seleccionar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"worked\",\n                  children: \"Trabajado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"holiday\",\n                  children: \"Vacaciones\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"permit\",\n                  children: \"Permiso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"negative\",\n                  children: \"C\\xF3mputo Negativo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Horas (C\\xF3mputo Positivo/Negativo)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"number\",\n                value: hours,\n                onChange: e => setHours(parseFloat(e.target.value)),\n                placeholder: \"Introduce horas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Descripci\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              value: description,\n              onChange: e => setDescription(e.target.value),\n              placeholder: \"A\\xF1ade una descripci\\xF3n (opcional)\",\n              disabled: useShift && selectedShift && !description.includes('Personalizado:')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this), useShift && selectedShift && /*#__PURE__*/_jsxDEV(Form.Text, {\n              className: \"text-muted\",\n              children: \"La descripci\\xF3n se genera autom\\xE1ticamente. Puedes editarla si es necesario.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => {\n            setShowModal(false);\n            resetForm();\n          },\n          disabled: loading,\n          children: \"Cerrar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSave,\n          disabled: loading || !useShift && !dayType || useShift && !selectedShift,\n          children: loading ? 'Guardando...' : 'Guardar Cambios'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 5\n  }, this);\n}\n_s(CalendarPage, \"ofC0DY+0Srqj3KXbdSyHbgJYccI=\");\n_c = CalendarPage;\nexport default CalendarPage;\nvar _c;\n$RefreshReg$(_c, \"CalendarPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Calendar", "Container", "Row", "Col", "<PERSON><PERSON>", "Modal", "Form", "<PERSON><PERSON>", "Badge", "getDayByDate", "createOrUpdateDay", "getAllShifts", "applyShiftToDate", "suggestShiftsForDate", "applyPattern", "getAllPatterns", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CalendarPage", "_s", "date", "setDate", "Date", "showModal", "setShowModal", "selected<PERSON>ay", "setSelectedDay", "dayType", "setDayType", "hours", "setHours", "description", "setDescription", "availableShifts", "setAvailableShifts", "selectedShift", "setSelectedShift", "suggestedShifts", "setSuggestedShifts", "useShift", "setUseShift", "loading", "setLoading", "error", "setError", "selectionStartDate", "setSelectionStartDate", "selectionEndDate", "setSelectionEndDate", "allPatterns", "setAllPatterns", "selectedPatternId", "setSelectedPatternId", "loadAvailableShifts", "loadAllPatterns", "fetchDayDetails", "loadSuggestedShifts", "patterns", "console", "shifts", "formattedDate", "toISOString", "split", "suggestions", "response", "type", "shift", "id", "resetForm", "handleDateClick", "value", "handleShiftChange", "shiftId", "length", "find", "s", "totalHours", "name", "startTime", "endTime", "breakMinutes", "handleSave", "handleApplyPattern", "formattedStartDate", "formattedEndDate", "alert", "children", "className", "md", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "onClickDay", "variant", "Group", "Label", "Control", "toDateString", "readOnly", "Select", "e", "target", "map", "pattern", "onClick", "disabled", "show", "onHide", "Header", "closeButton", "Title", "Body", "Check", "label", "checked", "bg", "style", "cursor", "parseFloat", "placeholder", "as", "rows", "includes", "Text", "Footer", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/CalendarPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Calendar from 'react-calendar';\nimport 'react-calendar/dist/Calendar.css';\nimport { Container, Row, Col, Button, Modal, Form, Alert, Badge } from 'react-bootstrap';\nimport {\n  getDayByDate,\n  createOrUpdateDay,\n  getAllShifts,\n  applyShiftToDate,\n  suggestShiftsForDate,\n  applyPattern,\n  getAllPatterns\n} from '../services/api';\n\nfunction CalendarPage() {\n  const [date, setDate] = useState(new Date());\n  const [showModal, setShowModal] = useState(false);\n  const [selectedDay, setSelectedDay] = useState(null);\n  const [dayType, setDayType] = useState('');\n  const [hours, setHours] = useState(0);\n  const [description, setDescription] = useState('');\n\n  // Estados para turnos predefinidos\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [selectedShift, setSelectedShift] = useState('');\n  const [suggestedShifts, setSuggestedShifts] = useState([]);\n  const [useShift, setUseShift] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Estados para el calendario patrón\n  const [selectionStartDate, setSelectionStartDate] = useState(null);\n  const [selectionEndDate, setSelectionEndDate] = useState(null);\n  const [allPatterns, setAllPatterns] = useState([]);\n  const [selectedPatternId, setSelectedPatternId] = useState('');\n\n  useEffect(() => {\n    // Cargar turnos disponibles al montar el componente\n    loadAvailableShifts();\n    loadAllPatterns(); // Load all patterns\n  }, []);\n\n  useEffect(() => {\n    if (selectedDay) {\n      fetchDayDetails(selectedDay);\n      loadSuggestedShifts(selectedDay);\n    }\n  }, [selectedDay]);\n\n  const loadAllPatterns = async () => {\n    try {\n      const patterns = await getAllPatterns();\n      setAllPatterns(patterns);\n    } catch (error) {\n      console.error('Error loading patterns:', error);\n      setError('Error cargando patrones.');\n    }\n  };\n\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (error) {\n      console.error('Error loading shifts:', error);\n      setError('Error cargando turnos predefinidos');\n    }\n  };\n\n  const loadSuggestedShifts = async (date) => {\n    try {\n      const formattedDate = date.toISOString().split('T')[0];\n      const suggestions = await suggestShiftsForDate(formattedDate);\n      setSuggestedShifts(suggestions.suggestions || []);\n    } catch (error) {\n      console.error('Error loading suggested shifts:', error);\n      setSuggestedShifts([]);\n    }\n  };\n\n  const fetchDayDetails = async (date) => {\n    try {\n      const formattedDate = date.toISOString().split('T')[0];\n      const response = await getDayByDate(formattedDate);\n      if (response) {\n        setDayType(response.type || '');\n        setHours(response.hours || 0);\n        setDescription(response.description || '');\n        // Si el día tiene un turno asignado, mostrar esa información\n        if (response.shift) {\n          setSelectedShift(response.shift.id);\n          setUseShift(true);\n        } else {\n          setSelectedShift('');\n          setUseShift(false);\n        }\n      } else {\n        resetForm();\n      }\n    } catch (error) {\n      console.error('Error fetching day details:', error);\n      resetForm();\n    }\n  };\n\n  const resetForm = () => {\n    setDayType('');\n    setHours(0);\n    setDescription('');\n    setSelectedShift('');\n    setUseShift(false);\n    setError('');\n  };\n\n  const handleDateClick = (value) => {\n    if (!selectionStartDate) {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    } else if (!selectionEndDate) {\n      if (value < selectionStartDate) {\n        setSelectionEndDate(selectionStartDate);\n        setSelectionStartDate(value);\n      } else {\n        setSelectionEndDate(value);\n      }\n    } else {\n      setSelectionStartDate(value);\n      setSelectionEndDate(null);\n    }\n    setSelectedDay(value);\n    setShowModal(true);\n    setError('');\n  };\n\n  const handleShiftChange = (shiftId) => {\n    setSelectedShift(shiftId);\n    if (shiftId && availableShifts.length > 0) {\n      const shift = availableShifts.find(s => s.id === shiftId);\n      if (shift) {\n        setDayType('worked');\n        setHours(shift.totalHours);\n        setDescription(`${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`);\n      }\n    }\n  };\n\n  const handleSave = async () => {\n    if (!selectedDay) return;\n\n    const formattedDate = selectedDay.toISOString().split('T')[0];\n    setLoading(true);\n    setError('');\n\n    try {\n      if (useShift && selectedShift) {\n        // Usar turno predefinido\n        await applyShiftToDate(formattedDate, selectedShift, description);\n      } else {\n        // Usar entrada manual\n        await createOrUpdateDay(formattedDate, dayType, hours, description);\n      }\n      setShowModal(false);\n      resetForm();\n    } catch (error) {\n      console.error('Error saving day:', error);\n      setError('Error guardando los datos. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleApplyPattern = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      const formattedStartDate = selectionStartDate.toISOString().split('T')[0];\n      const formattedEndDate = selectionEndDate.toISOString().split('T')[0];\n      await applyPattern(formattedStartDate, formattedEndDate, selectedPatternId);\n      setSelectionStartDate(null);\n      setSelectionEndDate(null);\n      setSelectedPatternId('');\n      // Optionally, refresh calendar data here if needed\n      alert('Patrón aplicado exitosamente!');\n    } catch (error) {\n      console.error('Error applying pattern:', error);\n      setError('Error aplicando el patrón. Por favor, inténtalo de nuevo.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Container>\n      <Row className=\"justify-content-md-center\">\n        <Col md=\"auto\">\n          <h2>Calendario de Días Trabajados</h2>\n          <Calendar\n            onChange={setDate}\n            value={date}\n            onClickDay={handleDateClick}\n          />\n          \n        </Col>\n      </Row>\n\n      <Row className=\"justify-content-md-center mt-3\">\n        <Col md=\"auto\">\n          <h3>Aplicar Patrón a Rango Seleccionado</h3>\n          {error && (\n            <Alert variant=\"danger\" className=\"mb-3\">\n              {error}\n            </Alert>\n          )}\n          <Form>\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Fechas Seleccionadas:</Form.Label>\n              <Form.Control\n                type=\"text\"\n                value={\n                  selectionStartDate && selectionEndDate\n                    ? `${selectionStartDate.toDateString()} - ${selectionEndDate.toDateString()}`\n                    : selectionStartDate\n                    ? selectionStartDate.toDateString()\n                    : 'Ninguna'\n                }\n                readOnly\n              />\n            </Form.Group>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Seleccionar Patrón</Form.Label>\n              <Form.Select\n                value={selectedPatternId}\n                onChange={(e) => setSelectedPatternId(e.target.value)}\n              >\n                <option value=\"\">Seleccionar patrón...</option>\n                {allPatterns.map((pattern) => (\n                  <option key={pattern.id} value={pattern.id}>\n                    {pattern.name}\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n\n            <Button\n              variant=\"primary\"\n              onClick={handleApplyPattern}\n              disabled={loading || !selectedPatternId || !selectionStartDate || !selectionEndDate}\n            >\n              {loading ? 'Aplicando...' : 'Aplicar Patrón a Rango'}\n            </Button>\n            <Button\n              variant=\"secondary\"\n              onClick={() => {\n                setSelectionStartDate(null);\n                setSelectionEndDate(null);\n                setSelectedPatternId('');\n                setError('');\n              }}\n              className=\"ms-2\"\n            >\n              Limpiar Selección\n            </Button>\n          </Form>\n        </Col>\n      </Row>\n\n      <Modal show={showModal} onHide={() => setShowModal(false)}>\n        <Modal.Header closeButton>\n          <Modal.Title>Detalles del Día: {selectedDay && selectedDay.toDateString()}</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {error && (\n            <Alert variant=\"danger\" className=\"mb-3\">\n              {error}\n            </Alert>\n          )}\n\n          <Form>\n            {/* Selector de método de entrada */}\n            <Form.Group className=\"mb-3\">\n              <Form.Check\n                type=\"radio\"\n                id=\"manual-entry\"\n                name=\"entry-method\"\n                label=\"Entrada manual\"\n                checked={!useShift}\n                onChange={() => setUseShift(false)}\n              />\n              <Form.Check\n                type=\"radio\"\n                id=\"shift-entry\"\n                name=\"entry-method\"\n                label=\"Usar turno predefinido\"\n                checked={useShift}\n                onChange={() => setUseShift(true)}\n              />\n            </Form.Group>\n\n            {/* Sección de turnos predefinidos */}\n            {useShift && (\n              <>\n                {suggestedShifts.length > 0 && (\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Turnos sugeridos para esta fecha:</Form.Label>\n                    <div className=\"d-flex flex-wrap gap-2 mb-2\">\n                      {suggestedShifts.map((shift) => (\n                        <Badge\n                          key={shift.id}\n                          bg={selectedShift === shift.id ? \"primary\" : \"secondary\"}\n                          style={{ cursor: 'pointer' }}\n                          onClick={() => handleShiftChange(shift.id)}\n                        >\n                          {shift.name} ({shift.startTime} - {shift.endTime})\n                        </Badge>\n                      ))}\n                    </div>\n                  </Form.Group>\n                )}\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Seleccionar Turno</Form.Label>\n                  <Form.Select\n                    value={selectedShift}\n                    onChange={(e) => handleShiftChange(e.target.value)}\n                  >\n                    <option value=\"\">Seleccionar turno...</option>\n                    {availableShifts.map((shift) => (\n                      <option key={shift.id} value={shift.id}>\n                        {shift.name} - {shift.startTime} a {shift.endTime} ({shift.totalHours}h)\n                      </option>\n                    ))}\n                  </Form.Select>\n                </Form.Group>\n              </>\n            )}\n\n            {/* Sección de entrada manual */}\n            {!useShift && (\n              <>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Tipo de Día</Form.Label>\n                  <Form.Select value={dayType} onChange={(e) => setDayType(e.target.value)}>\n                    <option value=\"\">Seleccionar</option>\n                    <option value=\"worked\">Trabajado</option>\n                    <option value=\"holiday\">Vacaciones</option>\n                    <option value=\"permit\">Permiso</option>\n                    <option value=\"negative\">Cómputo Negativo</option>\n                  </Form.Select>\n                </Form.Group>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Horas (Cómputo Positivo/Negativo)</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    value={hours}\n                    onChange={(e) => setHours(parseFloat(e.target.value))}\n                    placeholder=\"Introduce horas\"\n                  />\n                </Form.Group>\n              </>\n            )}\n\n            {/* Descripción (común para ambos métodos) */}\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Descripción</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={3}\n                value={description}\n                onChange={(e) => setDescription(e.target.value)}\n                placeholder=\"Añade una descripción (opcional)\"\n                disabled={useShift && selectedShift && !description.includes('Personalizado:')}\n              />\n              {useShift && selectedShift && (\n                <Form.Text className=\"text-muted\">\n                  La descripción se genera automáticamente. Puedes editarla si es necesario.\n                </Form.Text>\n              )}\n            </Form.Group>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button\n            variant=\"secondary\"\n            onClick={() => {\n              setShowModal(false);\n              resetForm();\n            }}\n            disabled={loading}\n          >\n            Cerrar\n          </Button>\n          <Button\n            variant=\"primary\"\n            onClick={handleSave}\n            disabled={loading || (!useShift && !dayType) || (useShift && !selectedShift)}\n          >\n            {loading ? 'Guardando...' : 'Guardar Cambios'}\n          </Button>\n        </Modal.Footer>\n      </Modal>\n\n      \n    </Container>\n  );\n}\n\nexport default CalendarPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAO,kCAAkC;AACzC,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AACxF,SACEC,YAAY,EACZC,iBAAiB,EACjBC,YAAY,EACZC,gBAAgB,EAChBC,oBAAoB,EACpBC,YAAY,EACZC,cAAc,QACT,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGzB,QAAQ,CAAC,IAAI0B,IAAI,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiC,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAM,CAACiD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACmD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACqD,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAE9DC,SAAS,CAAC,MAAM;IACd;IACAwD,mBAAmB,CAAC,CAAC;IACrBC,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAENzD,SAAS,CAAC,MAAM;IACd,IAAI4B,WAAW,EAAE;MACf8B,eAAe,CAAC9B,WAAW,CAAC;MAC5B+B,mBAAmB,CAAC/B,WAAW,CAAC;IAClC;EACF,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjB,MAAM6B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAM5C,cAAc,CAAC,CAAC;MACvCqC,cAAc,CAACO,QAAQ,CAAC;IAC1B,CAAC,CAAC,OAAOd,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,0BAA0B,CAAC;IACtC;EACF,CAAC;EAED,MAAMS,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMM,MAAM,GAAG,MAAMlD,YAAY,CAAC,CAAC;MACnCyB,kBAAkB,CAACyB,MAAM,CAAC;IAC5B,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,oCAAoC,CAAC;IAChD;EACF,CAAC;EAED,MAAMY,mBAAmB,GAAG,MAAOpC,IAAI,IAAK;IAC1C,IAAI;MACF,MAAMwC,aAAa,GAAGxC,IAAI,CAACyC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACtD,MAAMC,WAAW,GAAG,MAAMpD,oBAAoB,CAACiD,aAAa,CAAC;MAC7DtB,kBAAkB,CAACyB,WAAW,CAACA,WAAW,IAAI,EAAE,CAAC;IACnD,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDL,kBAAkB,CAAC,EAAE,CAAC;IACxB;EACF,CAAC;EAED,MAAMiB,eAAe,GAAG,MAAOnC,IAAI,IAAK;IACtC,IAAI;MACF,MAAMwC,aAAa,GAAGxC,IAAI,CAACyC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACtD,MAAME,QAAQ,GAAG,MAAMzD,YAAY,CAACqD,aAAa,CAAC;MAClD,IAAII,QAAQ,EAAE;QACZpC,UAAU,CAACoC,QAAQ,CAACC,IAAI,IAAI,EAAE,CAAC;QAC/BnC,QAAQ,CAACkC,QAAQ,CAACnC,KAAK,IAAI,CAAC,CAAC;QAC7BG,cAAc,CAACgC,QAAQ,CAACjC,WAAW,IAAI,EAAE,CAAC;QAC1C;QACA,IAAIiC,QAAQ,CAACE,KAAK,EAAE;UAClB9B,gBAAgB,CAAC4B,QAAQ,CAACE,KAAK,CAACC,EAAE,CAAC;UACnC3B,WAAW,CAAC,IAAI,CAAC;QACnB,CAAC,MAAM;UACLJ,gBAAgB,CAAC,EAAE,CAAC;UACpBI,WAAW,CAAC,KAAK,CAAC;QACpB;MACF,CAAC,MAAM;QACL4B,SAAS,CAAC,CAAC;MACb;IACF,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDyB,SAAS,CAAC,CAAC;IACb;EACF,CAAC;EAED,MAAMA,SAAS,GAAGA,CAAA,KAAM;IACtBxC,UAAU,CAAC,EAAE,CAAC;IACdE,QAAQ,CAAC,CAAC,CAAC;IACXE,cAAc,CAAC,EAAE,CAAC;IAClBI,gBAAgB,CAAC,EAAE,CAAC;IACpBI,WAAW,CAAC,KAAK,CAAC;IAClBI,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMyB,eAAe,GAAIC,KAAK,IAAK;IACjC,IAAI,CAACzB,kBAAkB,EAAE;MACvBC,qBAAqB,CAACwB,KAAK,CAAC;MAC5BtB,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM,IAAI,CAACD,gBAAgB,EAAE;MAC5B,IAAIuB,KAAK,GAAGzB,kBAAkB,EAAE;QAC9BG,mBAAmB,CAACH,kBAAkB,CAAC;QACvCC,qBAAqB,CAACwB,KAAK,CAAC;MAC9B,CAAC,MAAM;QACLtB,mBAAmB,CAACsB,KAAK,CAAC;MAC5B;IACF,CAAC,MAAM;MACLxB,qBAAqB,CAACwB,KAAK,CAAC;MAC5BtB,mBAAmB,CAAC,IAAI,CAAC;IAC3B;IACAtB,cAAc,CAAC4C,KAAK,CAAC;IACrB9C,YAAY,CAAC,IAAI,CAAC;IAClBoB,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAM2B,iBAAiB,GAAIC,OAAO,IAAK;IACrCpC,gBAAgB,CAACoC,OAAO,CAAC;IACzB,IAAIA,OAAO,IAAIvC,eAAe,CAACwC,MAAM,GAAG,CAAC,EAAE;MACzC,MAAMP,KAAK,GAAGjC,eAAe,CAACyC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACR,EAAE,KAAKK,OAAO,CAAC;MACzD,IAAIN,KAAK,EAAE;QACTtC,UAAU,CAAC,QAAQ,CAAC;QACpBE,QAAQ,CAACoC,KAAK,CAACU,UAAU,CAAC;QAC1B5C,cAAc,CAAC,GAAGkC,KAAK,CAACW,IAAI,KAAKX,KAAK,CAACY,SAAS,MAAMZ,KAAK,CAACa,OAAO,KAAKb,KAAK,CAACc,YAAY,eAAe,CAAC;MAC5G;IACF;EACF,CAAC;EAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACxD,WAAW,EAAE;IAElB,MAAMmC,aAAa,GAAGnC,WAAW,CAACoC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7DpB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,IAAIL,QAAQ,IAAIJ,aAAa,EAAE;QAC7B;QACA,MAAMzB,gBAAgB,CAACkD,aAAa,EAAEzB,aAAa,EAAEJ,WAAW,CAAC;MACnE,CAAC,MAAM;QACL;QACA,MAAMvB,iBAAiB,CAACoD,aAAa,EAAEjC,OAAO,EAAEE,KAAK,EAAEE,WAAW,CAAC;MACrE;MACAP,YAAY,CAAC,KAAK,CAAC;MACnB4C,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCC,QAAQ,CAAC,2DAA2D,CAAC;IACvE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrCxC,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,MAAMuC,kBAAkB,GAAGtC,kBAAkB,CAACgB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACzE,MAAMsB,gBAAgB,GAAGrC,gBAAgB,CAACc,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACrE,MAAMlD,YAAY,CAACuE,kBAAkB,EAAEC,gBAAgB,EAAEjC,iBAAiB,CAAC;MAC3EL,qBAAqB,CAAC,IAAI,CAAC;MAC3BE,mBAAmB,CAAC,IAAI,CAAC;MACzBI,oBAAoB,CAAC,EAAE,CAAC;MACxB;MACAiC,KAAK,CAAC,+BAA+B,CAAC;IACxC,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,2DAA2D,CAAC;IACvE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACE3B,OAAA,CAAChB,SAAS;IAAAuF,QAAA,gBACRvE,OAAA,CAACf,GAAG;MAACuF,SAAS,EAAC,2BAA2B;MAAAD,QAAA,eACxCvE,OAAA,CAACd,GAAG;QAACuF,EAAE,EAAC,MAAM;QAAAF,QAAA,gBACZvE,OAAA;UAAAuE,QAAA,EAAI;QAA6B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtC7E,OAAA,CAACjB,QAAQ;UACP+F,QAAQ,EAAExE,OAAQ;UAClBiD,KAAK,EAAElD,IAAK;UACZ0E,UAAU,EAAEzB;QAAgB;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7E,OAAA,CAACf,GAAG;MAACuF,SAAS,EAAC,gCAAgC;MAAAD,QAAA,eAC7CvE,OAAA,CAACd,GAAG;QAACuF,EAAE,EAAC,MAAM;QAAAF,QAAA,gBACZvE,OAAA;UAAAuE,QAAA,EAAI;QAAmC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC3CjD,KAAK,iBACJ5B,OAAA,CAACV,KAAK;UAAC0F,OAAO,EAAC,QAAQ;UAACR,SAAS,EAAC,MAAM;UAAAD,QAAA,EACrC3C;QAAK;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eACD7E,OAAA,CAACX,IAAI;UAAAkF,QAAA,gBACHvE,OAAA,CAACX,IAAI,CAAC4F,KAAK;YAACT,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAAC6F,KAAK;cAAAX,QAAA,EAAC;YAAqB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9C7E,OAAA,CAACX,IAAI,CAAC8F,OAAO;cACXjC,IAAI,EAAC,MAAM;cACXK,KAAK,EACHzB,kBAAkB,IAAIE,gBAAgB,GAClC,GAAGF,kBAAkB,CAACsD,YAAY,CAAC,CAAC,MAAMpD,gBAAgB,CAACoD,YAAY,CAAC,CAAC,EAAE,GAC3EtD,kBAAkB,GAClBA,kBAAkB,CAACsD,YAAY,CAAC,CAAC,GACjC,SACL;cACDC,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEb7E,OAAA,CAACX,IAAI,CAAC4F,KAAK;YAACT,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAAC6F,KAAK;cAAAX,QAAA,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3C7E,OAAA,CAACX,IAAI,CAACiG,MAAM;cACV/B,KAAK,EAAEnB,iBAAkB;cACzB0C,QAAQ,EAAGS,CAAC,IAAKlD,oBAAoB,CAACkD,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE;cAAAgB,QAAA,gBAEtDvE,OAAA;gBAAQuD,KAAK,EAAC,EAAE;gBAAAgB,QAAA,EAAC;cAAqB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9C3C,WAAW,CAACuD,GAAG,CAAEC,OAAO,iBACvB1F,OAAA;gBAAyBuD,KAAK,EAAEmC,OAAO,CAACtC,EAAG;gBAAAmB,QAAA,EACxCmB,OAAO,CAAC5B;cAAI,GADF4B,OAAO,CAACtC,EAAE;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEb7E,OAAA,CAACb,MAAM;YACL6F,OAAO,EAAC,SAAS;YACjBW,OAAO,EAAExB,kBAAmB;YAC5ByB,QAAQ,EAAElE,OAAO,IAAI,CAACU,iBAAiB,IAAI,CAACN,kBAAkB,IAAI,CAACE,gBAAiB;YAAAuC,QAAA,EAEnF7C,OAAO,GAAG,cAAc,GAAG;UAAwB;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACT7E,OAAA,CAACb,MAAM;YACL6F,OAAO,EAAC,WAAW;YACnBW,OAAO,EAAEA,CAAA,KAAM;cACb5D,qBAAqB,CAAC,IAAI,CAAC;cAC3BE,mBAAmB,CAAC,IAAI,CAAC;cACzBI,oBAAoB,CAAC,EAAE,CAAC;cACxBR,QAAQ,CAAC,EAAE,CAAC;YACd,CAAE;YACF2C,SAAS,EAAC,MAAM;YAAAD,QAAA,EACjB;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7E,OAAA,CAACZ,KAAK;MAACyG,IAAI,EAAErF,SAAU;MAACsF,MAAM,EAAEA,CAAA,KAAMrF,YAAY,CAAC,KAAK,CAAE;MAAA8D,QAAA,gBACxDvE,OAAA,CAACZ,KAAK,CAAC2G,MAAM;QAACC,WAAW;QAAAzB,QAAA,eACvBvE,OAAA,CAACZ,KAAK,CAAC6G,KAAK;UAAA1B,QAAA,GAAC,uBAAkB,EAAC7D,WAAW,IAAIA,WAAW,CAAC0E,YAAY,CAAC,CAAC;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,eACf7E,OAAA,CAACZ,KAAK,CAAC8G,IAAI;QAAA3B,QAAA,GACR3C,KAAK,iBACJ5B,OAAA,CAACV,KAAK;UAAC0F,OAAO,EAAC,QAAQ;UAACR,SAAS,EAAC,MAAM;UAAAD,QAAA,EACrC3C;QAAK;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eAED7E,OAAA,CAACX,IAAI;UAAAkF,QAAA,gBAEHvE,OAAA,CAACX,IAAI,CAAC4F,KAAK;YAACT,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAAC8G,KAAK;cACTjD,IAAI,EAAC,OAAO;cACZE,EAAE,EAAC,cAAc;cACjBU,IAAI,EAAC,cAAc;cACnBsC,KAAK,EAAC,gBAAgB;cACtBC,OAAO,EAAE,CAAC7E,QAAS;cACnBsD,QAAQ,EAAEA,CAAA,KAAMrD,WAAW,CAAC,KAAK;YAAE;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACF7E,OAAA,CAACX,IAAI,CAAC8G,KAAK;cACTjD,IAAI,EAAC,OAAO;cACZE,EAAE,EAAC,aAAa;cAChBU,IAAI,EAAC,cAAc;cACnBsC,KAAK,EAAC,wBAAwB;cAC9BC,OAAO,EAAE7E,QAAS;cAClBsD,QAAQ,EAAEA,CAAA,KAAMrD,WAAW,CAAC,IAAI;YAAE;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,EAGZrD,QAAQ,iBACPxB,OAAA,CAAAE,SAAA;YAAAqE,QAAA,GACGjD,eAAe,CAACoC,MAAM,GAAG,CAAC,iBACzB1D,OAAA,CAACX,IAAI,CAAC4F,KAAK;cAACT,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAAC6F,KAAK;gBAAAX,QAAA,EAAC;cAAiC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1D7E,OAAA;gBAAKwE,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,EACzCjD,eAAe,CAACmE,GAAG,CAAEtC,KAAK,iBACzBnD,OAAA,CAACT,KAAK;kBAEJ+G,EAAE,EAAElF,aAAa,KAAK+B,KAAK,CAACC,EAAE,GAAG,SAAS,GAAG,WAAY;kBACzDmD,KAAK,EAAE;oBAAEC,MAAM,EAAE;kBAAU,CAAE;kBAC7Bb,OAAO,EAAEA,CAAA,KAAMnC,iBAAiB,CAACL,KAAK,CAACC,EAAE,CAAE;kBAAAmB,QAAA,GAE1CpB,KAAK,CAACW,IAAI,EAAC,IAAE,EAACX,KAAK,CAACY,SAAS,EAAC,KAAG,EAACZ,KAAK,CAACa,OAAO,EAAC,GACnD;gBAAA,GANOb,KAAK,CAACC,EAAE;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMR,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAED7E,OAAA,CAACX,IAAI,CAAC4F,KAAK;cAACT,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAAC6F,KAAK;gBAAAX,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1C7E,OAAA,CAACX,IAAI,CAACiG,MAAM;gBACV/B,KAAK,EAAEnC,aAAc;gBACrB0D,QAAQ,EAAGS,CAAC,IAAK/B,iBAAiB,CAAC+B,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE;gBAAAgB,QAAA,gBAEnDvE,OAAA;kBAAQuD,KAAK,EAAC,EAAE;kBAAAgB,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC7C3D,eAAe,CAACuE,GAAG,CAAEtC,KAAK,iBACzBnD,OAAA;kBAAuBuD,KAAK,EAAEJ,KAAK,CAACC,EAAG;kBAAAmB,QAAA,GACpCpB,KAAK,CAACW,IAAI,EAAC,KAAG,EAACX,KAAK,CAACY,SAAS,EAAC,KAAG,EAACZ,KAAK,CAACa,OAAO,EAAC,IAAE,EAACb,KAAK,CAACU,UAAU,EAAC,IACxE;gBAAA,GAFaV,KAAK,CAACC,EAAE;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,eACb,CACH,EAGA,CAACrD,QAAQ,iBACRxB,OAAA,CAAAE,SAAA;YAAAqE,QAAA,gBACEvE,OAAA,CAACX,IAAI,CAAC4F,KAAK;cAACT,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAAC6F,KAAK;gBAAAX,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpC7E,OAAA,CAACX,IAAI,CAACiG,MAAM;gBAAC/B,KAAK,EAAE3C,OAAQ;gBAACkE,QAAQ,EAAGS,CAAC,IAAK1E,UAAU,CAAC0E,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE;gBAAAgB,QAAA,gBACvEvE,OAAA;kBAAQuD,KAAK,EAAC,EAAE;kBAAAgB,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrC7E,OAAA;kBAAQuD,KAAK,EAAC,QAAQ;kBAAAgB,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzC7E,OAAA;kBAAQuD,KAAK,EAAC,SAAS;kBAAAgB,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3C7E,OAAA;kBAAQuD,KAAK,EAAC,QAAQ;kBAAAgB,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvC7E,OAAA;kBAAQuD,KAAK,EAAC,UAAU;kBAAAgB,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACb7E,OAAA,CAACX,IAAI,CAAC4F,KAAK;cAACT,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAAC6F,KAAK;gBAAAX,QAAA,EAAC;cAAiC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1D7E,OAAA,CAACX,IAAI,CAAC8F,OAAO;gBACXjC,IAAI,EAAC,QAAQ;gBACbK,KAAK,EAAEzC,KAAM;gBACbgE,QAAQ,EAAGS,CAAC,IAAKxE,QAAQ,CAAC0F,UAAU,CAAClB,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAC,CAAE;gBACtDmD,WAAW,EAAC;cAAiB;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA,eACb,CACH,eAGD7E,OAAA,CAACX,IAAI,CAAC4F,KAAK;YAACT,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BvE,OAAA,CAACX,IAAI,CAAC6F,KAAK;cAAAX,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpC7E,OAAA,CAACX,IAAI,CAAC8F,OAAO;cACXwB,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRrD,KAAK,EAAEvC,WAAY;cACnB8D,QAAQ,EAAGS,CAAC,IAAKtE,cAAc,CAACsE,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE;cAChDmD,WAAW,EAAC,wCAAkC;cAC9Cd,QAAQ,EAAEpE,QAAQ,IAAIJ,aAAa,IAAI,CAACJ,WAAW,CAAC6F,QAAQ,CAAC,gBAAgB;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,EACDrD,QAAQ,IAAIJ,aAAa,iBACxBpB,OAAA,CAACX,IAAI,CAACyH,IAAI;cAACtC,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAElC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CACZ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACb7E,OAAA,CAACZ,KAAK,CAAC2H,MAAM;QAAAxC,QAAA,gBACXvE,OAAA,CAACb,MAAM;UACL6F,OAAO,EAAC,WAAW;UACnBW,OAAO,EAAEA,CAAA,KAAM;YACblF,YAAY,CAAC,KAAK,CAAC;YACnB4C,SAAS,CAAC,CAAC;UACb,CAAE;UACFuC,QAAQ,EAAElE,OAAQ;UAAA6C,QAAA,EACnB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7E,OAAA,CAACb,MAAM;UACL6F,OAAO,EAAC,SAAS;UACjBW,OAAO,EAAEzB,UAAW;UACpB0B,QAAQ,EAAElE,OAAO,IAAK,CAACF,QAAQ,IAAI,CAACZ,OAAQ,IAAKY,QAAQ,IAAI,CAACJ,aAAe;UAAAmD,QAAA,EAE5E7C,OAAO,GAAG,cAAc,GAAG;QAAiB;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGC,CAAC;AAEhB;AAACzE,EAAA,CAvYQD,YAAY;AAAA6G,EAAA,GAAZ7G,YAAY;AAyYrB,eAAeA,YAAY;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}