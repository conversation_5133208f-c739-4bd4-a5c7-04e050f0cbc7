[{"D:\\Proyectos Python\\Horario\\client\\src\\index.js": "1", "D:\\Proyectos Python\\Horario\\client\\src\\App.js": "2", "D:\\Proyectos Python\\Horario\\client\\src\\reportWebVitals.js": "3", "D:\\Proyectos Python\\Horario\\client\\src\\pages\\SettingsPage.js": "4", "D:\\Proyectos Python\\Horario\\client\\src\\pages\\CalendarPage.js": "5", "D:\\Proyectos Python\\Horario\\client\\src\\pages\\ReportsPage.js": "6", "D:\\Proyectos Python\\Horario\\client\\src\\components\\NavbarComponent.js": "7", "D:\\Proyectos Python\\Horario\\client\\src\\services\\api.js": "8"}, {"size": 512, "mtime": 1751325868914, "results": "9", "hashOfConfig": "10"}, {"size": 732, "mtime": 1751325868913, "results": "11", "hashOfConfig": "10"}, {"size": 362, "mtime": 1751325821295, "results": "12", "hashOfConfig": "10"}, {"size": 18252, "mtime": 1751360560488, "results": "13", "hashOfConfig": "10"}, {"size": 14090, "mtime": 1751361939406, "results": "14", "hashOfConfig": "10"}, {"size": 5910, "mtime": 1751367804647, "results": "15", "hashOfConfig": "10"}, {"size": 737, "mtime": 1751326777823, "results": "16", "hashOfConfig": "10"}, {"size": 5700, "mtime": 1751381388910, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "18a8vct", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Proyectos Python\\Horario\\client\\src\\index.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\App.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\reportWebVitals.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\pages\\SettingsPage.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\pages\\CalendarPage.js", ["42"], [], "D:\\Proyectos Python\\Horario\\client\\src\\pages\\ReportsPage.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\components\\NavbarComponent.js", [], [], "D:\\Proyectos Python\\Horario\\client\\src\\services\\api.js", [], [], {"ruleId": "43", "severity": 1, "message": "44", "line": 48, "column": 6, "nodeType": "45", "endLine": 48, "endColumn": 19, "suggestions": "46"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDayDetails'. Either include it or remove the dependency array.", "ArrayExpression", ["47"], {"desc": "48", "fix": "49"}, "Update the dependencies array to be: [fetchDayDetails, selectedDay]", {"range": "50", "text": "51"}, [1663, 1676], "[fetchDayDetails, selectedDay]"]