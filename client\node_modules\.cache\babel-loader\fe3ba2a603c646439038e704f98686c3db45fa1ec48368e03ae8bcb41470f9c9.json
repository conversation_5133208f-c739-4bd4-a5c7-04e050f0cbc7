{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\pages\\\\SettingsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Form, Alert, ListGroup, Modal } from 'react-bootstrap';\nimport { getAllPatterns, createPattern, updatePattern, deletePattern, getAllShifts, copyPattern } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SettingsPage() {\n  _s();\n  const [patterns, setPatterns] = useState([]);\n  const [currentPattern, setCurrentPattern] = useState(null);\n  const [showPatternForm, setShowPatternForm] = useState(false);\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showCopyModal, setShowCopyModal] = useState(false);\n  const [patternToCopy, setPatternToCopy] = useState(null);\n  const [newPatternName, setNewPatternName] = useState('');\n  const [newPatternYear, setNewPatternYear] = useState(new Date().getFullYear() + 1);\n  useEffect(() => {\n    loadPatterns();\n    loadAvailableShifts();\n  }, []);\n  const loadPatterns = async () => {\n    setLoading(true);\n    try {\n      const data = await getAllPatterns();\n      setPatterns(data);\n    } catch (err) {\n      setError('Error cargando patrones.');\n      console.error('Error loading patterns:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (err) {\n      console.error('Error loading shifts:', err);\n    }\n  };\n  const handleAddPattern = () => {\n    setCurrentPattern({\n      name: '',\n      basePattern: Array.from({\n        length: 7\n      }, () => ({\n        type: '',\n        hours: 0,\n        description: '',\n        shiftId: null\n      })),\n      overrides: []\n    });\n    setShowPatternForm(true);\n  };\n  const handleEditPattern = pattern => {\n    setCurrentPattern({\n      ...pattern\n    });\n    setShowPatternForm(true);\n  };\n  const handleDeletePattern = async id => {\n    if (window.confirm('¿Estás seguro de que quieres eliminar este patrón?')) {\n      setLoading(true);\n      try {\n        await deletePattern(id);\n        await loadPatterns();\n      } catch (err) {\n        setError('Error eliminando patrón.');\n        console.error('Error deleting pattern:', err);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n  const handleSavePattern = async () => {\n    if (!currentPattern || !currentPattern.name || !currentPattern.basePattern) {\n      setError('El nombre del patrón y el patrón base son requeridos.');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      if (currentPattern.id) {\n        await updatePattern(currentPattern.id, currentPattern.name, currentPattern.basePattern, currentPattern.overrides);\n      } else {\n        await createPattern(currentPattern.name, currentPattern.basePattern, currentPattern.overrides);\n      }\n      setShowPatternForm(false);\n      setCurrentPattern(null);\n      await loadPatterns();\n    } catch (err) {\n      setError('Error guardando patrón.');\n      console.error('Error saving pattern:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCancelEdit = () => {\n    setShowPatternForm(false);\n    setCurrentPattern(null);\n    setError('');\n  };\n  const handleShowCopyModal = pattern => {\n    setPatternToCopy(pattern);\n    setNewPatternName(`${pattern.name} - ${new Date().getFullYear() + 1}`);\n    setNewPatternYear(new Date().getFullYear() + 1);\n    setShowCopyModal(true);\n  };\n  const handleCopyPattern = async () => {\n    if (!patternToCopy || !newPatternName || !newPatternYear) {\n      setError('Nombre y año para la copia son requeridos.');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      await copyPattern(patternToCopy.id, newPatternName, newPatternYear);\n      setShowCopyModal(false);\n      setPatternToCopy(null);\n      setNewPatternName('');\n      setNewPatternYear(new Date().getFullYear() + 1);\n      await loadPatterns();\n      alert('Patrón copiado exitosamente!');\n    } catch (err) {\n      setError('Error copiando patrón.');\n      console.error('Error copying pattern:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleBasePatternDayChange = (index, field, value) => {\n    const newBasePattern = [...currentPattern.basePattern];\n    newBasePattern[index][field] = value;\n    if (field === 'shiftId' && value) {\n      const shift = availableShifts.find(s => s.id === value);\n      if (shift) {\n        newBasePattern[index].type = 'worked';\n        newBasePattern[index].hours = shift.totalHours;\n        newBasePattern[index].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;\n      }\n    } else if (field === 'shiftId' && !value) {\n      newBasePattern[index].description = '';\n    }\n    setCurrentPattern({\n      ...currentPattern,\n      basePattern: newBasePattern\n    });\n  };\n  const handleOverrideChange = (overrideIndex, field, value) => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex][field] = value;\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: newOverrides\n    });\n  };\n  const handleOverridePatternDayChange = (overrideIndex, dayIndex, field, value) => {\n    const newOverrides = [...currentPattern.overrides];\n    const newOverridePattern = [...newOverrides[overrideIndex].overridePattern];\n    newOverridePattern[dayIndex][field] = value;\n    if (field === 'shiftId' && value) {\n      const shift = availableShifts.find(s => s.id === value);\n      if (shift) {\n        newOverridePattern[dayIndex].type = 'worked';\n        newOverridePattern[dayIndex].hours = shift.totalHours;\n        newOverridePattern[dayIndex].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;\n      }\n    } else if (field === 'shiftId' && !value) {\n      newOverridePattern[dayIndex].description = '';\n    }\n    newOverrides[overrideIndex].overridePattern = newOverridePattern;\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: newOverrides\n    });\n  };\n  const handleAddOverride = () => {\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: [...currentPattern.overrides, {\n        startDate: '',\n        endDate: '',\n        overridePattern: [{\n          type: '',\n          hours: 0,\n          description: '',\n          shiftId: null\n        }]\n      }]\n    });\n  };\n  const handleRemoveOverride = overrideIndex => {\n    const newOverrides = currentPattern.overrides.filter((_, i) => i !== overrideIndex);\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: newOverrides\n    });\n  };\n  const handleAddOverridePatternDay = overrideIndex => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex].overridePattern.push({\n      type: '',\n      hours: 0,\n      description: '',\n      shiftId: null\n    });\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: newOverrides\n    });\n  };\n  const handleRemoveOverridePatternDay = (overrideIndex, dayIndex) => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex].overridePattern = newOverrides[overrideIndex].overridePattern.filter((_, i) => i !== dayIndex);\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: newOverrides\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"my-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Configuraci\\xF3n de Patrones de Calendario\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleAddPattern,\n          className: \"mb-3\",\n          children: \"Crear Nuevo Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Cargando patrones...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this) : patterns.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No hay patrones definidos.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ListGroup, {\n          children: patterns.map(pattern => /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n            className: \"d-flex justify-content-between align-items-center\",\n            children: [pattern.name, /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"info\",\n                size: \"sm\",\n                className: \"me-2\",\n                onClick: () => handleEditPattern(pattern),\n                children: \"Editar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"secondary\",\n                size: \"sm\",\n                className: \"me-2\",\n                onClick: () => handleShowCopyModal(pattern),\n                children: \"Copiar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"danger\",\n                size: \"sm\",\n                onClick: () => handleDeletePattern(pattern.id),\n                children: \"Eliminar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 19\n            }, this)]\n          }, pattern.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPatternForm,\n      onHide: handleCancelEdit,\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: currentPattern && currentPattern.id ? 'Editar Patrón' : 'Crear Nuevo Patrón'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: currentPattern && /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Nombre del Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              value: currentPattern.name,\n              onChange: e => setCurrentPattern({\n                ...currentPattern,\n                name: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Patr\\xF3n Base (Semanal):\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this), currentPattern.basePattern.map((day, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border p-3 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: [\"D\\xEDa \", ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'][index]]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Tipo de D\\xEDa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: day.type,\n                onChange: e => handleBasePatternDayChange(index, 'type', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Seleccionar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"worked\",\n                  children: \"Trabajado\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"holiday\",\n                  children: \"Vacaciones\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"permit\",\n                  children: \"Permiso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"negative\",\n                  children: \"C\\xF3mputo Negativo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Horas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"number\",\n                value: day.hours,\n                onChange: e => handleBasePatternDayChange(index, 'hours', parseFloat(e.target.value))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Turno Predefinido\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: day.shiftId || '',\n                onChange: e => handleBasePatternDayChange(index, 'shiftId', e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ninguno\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 23\n                }, this), availableShifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: shift.id,\n                  children: [shift.name, \" - \", shift.startTime, \" a \", shift.endTime, \" (\", shift.totalHours, \"h)\"]\n                }, shift.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Descripci\\xF3n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                as: \"textarea\",\n                rows: 1,\n                value: day.description,\n                onChange: e => handleBasePatternDayChange(index, 'description', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 17\n          }, this)), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mt-4\",\n            children: \"Anulaciones (Overrides):\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this), currentPattern.overrides.map((override, overrideIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border p-3 mb-2 bg-light\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: [\"Anulaci\\xF3n \", overrideIndex + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  controlId: `overrideStartDate-${overrideIndex}`,\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Fecha de Inicio\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: override.startDate,\n                    onChange: e => handleOverrideChange(overrideIndex, 'startDate', e.target.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  controlId: `overrideEndDate-${overrideIndex}`,\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Fecha de Fin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: override.endDate,\n                    onChange: e => handleOverrideChange(overrideIndex, 'endDate', e.target.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"D\\xEDas de la Anulaci\\xF3n:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 19\n            }, this), override.overridePattern.map((day, dayIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border p-3 mb-2 ms-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: [\"D\\xEDa \", dayIndex + 1]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Tipo de D\\xEDa\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: day.type,\n                  onChange: e => handleOverridePatternDayChange(overrideIndex, dayIndex, 'type', e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Seleccionar\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"worked\",\n                    children: \"Trabajado\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"holiday\",\n                    children: \"Vacaciones\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"permit\",\n                    children: \"Permiso\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"negative\",\n                    children: \"C\\xF3mputo Negativo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Horas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  value: day.hours,\n                  onChange: e => handleOverridePatternDayChange(overrideIndex, dayIndex, 'hours', parseFloat(e.target.value))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Turno Predefinido\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: day.shiftId || '',\n                  onChange: e => handleOverridePatternDayChange(overrideIndex, dayIndex, 'shiftId', e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Ninguno\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 27\n                  }, this), availableShifts.map(shift => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: shift.id,\n                    children: [shift.name, \" - \", shift.startTime, \" a \", shift.endTime, \" (\", shift.totalHours, \"h)\"]\n                  }, shift.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 29\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Descripci\\xF3n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  as: \"textarea\",\n                  rows: 1,\n                  value: day.description,\n                  onChange: e => handleOverridePatternDayChange(overrideIndex, dayIndex, 'description', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"danger\",\n                size: \"sm\",\n                onClick: () => handleRemoveOverridePatternDay(overrideIndex, dayIndex),\n                children: \"Eliminar D\\xEDa de Anulaci\\xF3n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 23\n              }, this)]\n            }, dayIndex, true, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 21\n            }, this)), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"secondary\",\n              onClick: () => handleAddOverridePatternDay(overrideIndex),\n              className: \"mt-3\",\n              children: \"A\\xF1adir D\\xEDa a Anulaci\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              onClick: () => handleRemoveOverride(overrideIndex),\n              className: \"mt-3 ms-2\",\n              children: \"Eliminar Anulaci\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 19\n            }, this)]\n          }, overrideIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 17\n          }, this)), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"info\",\n            onClick: handleAddOverride,\n            className: \"mt-3\",\n            children: \"A\\xF1adir Anulaci\\xF3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: handleCancelEdit,\n          children: \"Cancelar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSavePattern,\n          disabled: loading,\n          children: \"Guardar Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showCopyModal,\n      onHide: () => setShowCopyModal(false),\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Copiar Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 21\n        }, this), patternToCopy && /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Nombre del Patr\\xF3n Original\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              value: patternToCopy.name,\n              disabled: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Nuevo Nombre del Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              value: newPatternName,\n              onChange: e => setNewPatternName(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"A\\xF1o para el Nuevo Patr\\xF3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"number\",\n              value: newPatternYear,\n              onChange: e => setNewPatternYear(parseInt(e.target.value, 10))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowCopyModal(false),\n          children: \"Cancelar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleCopyPattern,\n          disabled: loading,\n          children: \"Copiar Patr\\xF3n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 203,\n    columnNumber: 5\n  }, this);\n}\n_s(SettingsPage, \"lej1LqdI/m2pH0PDXuiFxsRVlFM=\");\n_c = SettingsPage;\nexport default SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "<PERSON><PERSON>", "Form", "<PERSON><PERSON>", "ListGroup", "Modal", "getAllPatterns", "createPattern", "updatePattern", "deletePattern", "getAllShifts", "copyPattern", "jsxDEV", "_jsxDEV", "SettingsPage", "_s", "patterns", "setPatterns", "currentPattern", "setCurrentPattern", "showPatternForm", "setShowPatternForm", "availableShifts", "setAvailableShifts", "loading", "setLoading", "error", "setError", "showCopyModal", "setShowCopyModal", "patternToCopy", "setPatternToCopy", "newPatternName", "setNewPatternName", "newPatternYear", "setNewPatternYear", "Date", "getFullYear", "loadPatterns", "loadAvailableShifts", "data", "err", "console", "shifts", "handleAddPattern", "name", "basePattern", "Array", "from", "length", "type", "hours", "description", "shiftId", "overrides", "handleEditPattern", "pattern", "handleDeletePattern", "id", "window", "confirm", "handleSavePattern", "handleCancelEdit", "handleShowCopyModal", "handleCopyPattern", "alert", "handleBasePatternDayChange", "index", "field", "value", "newBasePattern", "shift", "find", "s", "totalHours", "startTime", "endTime", "breakMinutes", "handleOverrideChange", "overrideIndex", "newOverrides", "handleOverridePatternDayChange", "dayIndex", "newOverridePattern", "overridePattern", "handleAddOverride", "startDate", "endDate", "handleRemoveOverride", "filter", "_", "i", "handleAddOverridePatternDay", "push", "handleRemoveOverridePatternDay", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "map", "<PERSON><PERSON>", "size", "show", "onHide", "Header", "closeButton", "Title", "Body", "Group", "Label", "Control", "onChange", "e", "target", "day", "Select", "parseFloat", "as", "rows", "override", "controlId", "Footer", "disabled", "parseInt", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/pages/SettingsPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Form, Alert, ListGroup, Modal } from 'react-bootstrap';\nimport { getAllPatterns, createPattern, updatePattern, deletePattern, getAllShifts, copyPattern } from '../services/api';\n\nfunction SettingsPage() {\n  const [patterns, setPatterns] = useState([]);\n  const [currentPattern, setCurrentPattern] = useState(null);\n  const [showPatternForm, setShowPatternForm] = useState(false);\n  const [availableShifts, setAvailableShifts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showCopyModal, setShowCopyModal] = useState(false);\n  const [patternToCopy, setPatternToCopy] = useState(null);\n  const [newPatternName, setNewPatternName] = useState('');\n  const [newPatternYear, setNewPatternYear] = useState(new Date().getFullYear() + 1);\n\n  useEffect(() => {\n    loadPatterns();\n    loadAvailableShifts();\n  }, []);\n\n  const loadPatterns = async () => {\n    setLoading(true);\n    try {\n      const data = await getAllPatterns();\n      setPatterns(data);\n    } catch (err) {\n      setError('Error cargando patrones.');\n      console.error('Error loading patterns:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadAvailableShifts = async () => {\n    try {\n      const shifts = await getAllShifts();\n      setAvailableShifts(shifts);\n    } catch (err) {\n      console.error('Error loading shifts:', err);\n    }\n  };\n\n  const handleAddPattern = () => {\n    setCurrentPattern({\n      name: '',\n      basePattern: Array.from({ length: 7 }, () => ({ type: '', hours: 0, description: '', shiftId: null })),\n      overrides: [],\n    });\n    setShowPatternForm(true);\n  };\n\n  const handleEditPattern = (pattern) => {\n    setCurrentPattern({ ...pattern });\n    setShowPatternForm(true);\n  };\n\n  const handleDeletePattern = async (id) => {\n    if (window.confirm('¿Estás seguro de que quieres eliminar este patrón?')) {\n      setLoading(true);\n      try {\n        await deletePattern(id);\n        await loadPatterns();\n      } catch (err) {\n        setError('Error eliminando patrón.');\n        console.error('Error deleting pattern:', err);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  const handleSavePattern = async () => {\n    if (!currentPattern || !currentPattern.name || !currentPattern.basePattern) {\n      setError('El nombre del patrón y el patrón base son requeridos.');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    try {\n      if (currentPattern.id) {\n        await updatePattern(currentPattern.id, currentPattern.name, currentPattern.basePattern, currentPattern.overrides);\n      } else {\n        await createPattern(currentPattern.name, currentPattern.basePattern, currentPattern.overrides);\n      }\n      setShowPatternForm(false);\n      setCurrentPattern(null);\n      await loadPatterns();\n    } catch (err) {\n      setError('Error guardando patrón.');\n      console.error('Error saving pattern:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancelEdit = () => {\n    setShowPatternForm(false);\n    setCurrentPattern(null);\n    setError('');\n  };\n\n  const handleShowCopyModal = (pattern) => {\n    setPatternToCopy(pattern);\n    setNewPatternName(`${pattern.name} - ${new Date().getFullYear() + 1}`);\n    setNewPatternYear(new Date().getFullYear() + 1);\n    setShowCopyModal(true);\n  };\n\n  const handleCopyPattern = async () => {\n    if (!patternToCopy || !newPatternName || !newPatternYear) {\n      setError('Nombre y año para la copia son requeridos.');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    try {\n      await copyPattern(patternToCopy.id, newPatternName, newPatternYear);\n      setShowCopyModal(false);\n      setPatternToCopy(null);\n      setNewPatternName('');\n      setNewPatternYear(new Date().getFullYear() + 1);\n      await loadPatterns();\n      alert('Patrón copiado exitosamente!');\n    } catch (err) {\n      setError('Error copiando patrón.');\n      console.error('Error copying pattern:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBasePatternDayChange = (index, field, value) => {\n    const newBasePattern = [...currentPattern.basePattern];\n    newBasePattern[index][field] = value;\n\n    if (field === 'shiftId' && value) {\n      const shift = availableShifts.find(s => s.id === value);\n      if (shift) {\n        newBasePattern[index].type = 'worked';\n        newBasePattern[index].hours = shift.totalHours;\n        newBasePattern[index].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;\n      }\n    } else if (field === 'shiftId' && !value) {\n      newBasePattern[index].description = '';\n    }\n    setCurrentPattern({ ...currentPattern, basePattern: newBasePattern });\n  };\n\n  const handleOverrideChange = (overrideIndex, field, value) => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex][field] = value;\n    setCurrentPattern({ ...currentPattern, overrides: newOverrides });\n  };\n\n  const handleOverridePatternDayChange = (overrideIndex, dayIndex, field, value) => {\n    const newOverrides = [...currentPattern.overrides];\n    const newOverridePattern = [...newOverrides[overrideIndex].overridePattern];\n    newOverridePattern[dayIndex][field] = value;\n\n    if (field === 'shiftId' && value) {\n      const shift = availableShifts.find(s => s.id === value);\n      if (shift) {\n        newOverridePattern[dayIndex].type = 'worked';\n        newOverridePattern[dayIndex].hours = shift.totalHours;\n        newOverridePattern[dayIndex].description = `${shift.name}: ${shift.startTime} - ${shift.endTime} (${shift.breakMinutes}min descanso)`;\n      }\n    } else if (field === 'shiftId' && !value) {\n      newOverridePattern[dayIndex].description = '';\n    }\n\n    newOverrides[overrideIndex].overridePattern = newOverridePattern;\n    setCurrentPattern({ ...currentPattern, overrides: newOverrides });\n  };\n\n  const handleAddOverride = () => {\n    setCurrentPattern({\n      ...currentPattern,\n      overrides: [...currentPattern.overrides, { startDate: '', endDate: '', overridePattern: [{ type: '', hours: 0, description: '', shiftId: null }] }]\n    });\n  };\n\n  const handleRemoveOverride = (overrideIndex) => {\n    const newOverrides = currentPattern.overrides.filter((_, i) => i !== overrideIndex);\n    setCurrentPattern({ ...currentPattern, overrides: newOverrides });\n  };\n\n  const handleAddOverridePatternDay = (overrideIndex) => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex].overridePattern.push({ type: '', hours: 0, description: '', shiftId: null });\n    setCurrentPattern({ ...currentPattern, overrides: newOverrides });\n  };\n\n  const handleRemoveOverridePatternDay = (overrideIndex, dayIndex) => {\n    const newOverrides = [...currentPattern.overrides];\n    newOverrides[overrideIndex].overridePattern = newOverrides[overrideIndex].overridePattern.filter((_, i) => i !== dayIndex);\n    setCurrentPattern({ ...currentPattern, overrides: newOverrides });\n  };\n\n  return (\n    <Container>\n      <Row className=\"my-4\">\n        <Col>\n          <h2>Configuración de Patrones de Calendario</h2>\n          {error && <Alert variant=\"danger\">{error}</Alert>}\n          <Button onClick={handleAddPattern} className=\"mb-3\">Crear Nuevo Patrón</Button>\n\n          {loading ? (\n            <p>Cargando patrones...</p>\n          ) : patterns.length === 0 ? (\n            <p>No hay patrones definidos.</p>\n          ) : (\n            <ListGroup>\n              {patterns.map(pattern => (\n                <ListGroup.Item key={pattern.id} className=\"d-flex justify-content-between align-items-center\">\n                  {pattern.name}\n                  <div>\n                    <Button variant=\"info\" size=\"sm\" className=\"me-2\" onClick={() => handleEditPattern(pattern)}>Editar</Button>\n                    <Button variant=\"secondary\" size=\"sm\" className=\"me-2\" onClick={() => handleShowCopyModal(pattern)}>Copiar</Button>\n                    <Button variant=\"danger\" size=\"sm\" onClick={() => handleDeletePattern(pattern.id)}>Eliminar</Button>\n                  </div>\n                </ListGroup.Item>\n              ))}\n            </ListGroup>\n          )}\n        </Col>\n      </Row>\n\n      <Modal show={showPatternForm} onHide={handleCancelEdit} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>{currentPattern && currentPattern.id ? 'Editar Patrón' : 'Crear Nuevo Patrón'}</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {currentPattern && (\n            <Form>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Nombre del Patrón</Form.Label>\n                <Form.Control\n                  type=\"text\"\n                  value={currentPattern.name}\n                  onChange={(e) => setCurrentPattern({ ...currentPattern, name: e.target.value })}\n                />\n              </Form.Group>\n\n              <h5>Patrón Base (Semanal):</h5>\n              {currentPattern.basePattern.map((day, index) => (\n                <div key={index} className=\"border p-3 mb-2\">\n                  <h6>Día {['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'][index]}</h6>\n                  <Form.Group className=\"mb-2\">\n                    <Form.Label>Tipo de Día</Form.Label>\n                    <Form.Select\n                      value={day.type}\n                      onChange={(e) => handleBasePatternDayChange(index, 'type', e.target.value)}\n                    >\n                      <option value=\"\">Seleccionar</option>\n                      <option value=\"worked\">Trabajado</option>\n                      <option value=\"holiday\">Vacaciones</option>\n                      <option value=\"permit\">Permiso</option>\n                      <option value=\"negative\">Cómputo Negativo</option>\n                    </Form.Select>\n                  </Form.Group>\n                  <Form.Group className=\"mb-2\">\n                    <Form.Label>Horas</Form.Label>\n                    <Form.Control\n                      type=\"number\"\n                      value={day.hours}\n                      onChange={(e) => handleBasePatternDayChange(index, 'hours', parseFloat(e.target.value))}\n                    />\n                  </Form.Group>\n                  <Form.Group className=\"mb-2\">\n                    <Form.Label>Turno Predefinido</Form.Label>\n                    <Form.Select\n                      value={day.shiftId || ''}\n                      onChange={(e) => handleBasePatternDayChange(index, 'shiftId', e.target.value)}\n                    >\n                      <option value=\"\">Ninguno</option>\n                      {availableShifts.map((shift) => (\n                        <option key={shift.id} value={shift.id}>\n                          {shift.name} - {shift.startTime} a {shift.endTime} ({shift.totalHours}h)\n                        </option>\n                      ))}\n                    </Form.Select>\n                  </Form.Group>\n                  <Form.Group className=\"mb-2\">\n                    <Form.Label>Descripción</Form.Label>\n                    <Form.Control\n                      as=\"textarea\"\n                      rows={1}\n                      value={day.description}\n                      onChange={(e) => handleBasePatternDayChange(index, 'description', e.target.value)}\n                    />\n                  </Form.Group>\n                </div>\n              ))}\n\n              <h5 className=\"mt-4\">Anulaciones (Overrides):</h5>\n              {currentPattern.overrides.map((override, overrideIndex) => (\n                <div key={overrideIndex} className=\"border p-3 mb-2 bg-light\">\n                  <h6>Anulación {overrideIndex + 1}</h6>\n                  <Row className=\"mb-2\">\n                    <Col>\n                      <Form.Group controlId={`overrideStartDate-${overrideIndex}`}>\n                        <Form.Label>Fecha de Inicio</Form.Label>\n                        <Form.Control\n                          type=\"date\"\n                          value={override.startDate}\n                          onChange={(e) => handleOverrideChange(overrideIndex, 'startDate', e.target.value)}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col>\n                      <Form.Group controlId={`overrideEndDate-${overrideIndex}`}>\n                        <Form.Label>Fecha de Fin</Form.Label>\n                        <Form.Control\n                          type=\"date\"\n                          value={override.endDate}\n                          onChange={(e) => handleOverrideChange(overrideIndex, 'endDate', e.target.value)}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n                  <h6>Días de la Anulación:</h6>\n                  {override.overridePattern.map((day, dayIndex) => (\n                    <div key={dayIndex} className=\"border p-3 mb-2 ms-3\">\n                      <h6>Día {dayIndex + 1}</h6>\n                      <Form.Group className=\"mb-2\">\n                        <Form.Label>Tipo de Día</Form.Label>\n                        <Form.Select\n                          value={day.type}\n                          onChange={(e) => handleOverridePatternDayChange(overrideIndex, dayIndex, 'type', e.target.value)}\n                        >\n                          <option value=\"\">Seleccionar</option>\n                          <option value=\"worked\">Trabajado</option>\n                          <option value=\"holiday\">Vacaciones</option>\n                          <option value=\"permit\">Permiso</option>\n                          <option value=\"negative\">Cómputo Negativo</option>\n                        </Form.Select>\n                      </Form.Group>\n                      <Form.Group className=\"mb-2\">\n                        <Form.Label>Horas</Form.Label>\n                        <Form.Control\n                          type=\"number\"\n                          value={day.hours}\n                          onChange={(e) => handleOverridePatternDayChange(overrideIndex, dayIndex, 'hours', parseFloat(e.target.value))}\n                        />\n                      </Form.Group>\n                      <Form.Group className=\"mb-2\">\n                        <Form.Label>Turno Predefinido</Form.Label>\n                        <Form.Select\n                          value={day.shiftId || ''}\n                          onChange={(e) => handleOverridePatternDayChange(overrideIndex, dayIndex, 'shiftId', e.target.value)}\n                        >\n                          <option value=\"\">Ninguno</option>\n                          {availableShifts.map((shift) => (\n                            <option key={shift.id} value={shift.id}>\n                              {shift.name} - {shift.startTime} a {shift.endTime} ({shift.totalHours}h)\n                            </option>\n                          ))}\n                        </Form.Select>\n                      </Form.Group>\n                      <Form.Group className=\"mb-2\">\n                        <Form.Label>Descripción</Form.Label>\n                        <Form.Control\n                          as=\"textarea\"\n                          rows={1}\n                          value={day.description}\n                          onChange={(e) => handleOverridePatternDayChange(overrideIndex, dayIndex, 'description', e.target.value)}\n                        />\n                      </Form.Group>\n                      <Button variant=\"danger\" size=\"sm\" onClick={() => handleRemoveOverridePatternDay(overrideIndex, dayIndex)}>\n                        Eliminar Día de Anulación\n                      </Button>\n                    </div>\n                  ))}\n                  <Button variant=\"secondary\" onClick={() => handleAddOverridePatternDay(overrideIndex)} className=\"mt-3\">Añadir Día a Anulación</Button>\n                  <Button variant=\"danger\" onClick={() => handleRemoveOverride(overrideIndex)} className=\"mt-3 ms-2\">Eliminar Anulación</Button>\n                </div>\n              ))}\n              <Button variant=\"info\" onClick={handleAddOverride} className=\"mt-3\">Añadir Anulación</Button>\n            </Form>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={handleCancelEdit}>Cancelar</Button>\n          <Button variant=\"primary\" onClick={handleSavePattern} disabled={loading}>Guardar Patrón</Button>\n        </Modal.Footer>\n      </Modal>\n\n      {/* Modal para Copiar Patrón */}\n      <Modal show={showCopyModal} onHide={() => setShowCopyModal(false)}>\n        <Modal.Header closeButton>\n          <Modal.Title>Copiar Patrón</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {error && <Alert variant=\"danger\">{error}</Alert>}\n          {patternToCopy && (\n            <Form>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Nombre del Patrón Original</Form.Label>\n                <Form.Control type=\"text\" value={patternToCopy.name} disabled />\n              </Form.Group>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Nuevo Nombre del Patrón</Form.Label>\n                <Form.Control\n                  type=\"text\"\n                  value={newPatternName}\n                  onChange={(e) => setNewPatternName(e.target.value)}\n                />\n              </Form.Group>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Año para el Nuevo Patrón</Form.Label>\n                <Form.Control\n                  type=\"number\"\n                  value={newPatternYear}\n                  onChange={(e) => setNewPatternYear(parseInt(e.target.value, 10))}\n                />\n              </Form.Group>\n            </Form>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowCopyModal(false)}>Cancelar</Button>\n          <Button variant=\"primary\" onClick={handleCopyPattern} disabled={loading}>Copiar Patrón</Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n}\n\nexport default SettingsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAQ,iBAAiB;AAC5F,SAASC,cAAc,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,EAAEC,WAAW,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzH,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsB,cAAc,EAAEC,iBAAiB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACsC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAC,IAAIwC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;EAElFxC,SAAS,CAAC,MAAM;IACdyC,YAAY,CAAC,CAAC;IACdC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/Bb,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMe,IAAI,GAAG,MAAMlC,cAAc,CAAC,CAAC;MACnCW,WAAW,CAACuB,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZd,QAAQ,CAAC,0BAA0B,CAAC;MACpCe,OAAO,CAAChB,KAAK,CAAC,yBAAyB,EAAEe,GAAG,CAAC;IAC/C,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMI,MAAM,GAAG,MAAMjC,YAAY,CAAC,CAAC;MACnCa,kBAAkB,CAACoB,MAAM,CAAC;IAC5B,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAAChB,KAAK,CAAC,uBAAuB,EAAEe,GAAG,CAAC;IAC7C;EACF,CAAC;EAED,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzB,iBAAiB,CAAC;MAChB0B,IAAI,EAAE,EAAE;MACRC,WAAW,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,EAAE,OAAO;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,WAAW,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC,CAAC;MACtGC,SAAS,EAAE;IACb,CAAC,CAAC;IACFjC,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMkC,iBAAiB,GAAIC,OAAO,IAAK;IACrCrC,iBAAiB,CAAC;MAAE,GAAGqC;IAAQ,CAAC,CAAC;IACjCnC,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMoC,mBAAmB,GAAG,MAAOC,EAAE,IAAK;IACxC,IAAIC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACxEnC,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMhB,aAAa,CAACiD,EAAE,CAAC;QACvB,MAAMpB,YAAY,CAAC,CAAC;MACtB,CAAC,CAAC,OAAOG,GAAG,EAAE;QACZd,QAAQ,CAAC,0BAA0B,CAAC;QACpCe,OAAO,CAAChB,KAAK,CAAC,yBAAyB,EAAEe,GAAG,CAAC;MAC/C,CAAC,SAAS;QACRhB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;EAED,MAAMoC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC3C,cAAc,IAAI,CAACA,cAAc,CAAC2B,IAAI,IAAI,CAAC3B,cAAc,CAAC4B,WAAW,EAAE;MAC1EnB,QAAQ,CAAC,uDAAuD,CAAC;MACjE;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,IAAIT,cAAc,CAACwC,EAAE,EAAE;QACrB,MAAMlD,aAAa,CAACU,cAAc,CAACwC,EAAE,EAAExC,cAAc,CAAC2B,IAAI,EAAE3B,cAAc,CAAC4B,WAAW,EAAE5B,cAAc,CAACoC,SAAS,CAAC;MACnH,CAAC,MAAM;QACL,MAAM/C,aAAa,CAACW,cAAc,CAAC2B,IAAI,EAAE3B,cAAc,CAAC4B,WAAW,EAAE5B,cAAc,CAACoC,SAAS,CAAC;MAChG;MACAjC,kBAAkB,CAAC,KAAK,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;MACvB,MAAMmB,YAAY,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZd,QAAQ,CAAC,yBAAyB,CAAC;MACnCe,OAAO,CAAChB,KAAK,CAAC,uBAAuB,EAAEe,GAAG,CAAC;IAC7C,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzC,kBAAkB,CAAC,KAAK,CAAC;IACzBF,iBAAiB,CAAC,IAAI,CAAC;IACvBQ,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMoC,mBAAmB,GAAIP,OAAO,IAAK;IACvCzB,gBAAgB,CAACyB,OAAO,CAAC;IACzBvB,iBAAiB,CAAC,GAAGuB,OAAO,CAACX,IAAI,MAAM,IAAIT,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;IACtEF,iBAAiB,CAAC,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/CR,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMmC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAClC,aAAa,IAAI,CAACE,cAAc,IAAI,CAACE,cAAc,EAAE;MACxDP,QAAQ,CAAC,4CAA4C,CAAC;MACtD;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,MAAMhB,WAAW,CAACmB,aAAa,CAAC4B,EAAE,EAAE1B,cAAc,EAAEE,cAAc,CAAC;MACnEL,gBAAgB,CAAC,KAAK,CAAC;MACvBE,gBAAgB,CAAC,IAAI,CAAC;MACtBE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,iBAAiB,CAAC,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;MAC/C,MAAMC,YAAY,CAAC,CAAC;MACpB2B,KAAK,CAAC,8BAA8B,CAAC;IACvC,CAAC,CAAC,OAAOxB,GAAG,EAAE;MACZd,QAAQ,CAAC,wBAAwB,CAAC;MAClCe,OAAO,CAAChB,KAAK,CAAC,wBAAwB,EAAEe,GAAG,CAAC;IAC9C,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyC,0BAA0B,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,KAAK;IAC1D,MAAMC,cAAc,GAAG,CAAC,GAAGpD,cAAc,CAAC4B,WAAW,CAAC;IACtDwB,cAAc,CAACH,KAAK,CAAC,CAACC,KAAK,CAAC,GAAGC,KAAK;IAEpC,IAAID,KAAK,KAAK,SAAS,IAAIC,KAAK,EAAE;MAChC,MAAME,KAAK,GAAGjD,eAAe,CAACkD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACf,EAAE,KAAKW,KAAK,CAAC;MACvD,IAAIE,KAAK,EAAE;QACTD,cAAc,CAACH,KAAK,CAAC,CAACjB,IAAI,GAAG,QAAQ;QACrCoB,cAAc,CAACH,KAAK,CAAC,CAAChB,KAAK,GAAGoB,KAAK,CAACG,UAAU;QAC9CJ,cAAc,CAACH,KAAK,CAAC,CAACf,WAAW,GAAG,GAAGmB,KAAK,CAAC1B,IAAI,KAAK0B,KAAK,CAACI,SAAS,MAAMJ,KAAK,CAACK,OAAO,KAAKL,KAAK,CAACM,YAAY,eAAe;MAChI;IACF,CAAC,MAAM,IAAIT,KAAK,KAAK,SAAS,IAAI,CAACC,KAAK,EAAE;MACxCC,cAAc,CAACH,KAAK,CAAC,CAACf,WAAW,GAAG,EAAE;IACxC;IACAjC,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAE4B,WAAW,EAAEwB;IAAe,CAAC,CAAC;EACvE,CAAC;EAED,MAAMQ,oBAAoB,GAAGA,CAACC,aAAa,EAAEX,KAAK,EAAEC,KAAK,KAAK;IAC5D,MAAMW,YAAY,GAAG,CAAC,GAAG9D,cAAc,CAACoC,SAAS,CAAC;IAClD0B,YAAY,CAACD,aAAa,CAAC,CAACX,KAAK,CAAC,GAAGC,KAAK;IAC1ClD,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAEoC,SAAS,EAAE0B;IAAa,CAAC,CAAC;EACnE,CAAC;EAED,MAAMC,8BAA8B,GAAGA,CAACF,aAAa,EAAEG,QAAQ,EAAEd,KAAK,EAAEC,KAAK,KAAK;IAChF,MAAMW,YAAY,GAAG,CAAC,GAAG9D,cAAc,CAACoC,SAAS,CAAC;IAClD,MAAM6B,kBAAkB,GAAG,CAAC,GAAGH,YAAY,CAACD,aAAa,CAAC,CAACK,eAAe,CAAC;IAC3ED,kBAAkB,CAACD,QAAQ,CAAC,CAACd,KAAK,CAAC,GAAGC,KAAK;IAE3C,IAAID,KAAK,KAAK,SAAS,IAAIC,KAAK,EAAE;MAChC,MAAME,KAAK,GAAGjD,eAAe,CAACkD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACf,EAAE,KAAKW,KAAK,CAAC;MACvD,IAAIE,KAAK,EAAE;QACTY,kBAAkB,CAACD,QAAQ,CAAC,CAAChC,IAAI,GAAG,QAAQ;QAC5CiC,kBAAkB,CAACD,QAAQ,CAAC,CAAC/B,KAAK,GAAGoB,KAAK,CAACG,UAAU;QACrDS,kBAAkB,CAACD,QAAQ,CAAC,CAAC9B,WAAW,GAAG,GAAGmB,KAAK,CAAC1B,IAAI,KAAK0B,KAAK,CAACI,SAAS,MAAMJ,KAAK,CAACK,OAAO,KAAKL,KAAK,CAACM,YAAY,eAAe;MACvI;IACF,CAAC,MAAM,IAAIT,KAAK,KAAK,SAAS,IAAI,CAACC,KAAK,EAAE;MACxCc,kBAAkB,CAACD,QAAQ,CAAC,CAAC9B,WAAW,GAAG,EAAE;IAC/C;IAEA4B,YAAY,CAACD,aAAa,CAAC,CAACK,eAAe,GAAGD,kBAAkB;IAChEhE,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAEoC,SAAS,EAAE0B;IAAa,CAAC,CAAC;EACnE,CAAC;EAED,MAAMK,iBAAiB,GAAGA,CAAA,KAAM;IAC9BlE,iBAAiB,CAAC;MAChB,GAAGD,cAAc;MACjBoC,SAAS,EAAE,CAAC,GAAGpC,cAAc,CAACoC,SAAS,EAAE;QAAEgC,SAAS,EAAE,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEH,eAAe,EAAE,CAAC;UAAElC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,CAAC;UAAEC,WAAW,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAK,CAAC;MAAE,CAAC;IACpJ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMmC,oBAAoB,GAAIT,aAAa,IAAK;IAC9C,MAAMC,YAAY,GAAG9D,cAAc,CAACoC,SAAS,CAACmC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKZ,aAAa,CAAC;IACnF5D,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAEoC,SAAS,EAAE0B;IAAa,CAAC,CAAC;EACnE,CAAC;EAED,MAAMY,2BAA2B,GAAIb,aAAa,IAAK;IACrD,MAAMC,YAAY,GAAG,CAAC,GAAG9D,cAAc,CAACoC,SAAS,CAAC;IAClD0B,YAAY,CAACD,aAAa,CAAC,CAACK,eAAe,CAACS,IAAI,CAAC;MAAE3C,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,WAAW,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IACxGlC,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAEoC,SAAS,EAAE0B;IAAa,CAAC,CAAC;EACnE,CAAC;EAED,MAAMc,8BAA8B,GAAGA,CAACf,aAAa,EAAEG,QAAQ,KAAK;IAClE,MAAMF,YAAY,GAAG,CAAC,GAAG9D,cAAc,CAACoC,SAAS,CAAC;IAClD0B,YAAY,CAACD,aAAa,CAAC,CAACK,eAAe,GAAGJ,YAAY,CAACD,aAAa,CAAC,CAACK,eAAe,CAACK,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKT,QAAQ,CAAC;IAC1H/D,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAEoC,SAAS,EAAE0B;IAAa,CAAC,CAAC;EACnE,CAAC;EAED,oBACEnE,OAAA,CAACf,SAAS;IAAAiG,QAAA,gBACRlF,OAAA,CAACd,GAAG;MAACiG,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBlF,OAAA,CAACb,GAAG;QAAA+F,QAAA,gBACFlF,OAAA;UAAAkF,QAAA,EAAI;QAAuC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC/C1E,KAAK,iBAAIb,OAAA,CAACV,KAAK;UAACkG,OAAO,EAAC,QAAQ;UAAAN,QAAA,EAAErE;QAAK;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACjDvF,OAAA,CAACZ,MAAM;UAACqG,OAAO,EAAE1D,gBAAiB;UAACoD,SAAS,EAAC,MAAM;UAAAD,QAAA,EAAC;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAE9E5E,OAAO,gBACNX,OAAA;UAAAkF,QAAA,EAAG;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,GACzBpF,QAAQ,CAACiC,MAAM,KAAK,CAAC,gBACvBpC,OAAA;UAAAkF,QAAA,EAAG;QAA0B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAEjCvF,OAAA,CAACT,SAAS;UAAA2F,QAAA,EACP/E,QAAQ,CAACuF,GAAG,CAAC/C,OAAO,iBACnB3C,OAAA,CAACT,SAAS,CAACoG,IAAI;YAAkBR,SAAS,EAAC,mDAAmD;YAAAD,QAAA,GAC3FvC,OAAO,CAACX,IAAI,eACbhC,OAAA;cAAAkF,QAAA,gBACElF,OAAA,CAACZ,MAAM;gBAACoG,OAAO,EAAC,MAAM;gBAACI,IAAI,EAAC,IAAI;gBAACT,SAAS,EAAC,MAAM;gBAACM,OAAO,EAAEA,CAAA,KAAM/C,iBAAiB,CAACC,OAAO,CAAE;gBAAAuC,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5GvF,OAAA,CAACZ,MAAM;gBAACoG,OAAO,EAAC,WAAW;gBAACI,IAAI,EAAC,IAAI;gBAACT,SAAS,EAAC,MAAM;gBAACM,OAAO,EAAEA,CAAA,KAAMvC,mBAAmB,CAACP,OAAO,CAAE;gBAAAuC,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnHvF,OAAA,CAACZ,MAAM;gBAACoG,OAAO,EAAC,QAAQ;gBAACI,IAAI,EAAC,IAAI;gBAACH,OAAO,EAAEA,CAAA,KAAM7C,mBAAmB,CAACD,OAAO,CAACE,EAAE,CAAE;gBAAAqC,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjG,CAAC;UAAA,GANa5C,OAAO,CAACE,EAAE;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOf,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CACZ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENvF,OAAA,CAACR,KAAK;MAACqG,IAAI,EAAEtF,eAAgB;MAACuF,MAAM,EAAE7C,gBAAiB;MAAC2C,IAAI,EAAC,IAAI;MAAAV,QAAA,gBAC/DlF,OAAA,CAACR,KAAK,CAACuG,MAAM;QAACC,WAAW;QAAAd,QAAA,eACvBlF,OAAA,CAACR,KAAK,CAACyG,KAAK;UAAAf,QAAA,EAAE7E,cAAc,IAAIA,cAAc,CAACwC,EAAE,GAAG,eAAe,GAAG;QAAoB;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F,CAAC,eACfvF,OAAA,CAACR,KAAK,CAAC0G,IAAI;QAAAhB,QAAA,EACR7E,cAAc,iBACbL,OAAA,CAACX,IAAI;UAAA6F,QAAA,gBACHlF,OAAA,CAACX,IAAI,CAAC8G,KAAK;YAAChB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BlF,OAAA,CAACX,IAAI,CAAC+G,KAAK;cAAAlB,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1CvF,OAAA,CAACX,IAAI,CAACgH,OAAO;cACXhE,IAAI,EAAC,MAAM;cACXmB,KAAK,EAAEnD,cAAc,CAAC2B,IAAK;cAC3BsE,QAAQ,EAAGC,CAAC,IAAKjG,iBAAiB,CAAC;gBAAE,GAAGD,cAAc;gBAAE2B,IAAI,EAAEuE,CAAC,CAACC,MAAM,CAAChD;cAAM,CAAC;YAAE;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEbvF,OAAA;YAAAkF,QAAA,EAAI;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC9BlF,cAAc,CAAC4B,WAAW,CAACyD,GAAG,CAAC,CAACe,GAAG,EAAEnD,KAAK,kBACzCtD,OAAA;YAAiBmF,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC1ClF,OAAA;cAAAkF,QAAA,GAAI,SAAI,EAAC,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC5B,KAAK,CAAC;YAAA;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChGvF,OAAA,CAACX,IAAI,CAAC8G,KAAK;cAAChB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BlF,OAAA,CAACX,IAAI,CAAC+G,KAAK;gBAAAlB,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpCvF,OAAA,CAACX,IAAI,CAACqH,MAAM;gBACVlD,KAAK,EAAEiD,GAAG,CAACpE,IAAK;gBAChBiE,QAAQ,EAAGC,CAAC,IAAKlD,0BAA0B,CAACC,KAAK,EAAE,MAAM,EAAEiD,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAE;gBAAA0B,QAAA,gBAE3ElF,OAAA;kBAAQwD,KAAK,EAAC,EAAE;kBAAA0B,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCvF,OAAA;kBAAQwD,KAAK,EAAC,QAAQ;kBAAA0B,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCvF,OAAA;kBAAQwD,KAAK,EAAC,SAAS;kBAAA0B,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3CvF,OAAA;kBAAQwD,KAAK,EAAC,QAAQ;kBAAA0B,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCvF,OAAA;kBAAQwD,KAAK,EAAC,UAAU;kBAAA0B,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACbvF,OAAA,CAACX,IAAI,CAAC8G,KAAK;cAAChB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BlF,OAAA,CAACX,IAAI,CAAC+G,KAAK;gBAAAlB,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9BvF,OAAA,CAACX,IAAI,CAACgH,OAAO;gBACXhE,IAAI,EAAC,QAAQ;gBACbmB,KAAK,EAAEiD,GAAG,CAACnE,KAAM;gBACjBgE,QAAQ,EAAGC,CAAC,IAAKlD,0BAA0B,CAACC,KAAK,EAAE,OAAO,EAAEqD,UAAU,CAACJ,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAC;cAAE;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eACbvF,OAAA,CAACX,IAAI,CAAC8G,KAAK;cAAChB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BlF,OAAA,CAACX,IAAI,CAAC+G,KAAK;gBAAAlB,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1CvF,OAAA,CAACX,IAAI,CAACqH,MAAM;gBACVlD,KAAK,EAAEiD,GAAG,CAACjE,OAAO,IAAI,EAAG;gBACzB8D,QAAQ,EAAGC,CAAC,IAAKlD,0BAA0B,CAACC,KAAK,EAAE,SAAS,EAAEiD,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAE;gBAAA0B,QAAA,gBAE9ElF,OAAA;kBAAQwD,KAAK,EAAC,EAAE;kBAAA0B,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAChC9E,eAAe,CAACiF,GAAG,CAAEhC,KAAK,iBACzB1D,OAAA;kBAAuBwD,KAAK,EAAEE,KAAK,CAACb,EAAG;kBAAAqC,QAAA,GACpCxB,KAAK,CAAC1B,IAAI,EAAC,KAAG,EAAC0B,KAAK,CAACI,SAAS,EAAC,KAAG,EAACJ,KAAK,CAACK,OAAO,EAAC,IAAE,EAACL,KAAK,CAACG,UAAU,EAAC,IACxE;gBAAA,GAFaH,KAAK,CAACb,EAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACbvF,OAAA,CAACX,IAAI,CAAC8G,KAAK;cAAChB,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BlF,OAAA,CAACX,IAAI,CAAC+G,KAAK;gBAAAlB,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpCvF,OAAA,CAACX,IAAI,CAACgH,OAAO;gBACXO,EAAE,EAAC,UAAU;gBACbC,IAAI,EAAE,CAAE;gBACRrD,KAAK,EAAEiD,GAAG,CAAClE,WAAY;gBACvB+D,QAAQ,EAAGC,CAAC,IAAKlD,0BAA0B,CAACC,KAAK,EAAE,aAAa,EAAEiD,CAAC,CAACC,MAAM,CAAChD,KAAK;cAAE;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA,GA7CLjC,KAAK;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8CV,CACN,CAAC,eAEFvF,OAAA;YAAImF,SAAS,EAAC,MAAM;YAAAD,QAAA,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACjDlF,cAAc,CAACoC,SAAS,CAACiD,GAAG,CAAC,CAACoB,QAAQ,EAAE5C,aAAa,kBACpDlE,OAAA;YAAyBmF,SAAS,EAAC,0BAA0B;YAAAD,QAAA,gBAC3DlF,OAAA;cAAAkF,QAAA,GAAI,eAAU,EAAChB,aAAa,GAAG,CAAC;YAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCvF,OAAA,CAACd,GAAG;cAACiG,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBlF,OAAA,CAACb,GAAG;gBAAA+F,QAAA,eACFlF,OAAA,CAACX,IAAI,CAAC8G,KAAK;kBAACY,SAAS,EAAE,qBAAqB7C,aAAa,EAAG;kBAAAgB,QAAA,gBAC1DlF,OAAA,CAACX,IAAI,CAAC+G,KAAK;oBAAAlB,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACxCvF,OAAA,CAACX,IAAI,CAACgH,OAAO;oBACXhE,IAAI,EAAC,MAAM;oBACXmB,KAAK,EAAEsD,QAAQ,CAACrC,SAAU;oBAC1B6B,QAAQ,EAAGC,CAAC,IAAKtC,oBAAoB,CAACC,aAAa,EAAE,WAAW,EAAEqC,CAAC,CAACC,MAAM,CAAChD,KAAK;kBAAE;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvF,OAAA,CAACb,GAAG;gBAAA+F,QAAA,eACFlF,OAAA,CAACX,IAAI,CAAC8G,KAAK;kBAACY,SAAS,EAAE,mBAAmB7C,aAAa,EAAG;kBAAAgB,QAAA,gBACxDlF,OAAA,CAACX,IAAI,CAAC+G,KAAK;oBAAAlB,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrCvF,OAAA,CAACX,IAAI,CAACgH,OAAO;oBACXhE,IAAI,EAAC,MAAM;oBACXmB,KAAK,EAAEsD,QAAQ,CAACpC,OAAQ;oBACxB4B,QAAQ,EAAGC,CAAC,IAAKtC,oBAAoB,CAACC,aAAa,EAAE,SAAS,EAAEqC,CAAC,CAACC,MAAM,CAAChD,KAAK;kBAAE;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvF,OAAA;cAAAkF,QAAA,EAAI;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC7BuB,QAAQ,CAACvC,eAAe,CAACmB,GAAG,CAAC,CAACe,GAAG,EAAEpC,QAAQ,kBAC1CrE,OAAA;cAAoBmF,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBAClDlF,OAAA;gBAAAkF,QAAA,GAAI,SAAI,EAACb,QAAQ,GAAG,CAAC;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3BvF,OAAA,CAACX,IAAI,CAAC8G,KAAK;gBAAChB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BlF,OAAA,CAACX,IAAI,CAAC+G,KAAK;kBAAAlB,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCvF,OAAA,CAACX,IAAI,CAACqH,MAAM;kBACVlD,KAAK,EAAEiD,GAAG,CAACpE,IAAK;kBAChBiE,QAAQ,EAAGC,CAAC,IAAKnC,8BAA8B,CAACF,aAAa,EAAEG,QAAQ,EAAE,MAAM,EAAEkC,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAE;kBAAA0B,QAAA,gBAEjGlF,OAAA;oBAAQwD,KAAK,EAAC,EAAE;oBAAA0B,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrCvF,OAAA;oBAAQwD,KAAK,EAAC,QAAQ;oBAAA0B,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzCvF,OAAA;oBAAQwD,KAAK,EAAC,SAAS;oBAAA0B,QAAA,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC3CvF,OAAA;oBAAQwD,KAAK,EAAC,QAAQ;oBAAA0B,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvCvF,OAAA;oBAAQwD,KAAK,EAAC,UAAU;oBAAA0B,QAAA,EAAC;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACbvF,OAAA,CAACX,IAAI,CAAC8G,KAAK;gBAAChB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BlF,OAAA,CAACX,IAAI,CAAC+G,KAAK;kBAAAlB,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9BvF,OAAA,CAACX,IAAI,CAACgH,OAAO;kBACXhE,IAAI,EAAC,QAAQ;kBACbmB,KAAK,EAAEiD,GAAG,CAACnE,KAAM;kBACjBgE,QAAQ,EAAGC,CAAC,IAAKnC,8BAA8B,CAACF,aAAa,EAAEG,QAAQ,EAAE,OAAO,EAAEsC,UAAU,CAACJ,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAC;gBAAE;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eACbvF,OAAA,CAACX,IAAI,CAAC8G,KAAK;gBAAChB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BlF,OAAA,CAACX,IAAI,CAAC+G,KAAK;kBAAAlB,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1CvF,OAAA,CAACX,IAAI,CAACqH,MAAM;kBACVlD,KAAK,EAAEiD,GAAG,CAACjE,OAAO,IAAI,EAAG;kBACzB8D,QAAQ,EAAGC,CAAC,IAAKnC,8BAA8B,CAACF,aAAa,EAAEG,QAAQ,EAAE,SAAS,EAAEkC,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAE;kBAAA0B,QAAA,gBAEpGlF,OAAA;oBAAQwD,KAAK,EAAC,EAAE;oBAAA0B,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAChC9E,eAAe,CAACiF,GAAG,CAAEhC,KAAK,iBACzB1D,OAAA;oBAAuBwD,KAAK,EAAEE,KAAK,CAACb,EAAG;oBAAAqC,QAAA,GACpCxB,KAAK,CAAC1B,IAAI,EAAC,KAAG,EAAC0B,KAAK,CAACI,SAAS,EAAC,KAAG,EAACJ,KAAK,CAACK,OAAO,EAAC,IAAE,EAACL,KAAK,CAACG,UAAU,EAAC,IACxE;kBAAA,GAFaH,KAAK,CAACb,EAAE;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEb,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACbvF,OAAA,CAACX,IAAI,CAAC8G,KAAK;gBAAChB,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BlF,OAAA,CAACX,IAAI,CAAC+G,KAAK;kBAAAlB,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCvF,OAAA,CAACX,IAAI,CAACgH,OAAO;kBACXO,EAAE,EAAC,UAAU;kBACbC,IAAI,EAAE,CAAE;kBACRrD,KAAK,EAAEiD,GAAG,CAAClE,WAAY;kBACvB+D,QAAQ,EAAGC,CAAC,IAAKnC,8BAA8B,CAACF,aAAa,EAAEG,QAAQ,EAAE,aAAa,EAAEkC,CAAC,CAACC,MAAM,CAAChD,KAAK;gBAAE;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eACbvF,OAAA,CAACZ,MAAM;gBAACoG,OAAO,EAAC,QAAQ;gBAACI,IAAI,EAAC,IAAI;gBAACH,OAAO,EAAEA,CAAA,KAAMR,8BAA8B,CAACf,aAAa,EAAEG,QAAQ,CAAE;gBAAAa,QAAA,EAAC;cAE3G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GAhDDlB,QAAQ;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiDb,CACN,CAAC,eACFvF,OAAA,CAACZ,MAAM;cAACoG,OAAO,EAAC,WAAW;cAACC,OAAO,EAAEA,CAAA,KAAMV,2BAA2B,CAACb,aAAa,CAAE;cAACiB,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvIvF,OAAA,CAACZ,MAAM;cAACoG,OAAO,EAAC,QAAQ;cAACC,OAAO,EAAEA,CAAA,KAAMd,oBAAoB,CAACT,aAAa,CAAE;cAACiB,SAAS,EAAC,WAAW;cAAAD,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,GA9EtHrB,aAAa;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+ElB,CACN,CAAC,eACFvF,OAAA,CAACZ,MAAM;YAACoG,OAAO,EAAC,MAAM;YAACC,OAAO,EAAEjB,iBAAkB;YAACW,SAAS,EAAC,MAAM;YAAAD,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACbvF,OAAA,CAACR,KAAK,CAACwH,MAAM;QAAA9B,QAAA,gBACXlF,OAAA,CAACZ,MAAM;UAACoG,OAAO,EAAC,WAAW;UAACC,OAAO,EAAExC,gBAAiB;UAAAiC,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxEvF,OAAA,CAACZ,MAAM;UAACoG,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEzC,iBAAkB;UAACiE,QAAQ,EAAEtG,OAAQ;UAAAuE,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGRvF,OAAA,CAACR,KAAK;MAACqG,IAAI,EAAE9E,aAAc;MAAC+E,MAAM,EAAEA,CAAA,KAAM9E,gBAAgB,CAAC,KAAK,CAAE;MAAAkE,QAAA,gBAChElF,OAAA,CAACR,KAAK,CAACuG,MAAM;QAACC,WAAW;QAAAd,QAAA,eACvBlF,OAAA,CAACR,KAAK,CAACyG,KAAK;UAAAf,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACfvF,OAAA,CAACR,KAAK,CAAC0G,IAAI;QAAAhB,QAAA,GACRrE,KAAK,iBAAIb,OAAA,CAACV,KAAK;UAACkG,OAAO,EAAC,QAAQ;UAAAN,QAAA,EAAErE;QAAK;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAChDtE,aAAa,iBACZjB,OAAA,CAACX,IAAI;UAAA6F,QAAA,gBACHlF,OAAA,CAACX,IAAI,CAAC8G,KAAK;YAAChB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BlF,OAAA,CAACX,IAAI,CAAC+G,KAAK;cAAAlB,QAAA,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnDvF,OAAA,CAACX,IAAI,CAACgH,OAAO;cAAChE,IAAI,EAAC,MAAM;cAACmB,KAAK,EAAEvC,aAAa,CAACe,IAAK;cAACiF,QAAQ;YAAA;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACbvF,OAAA,CAACX,IAAI,CAAC8G,KAAK;YAAChB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BlF,OAAA,CAACX,IAAI,CAAC+G,KAAK;cAAAlB,QAAA,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChDvF,OAAA,CAACX,IAAI,CAACgH,OAAO;cACXhE,IAAI,EAAC,MAAM;cACXmB,KAAK,EAAErC,cAAe;cACtBmF,QAAQ,EAAGC,CAAC,IAAKnF,iBAAiB,CAACmF,CAAC,CAACC,MAAM,CAAChD,KAAK;YAAE;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACbvF,OAAA,CAACX,IAAI,CAAC8G,KAAK;YAAChB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BlF,OAAA,CAACX,IAAI,CAAC+G,KAAK;cAAAlB,QAAA,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACjDvF,OAAA,CAACX,IAAI,CAACgH,OAAO;cACXhE,IAAI,EAAC,QAAQ;cACbmB,KAAK,EAAEnC,cAAe;cACtBiF,QAAQ,EAAGC,CAAC,IAAKjF,iBAAiB,CAAC4F,QAAQ,CAACX,CAAC,CAACC,MAAM,CAAChD,KAAK,EAAE,EAAE,CAAC;YAAE;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACbvF,OAAA,CAACR,KAAK,CAACwH,MAAM;QAAA9B,QAAA,gBACXlF,OAAA,CAACZ,MAAM;UAACoG,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMzE,gBAAgB,CAAC,KAAK,CAAE;UAAAkE,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACrFvF,OAAA,CAACZ,MAAM;UAACoG,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEtC,iBAAkB;UAAC8D,QAAQ,EAAEtG,OAAQ;UAAAuE,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB;AAACrF,EAAA,CAzaQD,YAAY;AAAAkH,EAAA,GAAZlH,YAAY;AA2arB,eAAeA,YAAY;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}