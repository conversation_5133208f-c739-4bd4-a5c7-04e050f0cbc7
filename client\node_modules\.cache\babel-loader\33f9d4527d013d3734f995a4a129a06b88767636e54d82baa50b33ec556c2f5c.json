{"ast": null, "code": "var _jsxFileName = \"D:\\\\Proyectos Python\\\\Horario\\\\client\\\\src\\\\components\\\\RealCalendarView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Button, Alert } from 'react-bootstrap';\nimport Calendar from 'react-calendar';\nimport RealDayModal from './RealDayModal';\nimport { getAllRealDays } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction RealCalendarView({\n  onDateSelect,\n  selectedDate,\n  onCalendarUpdate\n}) {\n  _s();\n  const [realDays, setRealDays] = useState([]);\n  const [showModal, setShowModal] = useState(false);\n  const [selectedDay, setSelectedDay] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    loadRealDays();\n  }, []);\n  const loadRealDays = async () => {\n    try {\n      setLoading(true);\n      const days = await getAllRealDays();\n      setRealDays(days);\n      setError('');\n    } catch (err) {\n      console.error('Error loading real days:', err);\n      setError('Error cargando días reales: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDateClick = date => {\n    onDateSelect(date);\n    const existingDay = realDays.find(day => new Date(day.date).toDateString() === date.toDateString());\n    setSelectedDay(existingDay);\n    setShowModal(true);\n  };\n  const handleDayUpdated = () => {\n    loadRealDays();\n    onCalendarUpdate();\n    setShowModal(false);\n  };\n  const getTileContent = ({\n    date,\n    view\n  }) => {\n    if (view !== 'month') return null;\n    const dayData = realDays.find(day => new Date(day.date).toDateString() === date.toDateString());\n    if (dayData) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"calendar-tile-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"badge bg-primary\",\n          children: [dayData.hours, \"h\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), dayData.entryTime && dayData.exitTime && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"time-range\",\n          children: [dayData.entryTime, \"-\", dayData.exitTime]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const getTileClassName = ({\n    date,\n    view\n  }) => {\n    if (view !== 'month') return '';\n    const dayData = realDays.find(day => new Date(day.date).toDateString() === date.toDateString());\n    if (dayData) {\n      return 'has-real-work';\n    }\n    return '';\n  };\n  const getTotalHours = () => {\n    return realDays.reduce((total, day) => total + (day.hours || 0), 0);\n  };\n  const getCurrentMonthHours = () => {\n    const currentMonth = new Date().getMonth();\n    const currentYear = new Date().getFullYear();\n    return realDays.filter(day => {\n      const dayDate = new Date(day.date);\n      return dayDate.getMonth() === currentMonth && dayDate.getFullYear() === currentYear;\n    }).reduce((total, day) => total + (day.hours || 0), 0);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Calendario de Trabajo Real\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              onClick: () => {\n                setSelectedDay(null);\n                setShowModal(true);\n              },\n              children: \"\\u2795 Registrar D\\xEDa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          onClose: () => setError(''),\n          dismissible: true,\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"calendar-container\",\n          children: /*#__PURE__*/_jsxDEV(Calendar, {\n            onChange: onDateSelect,\n            value: selectedDate,\n            onClickDay: handleDateClick,\n            tileContent: getTileContent,\n            tileClassName: getTileClassName,\n            locale: \"es-ES\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 4,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"calendar-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Estad\\xEDsticas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total de d\\xEDas registrados:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), \" \", realDays.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total de horas:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), \" \", getTotalHours().toFixed(1), \"h\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Horas este mes:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), \" \", getCurrentMonthHours().toFixed(1), \"h\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Promedio por d\\xEDa:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), \" \", realDays.length > 0 ? (getTotalHours() / realDays.length).toFixed(1) : 0, \"h\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), selectedDate && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"selected-date-info mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Fecha seleccionada:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: selectedDate.toLocaleDateString('es-ES', {\n              weekday: 'long',\n              year: 'numeric',\n              month: 'long',\n              day: 'numeric'\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this), realDays.find(day => new Date(day.date).toDateString() === selectedDate.toDateString()) ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"day-details\",\n            children: (() => {\n              const dayData = realDays.find(day => new Date(day.date).toDateString() === selectedDate.toDateString());\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Horas:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 28\n                  }, this), \" \", dayData.hours, \"h\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 25\n                }, this), dayData.entryTime && /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Entrada:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 50\n                  }, this), \" \", dayData.entryTime]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 47\n                }, this), dayData.exitTime && /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Salida:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 49\n                  }, this), \" \", dayData.exitTime]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 46\n                }, this), dayData.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Descripci\\xF3n:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 52\n                  }, this), \" \", dayData.description]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-primary\",\n                  size: \"sm\",\n                  onClick: () => handleDateClick(selectedDate),\n                  children: \"\\u270F\\uFE0F Editar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 23\n              }, this);\n            })()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"No hay registro para este d\\xEDa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-success\",\n              size: \"sm\",\n              onClick: () => handleDateClick(selectedDate),\n              children: \"\\u2795 Registrar\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RealDayModal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      selectedDate: selectedDate,\n      existingDay: selectedDay,\n      onDayUpdated: handleDayUpdated\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n}\n_s(RealCalendarView, \"jj+CooWzpLH243aVxWalaoq+UoA=\");\n_c = RealCalendarView;\nexport default RealCalendarView;\nvar _c;\n$RefreshReg$(_c, \"RealCalendarView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "<PERSON><PERSON>", "<PERSON><PERSON>", "Calendar", "RealDayModal", "getAllRealDays", "jsxDEV", "_jsxDEV", "RealCalendarView", "onDateSelect", "selectedDate", "onCalendarUpdate", "_s", "realDays", "setRealDays", "showModal", "setShowModal", "selected<PERSON>ay", "setSelectedDay", "loading", "setLoading", "error", "setError", "loadRealDays", "days", "err", "console", "message", "handleDateClick", "date", "existingDay", "find", "day", "Date", "toDateString", "handleDayUpdated", "getTileContent", "view", "dayData", "className", "children", "hours", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "entryTime", "exitTime", "getTileClassName", "getTotalHours", "reduce", "total", "getCurrentMonthHours", "currentMonth", "getMonth", "currentYear", "getFullYear", "filter", "dayDate", "variant", "onClick", "onClose", "dismissible", "md", "onChange", "value", "onClickDay", "tileContent", "tileClassName", "locale", "length", "toFixed", "toLocaleDateString", "weekday", "year", "month", "description", "size", "show", "onHide", "onDayUpdated", "_c", "$RefreshReg$"], "sources": ["D:/Proyectos Python/Horario/client/src/components/RealCalendarView.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Button, Alert } from 'react-bootstrap';\nimport Calendar from 'react-calendar';\nimport RealDayModal from './RealDayModal';\nimport { getAllRealDays } from '../services/api';\n\nfunction RealCalendarView({ onDateSelect, selectedDate, onCalendarUpdate }) {\n  const [realDays, setRealDays] = useState([]);\n  const [showModal, setShowModal] = useState(false);\n  const [selectedDay, setSelectedDay] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    loadRealDays();\n  }, []);\n\n  const loadRealDays = async () => {\n    try {\n      setLoading(true);\n      const days = await getAllRealDays();\n      setRealDays(days);\n      setError('');\n    } catch (err) {\n      console.error('Error loading real days:', err);\n      setError('Error cargando días reales: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDateClick = (date) => {\n    onDateSelect(date);\n    const existingDay = realDays.find(day => \n      new Date(day.date).toDateString() === date.toDateString()\n    );\n    setSelectedDay(existingDay);\n    setShowModal(true);\n  };\n\n  const handleDayUpdated = () => {\n    loadRealDays();\n    onCalendarUpdate();\n    setShowModal(false);\n  };\n\n  const getTileContent = ({ date, view }) => {\n    if (view !== 'month') return null;\n    \n    const dayData = realDays.find(day => \n      new Date(day.date).toDateString() === date.toDateString()\n    );\n    \n    if (dayData) {\n      return (\n        <div className=\"calendar-tile-content\">\n          <div className=\"badge bg-primary\">{dayData.hours}h</div>\n          {dayData.entryTime && dayData.exitTime && (\n            <div className=\"time-range\">\n              {dayData.entryTime}-{dayData.exitTime}\n            </div>\n          )}\n        </div>\n      );\n    }\n    return null;\n  };\n\n  const getTileClassName = ({ date, view }) => {\n    if (view !== 'month') return '';\n    \n    const dayData = realDays.find(day => \n      new Date(day.date).toDateString() === date.toDateString()\n    );\n    \n    if (dayData) {\n      return 'has-real-work';\n    }\n    return '';\n  };\n\n  const getTotalHours = () => {\n    return realDays.reduce((total, day) => total + (day.hours || 0), 0);\n  };\n\n  const getCurrentMonthHours = () => {\n    const currentMonth = new Date().getMonth();\n    const currentYear = new Date().getFullYear();\n    \n    return realDays\n      .filter(day => {\n        const dayDate = new Date(day.date);\n        return dayDate.getMonth() === currentMonth && dayDate.getFullYear() === currentYear;\n      })\n      .reduce((total, day) => total + (day.hours || 0), 0);\n  };\n\n  return (\n    <div>\n      <Row className=\"mb-3\">\n        <Col>\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <h4>Calendario de Trabajo Real</h4>\n            <div>\n              <Button\n                variant=\"primary\"\n                onClick={() => {\n                  setSelectedDay(null);\n                  setShowModal(true);\n                }}\n              >\n                ➕ Registrar Día\n              </Button>\n            </div>\n          </div>\n        </Col>\n      </Row>\n\n      {error && (\n        <Row className=\"mb-3\">\n          <Col>\n            <Alert variant=\"danger\" onClose={() => setError('')} dismissible>\n              {error}\n            </Alert>\n          </Col>\n        </Row>\n      )}\n\n      <Row className=\"mb-3\">\n        <Col md={8}>\n          <div className=\"calendar-container\">\n            <Calendar\n              onChange={onDateSelect}\n              value={selectedDate}\n              onClickDay={handleDateClick}\n              tileContent={getTileContent}\n              tileClassName={getTileClassName}\n              locale=\"es-ES\"\n            />\n          </div>\n        </Col>\n        <Col md={4}>\n          <div className=\"calendar-stats\">\n            <h5>Estadísticas</h5>\n            <div className=\"stat-item\">\n              <strong>Total de días registrados:</strong> {realDays.length}\n            </div>\n            <div className=\"stat-item\">\n              <strong>Total de horas:</strong> {getTotalHours().toFixed(1)}h\n            </div>\n            <div className=\"stat-item\">\n              <strong>Horas este mes:</strong> {getCurrentMonthHours().toFixed(1)}h\n            </div>\n            <div className=\"stat-item\">\n              <strong>Promedio por día:</strong> {realDays.length > 0 ? (getTotalHours() / realDays.length).toFixed(1) : 0}h\n            </div>\n          </div>\n\n          {selectedDate && (\n            <div className=\"selected-date-info mt-3\">\n              <h6>Fecha seleccionada:</h6>\n              <p>{selectedDate.toLocaleDateString('es-ES', { \n                weekday: 'long', \n                year: 'numeric', \n                month: 'long', \n                day: 'numeric' \n              })}</p>\n              \n              {realDays.find(day => \n                new Date(day.date).toDateString() === selectedDate.toDateString()\n              ) ? (\n                <div className=\"day-details\">\n                  {(() => {\n                    const dayData = realDays.find(day => \n                      new Date(day.date).toDateString() === selectedDate.toDateString()\n                    );\n                    return (\n                      <div>\n                        <p><strong>Horas:</strong> {dayData.hours}h</p>\n                        {dayData.entryTime && <p><strong>Entrada:</strong> {dayData.entryTime}</p>}\n                        {dayData.exitTime && <p><strong>Salida:</strong> {dayData.exitTime}</p>}\n                        {dayData.description && <p><strong>Descripción:</strong> {dayData.description}</p>}\n                        <Button \n                          variant=\"outline-primary\" \n                          size=\"sm\"\n                          onClick={() => handleDateClick(selectedDate)}\n                        >\n                          ✏️ Editar\n                        </Button>\n                      </div>\n                    );\n                  })()}\n                </div>\n              ) : (\n                <div>\n                  <p className=\"text-muted\">No hay registro para este día</p>\n                  <Button \n                    variant=\"outline-success\" \n                    size=\"sm\"\n                    onClick={() => handleDateClick(selectedDate)}\n                  >\n                    ➕ Registrar\n                  </Button>\n                </div>\n              )}\n            </div>\n          )}\n        </Col>\n      </Row>\n\n      <RealDayModal\n        show={showModal}\n        onHide={() => setShowModal(false)}\n        selectedDate={selectedDate}\n        existingDay={selectedDay}\n        onDayUpdated={handleDayUpdated}\n      />\n    </div>\n  );\n}\n\nexport default RealCalendarView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,QAAQ,iBAAiB;AACzD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,cAAc,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,gBAAgBA,CAAC;EAAEC,YAAY;EAAEC,YAAY;EAAEC;AAAiB,CAAC,EAAE;EAAAC,EAAA;EAC1E,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACdyB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,IAAI,GAAG,MAAMnB,cAAc,CAAC,CAAC;MACnCS,WAAW,CAACU,IAAI,CAAC;MACjBF,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAACL,KAAK,CAAC,0BAA0B,EAAEI,GAAG,CAAC;MAC9CH,QAAQ,CAAC,8BAA8B,GAAGG,GAAG,CAACE,OAAO,CAAC;IACxD,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,eAAe,GAAIC,IAAI,IAAK;IAChCpB,YAAY,CAACoB,IAAI,CAAC;IAClB,MAAMC,WAAW,GAAGjB,QAAQ,CAACkB,IAAI,CAACC,GAAG,IACnC,IAAIC,IAAI,CAACD,GAAG,CAACH,IAAI,CAAC,CAACK,YAAY,CAAC,CAAC,KAAKL,IAAI,CAACK,YAAY,CAAC,CAC1D,CAAC;IACDhB,cAAc,CAACY,WAAW,CAAC;IAC3Bd,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMmB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BZ,YAAY,CAAC,CAAC;IACdZ,gBAAgB,CAAC,CAAC;IAClBK,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMoB,cAAc,GAAGA,CAAC;IAAEP,IAAI;IAAEQ;EAAK,CAAC,KAAK;IACzC,IAAIA,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;IAEjC,MAAMC,OAAO,GAAGzB,QAAQ,CAACkB,IAAI,CAACC,GAAG,IAC/B,IAAIC,IAAI,CAACD,GAAG,CAACH,IAAI,CAAC,CAACK,YAAY,CAAC,CAAC,KAAKL,IAAI,CAACK,YAAY,CAAC,CAC1D,CAAC;IAED,IAAII,OAAO,EAAE;MACX,oBACE/B,OAAA;QAAKgC,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCjC,OAAA;UAAKgC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAAEF,OAAO,CAACG,KAAK,EAAC,GAAC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EACvDP,OAAO,CAACQ,SAAS,IAAIR,OAAO,CAACS,QAAQ,iBACpCxC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,GACxBF,OAAO,CAACQ,SAAS,EAAC,GAAC,EAACR,OAAO,CAACS,QAAQ;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAEV;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMG,gBAAgB,GAAGA,CAAC;IAAEnB,IAAI;IAAEQ;EAAK,CAAC,KAAK;IAC3C,IAAIA,IAAI,KAAK,OAAO,EAAE,OAAO,EAAE;IAE/B,MAAMC,OAAO,GAAGzB,QAAQ,CAACkB,IAAI,CAACC,GAAG,IAC/B,IAAIC,IAAI,CAACD,GAAG,CAACH,IAAI,CAAC,CAACK,YAAY,CAAC,CAAC,KAAKL,IAAI,CAACK,YAAY,CAAC,CAC1D,CAAC;IAED,IAAII,OAAO,EAAE;MACX,OAAO,eAAe;IACxB;IACA,OAAO,EAAE;EACX,CAAC;EAED,MAAMW,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAOpC,QAAQ,CAACqC,MAAM,CAAC,CAACC,KAAK,EAAEnB,GAAG,KAAKmB,KAAK,IAAInB,GAAG,CAACS,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACrE,CAAC;EAED,MAAMW,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,YAAY,GAAG,IAAIpB,IAAI,CAAC,CAAC,CAACqB,QAAQ,CAAC,CAAC;IAC1C,MAAMC,WAAW,GAAG,IAAItB,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAC;IAE5C,OAAO3C,QAAQ,CACZ4C,MAAM,CAACzB,GAAG,IAAI;MACb,MAAM0B,OAAO,GAAG,IAAIzB,IAAI,CAACD,GAAG,CAACH,IAAI,CAAC;MAClC,OAAO6B,OAAO,CAACJ,QAAQ,CAAC,CAAC,KAAKD,YAAY,IAAIK,OAAO,CAACF,WAAW,CAAC,CAAC,KAAKD,WAAW;IACrF,CAAC,CAAC,CACDL,MAAM,CAAC,CAACC,KAAK,EAAEnB,GAAG,KAAKmB,KAAK,IAAInB,GAAG,CAACS,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACxD,CAAC;EAED,oBACElC,OAAA;IAAAiC,QAAA,gBACEjC,OAAA,CAACR,GAAG;MAACwC,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBjC,OAAA,CAACP,GAAG;QAAAwC,QAAA,eACFjC,OAAA;UAAKgC,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChEjC,OAAA;YAAAiC,QAAA,EAAI;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnCtC,OAAA;YAAAiC,QAAA,eACEjC,OAAA,CAACN,MAAM;cACL0D,OAAO,EAAC,SAAS;cACjBC,OAAO,EAAEA,CAAA,KAAM;gBACb1C,cAAc,CAAC,IAAI,CAAC;gBACpBF,YAAY,CAAC,IAAI,CAAC;cACpB,CAAE;cAAAwB,QAAA,EACH;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELxB,KAAK,iBACJd,OAAA,CAACR,GAAG;MAACwC,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBjC,OAAA,CAACP,GAAG;QAAAwC,QAAA,eACFjC,OAAA,CAACL,KAAK;UAACyD,OAAO,EAAC,QAAQ;UAACE,OAAO,EAAEA,CAAA,KAAMvC,QAAQ,CAAC,EAAE,CAAE;UAACwC,WAAW;UAAAtB,QAAA,EAC7DnB;QAAK;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDtC,OAAA,CAACR,GAAG;MAACwC,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBjC,OAAA,CAACP,GAAG;QAAC+D,EAAE,EAAE,CAAE;QAAAvB,QAAA,eACTjC,OAAA;UAAKgC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCjC,OAAA,CAACJ,QAAQ;YACP6D,QAAQ,EAAEvD,YAAa;YACvBwD,KAAK,EAAEvD,YAAa;YACpBwD,UAAU,EAAEtC,eAAgB;YAC5BuC,WAAW,EAAE/B,cAAe;YAC5BgC,aAAa,EAAEpB,gBAAiB;YAChCqB,MAAM,EAAC;UAAO;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtC,OAAA,CAACP,GAAG;QAAC+D,EAAE,EAAE,CAAE;QAAAvB,QAAA,gBACTjC,OAAA;UAAKgC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BjC,OAAA;YAAAiC,QAAA,EAAI;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBtC,OAAA;YAAKgC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjC,OAAA;cAAAiC,QAAA,EAAQ;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAChC,QAAQ,CAACyD,MAAM;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNtC,OAAA;YAAKgC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjC,OAAA;cAAAiC,QAAA,EAAQ;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACI,aAAa,CAAC,CAAC,CAACsB,OAAO,CAAC,CAAC,CAAC,EAAC,GAC/D;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtC,OAAA;YAAKgC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjC,OAAA;cAAAiC,QAAA,EAAQ;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACO,oBAAoB,CAAC,CAAC,CAACmB,OAAO,CAAC,CAAC,CAAC,EAAC,GACtE;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtC,OAAA;YAAKgC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjC,OAAA;cAAAiC,QAAA,EAAQ;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAChC,QAAQ,CAACyD,MAAM,GAAG,CAAC,GAAG,CAACrB,aAAa,CAAC,CAAC,GAAGpC,QAAQ,CAACyD,MAAM,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,GAC/G;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELnC,YAAY,iBACXH,OAAA;UAAKgC,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCjC,OAAA;YAAAiC,QAAA,EAAI;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BtC,OAAA;YAAAiC,QAAA,EAAI9B,YAAY,CAAC8D,kBAAkB,CAAC,OAAO,EAAE;cAC3CC,OAAO,EAAE,MAAM;cACfC,IAAI,EAAE,SAAS;cACfC,KAAK,EAAE,MAAM;cACb3C,GAAG,EAAE;YACP,CAAC;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAENhC,QAAQ,CAACkB,IAAI,CAACC,GAAG,IAChB,IAAIC,IAAI,CAACD,GAAG,CAACH,IAAI,CAAC,CAACK,YAAY,CAAC,CAAC,KAAKxB,YAAY,CAACwB,YAAY,CAAC,CAClE,CAAC,gBACC3B,OAAA;YAAKgC,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzB,CAAC,MAAM;cACN,MAAMF,OAAO,GAAGzB,QAAQ,CAACkB,IAAI,CAACC,GAAG,IAC/B,IAAIC,IAAI,CAACD,GAAG,CAACH,IAAI,CAAC,CAACK,YAAY,CAAC,CAAC,KAAKxB,YAAY,CAACwB,YAAY,CAAC,CAClE,CAAC;cACD,oBACE3B,OAAA;gBAAAiC,QAAA,gBACEjC,OAAA;kBAAAiC,QAAA,gBAAGjC,OAAA;oBAAAiC,QAAA,EAAQ;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACP,OAAO,CAACG,KAAK,EAAC,GAAC;gBAAA;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,EAC9CP,OAAO,CAACQ,SAAS,iBAAIvC,OAAA;kBAAAiC,QAAA,gBAAGjC,OAAA;oBAAAiC,QAAA,EAAQ;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACP,OAAO,CAACQ,SAAS;gBAAA;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACzEP,OAAO,CAACS,QAAQ,iBAAIxC,OAAA;kBAAAiC,QAAA,gBAAGjC,OAAA;oBAAAiC,QAAA,EAAQ;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACP,OAAO,CAACS,QAAQ;gBAAA;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACtEP,OAAO,CAACsC,WAAW,iBAAIrE,OAAA;kBAAAiC,QAAA,gBAAGjC,OAAA;oBAAAiC,QAAA,EAAQ;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACP,OAAO,CAACsC,WAAW;gBAAA;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClFtC,OAAA,CAACN,MAAM;kBACL0D,OAAO,EAAC,iBAAiB;kBACzBkB,IAAI,EAAC,IAAI;kBACTjB,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAAClB,YAAY,CAAE;kBAAA8B,QAAA,EAC9C;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAEV,CAAC,EAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,gBAENtC,OAAA;YAAAiC,QAAA,gBACEjC,OAAA;cAAGgC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3DtC,OAAA,CAACN,MAAM;cACL0D,OAAO,EAAC,iBAAiB;cACzBkB,IAAI,EAAC,IAAI;cACTjB,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAAClB,YAAY,CAAE;cAAA8B,QAAA,EAC9C;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtC,OAAA,CAACH,YAAY;MACX0E,IAAI,EAAE/D,SAAU;MAChBgE,MAAM,EAAEA,CAAA,KAAM/D,YAAY,CAAC,KAAK,CAAE;MAClCN,YAAY,EAAEA,YAAa;MAC3BoB,WAAW,EAAEb,WAAY;MACzB+D,YAAY,EAAE7C;IAAiB;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACjC,EAAA,CArNQJ,gBAAgB;AAAAyE,EAAA,GAAhBzE,gBAAgB;AAuNzB,eAAeA,gBAAgB;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}