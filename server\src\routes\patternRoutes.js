const express = require('express');
const patternController = require('../controllers/patternController');
const router = express.Router();

router.get('/', patternController.getAllPatterns);
router.get('/:id', patternController.getPatternById);
router.post('/', patternController.createPattern);
router.put('/:id', patternController.updatePattern);
router.delete('/:id', patternController.deletePattern);
router.post('/:id/copy', patternController.copyPattern);

module.exports = router;